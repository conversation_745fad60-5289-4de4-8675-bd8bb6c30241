import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { v4 as uuidv4 } from 'uuid';

export interface MemoryStats {
    usedSize: number;
    fileCount: number;
    availableSpace: number;
    totalSpace: number;
}

export interface FileMetadata {
    size: number;
    createdAt: number;
    lastAccessed: number;
    category?: string;
    sessionId?: string;
}

export class StorageManager {
    private static instance: StorageManager;
    private readonly TEMP_DIR: string;
    private readonly PERSISTENT_DIR: string;
    private readonly MAX_SESSION_IMAGES: number = 50;
    private readonly MAX_IMAGE_SIZE: number = 5 * 1024 * 1024; // 5MB
    private readonly MAX_STORAGE_SIZE: number = 100 * 1024 * 1024; // 100MB

    // Cache để giảm truy cập FileSystem
    private _metadataCache: Map<string, FileMetadata> = new Map();
    private _lastCacheUpdate: number = 0;
    private _cacheValid: boolean = false;

    private constructor() {
        this.TEMP_DIR = `${FileSystem.cacheDirectory}scan_temp/`;
        this.PERSISTENT_DIR = `${FileSystem.documentDirectory}scans/`;
        this.initializeDirectories();
    }

    static getInstance(): StorageManager {
        if (!StorageManager.instance) {
            StorageManager.instance = new StorageManager();
        }
        return StorageManager.instance;
    }

    private async initializeDirectories() {
        try {
            // Đảm bảo các thư mục tồn tại
            const tempDirInfo = await FileSystem.getInfoAsync(this.TEMP_DIR);
            if (!tempDirInfo.exists) {
                await FileSystem.makeDirectoryAsync(this.TEMP_DIR, { intermediates: true });
            }

            const persistentDirInfo = await FileSystem.getInfoAsync(this.PERSISTENT_DIR);
            if (!persistentDirInfo.exists) {
                await FileSystem.makeDirectoryAsync(this.PERSISTENT_DIR, { intermediates: true });
            }
        } catch (error) {
            console.error('Error initializing directories:', error);
        }
    }

    /**
     * Lấy thống kê về bộ nhớ đã sử dụng
     */
    async getMemoryStats(): Promise<MemoryStats> {
        await this.updateMetadataCache();

        let usedSize = 0;
        let fileCount = 0;

        for (const metadata of this._metadataCache.values()) {
            usedSize += metadata.size;
            fileCount++;
        }

        const availableSpace = await this.getAvailableSpace();
        const totalSpace = Platform.OS === 'ios' ? 1024 * 1024 * 1024 : availableSpace + usedSize; // 1GB for iOS

        return {
            usedSize,
            fileCount,
            availableSpace,
            totalSpace
        };
    }

    /**
     * Kiểm tra xem có đủ bộ nhớ cho phiên quét mới
     */
    async checkMemoryLimit(estimatedSize: number = this.MAX_IMAGE_SIZE): Promise<boolean> {
        const stats = await this.getMemoryStats();
        const isConnected = await NetInfo.fetch().then(state => state.isConnected);

        // Nếu có kết nối internet, cho phép sử dụng nhiều bộ nhớ hơn vì có thể tải lên server
        const threshold = isConnected ? this.MAX_STORAGE_SIZE * 0.9 : this.MAX_STORAGE_SIZE * 0.7;

        // Kiểm tra xem dung lượng hiện tại + ước tính có vượt ngưỡng không
        const projectedUsage = stats.usedSize + estimatedSize;

        return projectedUsage < threshold;
    }

    /**
     * Kiểm tra và đảm bảo có đủ bộ nhớ cho phiên quét mới bằng cách dọn dẹp nếu cần
     */
    async ensureStorageAvailable(requiredSize: number = this.MAX_IMAGE_SIZE * 5): Promise<boolean> {
        const stats = await this.getMemoryStats();

        if (stats.availableSpace > requiredSize) {
            return true;
        }

        // Không đủ dung lượng, cần dọn dẹp
        await this.cleanupByAge(7); // Dọn file cũ hơn 7 ngày

        // Kiểm tra lại
        const newStats = await this.getMemoryStats();
        if (newStats.availableSpace > requiredSize) {
            return true;
        }

        // Vẫn không đủ, xóa các file ít dùng theo thứ tự
        await this.cleanupLeastUsed(requiredSize - newStats.availableSpace);

        return (await this.getMemoryStats()).availableSpace >= requiredSize;
    }

    /**
     * Xóa tệp cũ hơn số ngày chỉ định
     */
    async cleanupByAge(maxAgeDays: number = 7): Promise<number> {
        const maxAgeMs = maxAgeDays * 24 * 60 * 60 * 1000;
        const now = Date.now();
        let filesRemoved = 0;

        await this.updateMetadataCache();

        for (const [path, metadata] of this._metadataCache.entries()) {
            if (now - metadata.createdAt > maxAgeMs) {
                try {
                    await FileSystem.deleteAsync(path, { idempotent: true });
                    this._metadataCache.delete(path);
                    filesRemoved++;
                } catch (error) {
                    console.warn(`Could not delete file: ${path}`, error);
                }
            }
        }

        return filesRemoved;
    }

    /**
     * Xóa các tệp ít dùng nhất để giải phóng không gian
     */
    async cleanupLeastUsed(requiredSpace: number): Promise<number> {
        await this.updateMetadataCache();

        // Sắp xếp files theo thời gian truy cập gần nhất
        const sortedFiles = Array.from(this._metadataCache.entries())
            .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

        let spaceFreed = 0;
        let filesRemoved = 0;

        for (const [path, metadata] of sortedFiles) {
            if (spaceFreed >= requiredSpace) {
                break;
            }

            try {
                await FileSystem.deleteAsync(path, { idempotent: true });
                spaceFreed += metadata.size;
                this._metadataCache.delete(path);
                filesRemoved++;
            } catch (error) {
                console.warn(`Could not delete file: ${path}`, error);
            }
        }

        return filesRemoved;
    }

    /**
     * Cập nhật cache metadata từ filesystem
     */
    public async updateMetadataCache(force: boolean = false): Promise<void> {
        const now = Date.now();

        // Chỉ cập nhật cache nếu đã hết hạn hoặc bị buộc cập nhật
        if (this._cacheValid && !force && now - this._lastCacheUpdate < 60000) {
            return;
        }

        try {
            // Đảm bảo thư mục tồn tại trước khi đọc
            await this.initializeDirectories();

            // Làm mới cache
            this._metadataCache.clear();

            // Đọc thư mục tạm
            try {
                const tempFiles = await FileSystem.readDirectoryAsync(this.TEMP_DIR);
                for (const file of tempFiles) {
                    const path = `${this.TEMP_DIR}${file}`;
                    const info = await FileSystem.getInfoAsync(path);

                    if (info.exists) {
                        this._metadataCache.set(path, {
                            size: info.size || 0,
                            createdAt: info.modificationTime || now,
                            lastAccessed: info.modificationTime || now,
                            category: 'temp'
                        });
                    }
                }
            } catch (error) {
                console.error(`Error reading temp directory: ${this.TEMP_DIR}`, error);
                // Thử tạo lại thư mục nếu không có
                await this.initializeDirectories();
            }

            // Đọc thư mục bền vững
            try {
                const persistentFiles = await FileSystem.readDirectoryAsync(this.PERSISTENT_DIR);
                for (const file of persistentFiles) {
                    const path = `${this.PERSISTENT_DIR}${file}`;
                    const info = await FileSystem.getInfoAsync(path);

                    if (info.exists) {
                        this._metadataCache.set(path, {
                            size: info.size || 0,
                            createdAt: info.modificationTime || now,
                            lastAccessed: info.modificationTime || now,
                            category: 'persistent'
                        });
                    }
                }
            } catch (error) {
                console.error(`Error reading persistent directory: ${this.PERSISTENT_DIR}`, error);
                // Thử tạo lại thư mục nếu không có
                await this.initializeDirectories();
            }

            this._lastCacheUpdate = now;
            this._cacheValid = true;
        } catch (error) {
            console.error('Error updating metadata cache:', error);
        }
    }

    /**
     * Lấy không gian có sẵn
     */
    private async getAvailableSpace(): Promise<number> {
        // Không có API nào trong Expo để lấy thông tin không gian lưu trữ có sẵn
        // Nếu là iOS, dùng giá trị giả định
        if (Platform.OS === 'ios') {
            return 1024 * 1024 * 1024; // 1GB
        }

        // Cho Android, đây chỉ là một ước tính
        const stats = await this.getMemoryStats();
        return this.MAX_STORAGE_SIZE - stats.usedSize;
    }

    /**
     * Lưu file với metadata
     */
    async saveFile(uri: string, options: {
        permanent?: boolean;
        metadata?: Partial<FileMetadata>;
    } = {}): Promise<string> {
        const { permanent = false, metadata = {} } = options;
        const fileName = `${Date.now()}_${uuidv4()}.jpg`;
        const targetDir = permanent ? this.PERSISTENT_DIR : this.TEMP_DIR;
        const targetPath = `${targetDir}${fileName}`;

        try {
            await FileSystem.copyAsync({
                from: uri,
                to: targetPath
            });

            const fileInfo = await FileSystem.getInfoAsync(targetPath);

            if (fileInfo.exists) {
                const now = Date.now();

                // Cập nhật cache
                this._metadataCache.set(targetPath, {
                    size: fileInfo.size || 0,
                    createdAt: now,
                    lastAccessed: now,
                    category: permanent ? 'persistent' : 'temp',
                    ...metadata
                });

                this._cacheValid = true;
            }

            return targetPath;
        } catch (error) {
            console.error('Error saving file:', error);
            throw error;
        }
    }

    /**
     * Cập nhật thời gian truy cập gần nhất của file
     */
    async updateLastAccessed(path: string): Promise<void> {
        if (this._metadataCache.has(path)) {
            const metadata = this._metadataCache.get(path)!;
            metadata.lastAccessed = Date.now();
            this._metadataCache.set(path, metadata);
        }
    }

    /**
     * Dọn dẹp tất cả các file tạm
     */
    async cleanupTempFiles(): Promise<number> {
        try {
            const tempFiles = await FileSystem.readDirectoryAsync(this.TEMP_DIR);
            let deletedCount = 0;

            for (const file of tempFiles) {
                const path = `${this.TEMP_DIR}${file}`;
                try {
                    await FileSystem.deleteAsync(path, { idempotent: true });
                    this._metadataCache.delete(path);
                    deletedCount++;
                } catch (err) {
                    console.warn('Could not delete temp file:', path, err);
                }
            }

            return deletedCount;
        } catch (error) {
            console.error('Error cleaning temp files:', error);
            return 0;
        }
    }
} 