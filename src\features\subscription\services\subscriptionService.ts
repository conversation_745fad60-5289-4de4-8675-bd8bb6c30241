import axios from 'axios';
import { API_CONFIG } from '@core/api/config';

// Types for subscription data
export interface SubscriptionPackage {
  id: string;
  name: string;
  price: string;
  period: string;
  features: string[];
  color: string;
  popular: boolean;
}

export interface ActiveSubscription {
  id: string;
  startDate: string;
  endDate: string;
  autoRenew: boolean;
  paymentMethod: string;
}

export interface SubscriptionResponse {
  packages: SubscriptionPackage[];
  activeSubscription: ActiveSubscription | null;
}

// Subscription service
export const subscriptionService = {
  /**
   * Fetch subscription packages and active subscription data
   */
  getSubscriptions: async (): Promise<SubscriptionResponse> => {
    try {
      // First try to fetch from real API
      const response = await axios.get(`${API_CONFIG.BASE_URL}/subscriptions`, {
        timeout: 5000, // 5 seconds timeout
      });

      return response.data;
    } catch (error) {
      console.log('Error fetching subscription data, using fallback data', error);
      
      // Return mock data as fallback
      return {
        packages: [
          {
            id: 'basic',
            name: '<PERSON><PERSON> bản',
            price: '49.000',
            period: 'tháng',
            features: [
              '100 tài liệu/tháng',
              'OCR cơ bản',
              'Lưu trữ đám mây 1GB',
              'Xuất PDF, JPEG'
            ],
            color: '#4F6CFF',
            popular: false,
          },
          {
            id: 'premium',
            name: 'Cao cấp',
            price: '99.000',
            period: 'tháng',
            features: [
              'Không giới hạn tài liệu',
              'OCR nâng cao',
              'Lưu trữ đám mây 5GB',
              'Xuất tất cả định dạng',
              'Hỗ trợ ưu tiên'
            ],
            color: '#FF6B6B',
            popular: true,
          },
          {
            id: 'business',
            name: 'Doanh nghiệp',
            price: '199.000',
            period: 'tháng',
            features: [
              'Không giới hạn tài liệu',
              'OCR nâng cao',
              'Lưu trữ đám mây 20GB',
              'Xuất tất cả định dạng',
              'Quản lý người dùng',
              'Hỗ trợ 24/7'
            ],
            color: '#38D9A9',
            popular: false,
          }
        ],
        activeSubscription: {
          id: 'premium',
          startDate: '01/06/2023',
          endDate: '01/06/2024',
          autoRenew: true,
          paymentMethod: 'Visa •••• 4242',
        }
      };
    }
  },

  /**
   * Update subscription auto-renewal setting
   */
  updateAutoRenew: async (subscriptionId: string, autoRenew: boolean): Promise<boolean> => {
    try {
      await axios.put(`${API_CONFIG.BASE_URL}/subscriptions/${subscriptionId}/auto-renew`, {
        autoRenew
      });
      return true;
    } catch (error) {
      console.error('Error updating auto-renew setting:', error);
      return false;
    }
  },

  /**
   * Cancel subscription
   */
  cancelSubscription: async (subscriptionId: string): Promise<boolean> => {
    try {
      await axios.post(`${API_CONFIG.BASE_URL}/subscriptions/${subscriptionId}/cancel`);
      return true;
    } catch (error) {
      console.error('Error canceling subscription:', error);
      return false;
    }
  },

  /**
   * Subscribe to a new package
   */
  subscribe: async (packageId: string, paymentMethodId: string): Promise<{success: boolean, message?: string}> => {
    try {
      const response = await axios.post(`${API_CONFIG.BASE_URL}/subscriptions`, {
        packageId,
        paymentMethodId
      });
      
      return {
        success: true,
        message: 'Đăng ký gói dịch vụ thành công'
      };
    } catch (error: any) {
      console.error('Error subscribing to package:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Đăng ký thất bại. Vui lòng thử lại sau.'
      };
    }
  }
}; 