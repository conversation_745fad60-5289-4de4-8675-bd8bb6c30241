import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useScanSessionRepository } from '@core/hooks/useScanSessionRepository';
import { ScanSessionRepository } from '@core/database/repositories/scanSessionRepository';

export default function SQLiteTestScreen() {
  const [sessions, setSessions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { getAllSessions, addSession, deleteSession, deleteOldSessions } =
    useScanSessionRepository();

  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    try {
      setIsLoading(true);
      const data = await getAllSessions();
      setSessions(data);
    } catch (error) {
      console.error('Error loading sessions:', error);
      Alert.alert('Lỗi', 'Không thể tải dữ liệu từ database');
    } finally {
      setIsLoading(false);
    }
  };

  const createTestSession = async () => {
    try {
      const testSession = {
        title: `Test Session ${new Date().toLocaleString()}`,
        images: ScanSessionRepository.imagesToString([
          'file://test1.jpg',
          'file://test2.jpg',
        ]),
        siteName: 'Test App',
        isSent: false,
        size: 1024 * 1024, // 1MB
        userId: undefined,
      };

      await addSession(testSession);
      Alert.alert('Thành công', 'Đã tạo session test');
      loadSessions(); // Reload data
    } catch (error) {
      console.error('Error creating test session:', error);
      Alert.alert('Lỗi', 'Không thể tạo session test');
    }
  };

  const deleteAllSessions = async () => {
    Alert.alert('Xác nhận', 'Bạn có chắc chắn muốn xóa tất cả sessions?', [
      { text: 'Hủy', style: 'cancel' },
      {
        text: 'Xóa',
        style: 'destructive',
        onPress: async () => {
          try {
            for (const session of sessions) {
              await deleteSession(session.id);
            }
            Alert.alert('Thành công', 'Đã xóa tất cả sessions');
            loadSessions();
          } catch (error) {
            console.error('Error deleting sessions:', error);
            Alert.alert('Lỗi', 'Không thể xóa sessions');
          }
        },
      },
    ]);
  };

  const cleanupOldSessions = async () => {
    try {
      const deletedCount = await deleteOldSessions(7);
      Alert.alert('Thành công', `Đã xóa ${deletedCount} sessions cũ`);
      loadSessions();
    } catch (error) {
      console.error('Error cleaning up old sessions:', error);
      Alert.alert('Lỗi', 'Không thể dọn dẹp sessions cũ');
    }
  };

  const renderSession = (session: any) => (
    <View key={session.id} style={styles.sessionCard}>
      <View style={styles.sessionHeader}>
        <Text style={styles.sessionTitle}>{session.title}</Text>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => {
            Alert.alert('Xác nhận', 'Xóa session này?', [
              { text: 'Hủy', style: 'cancel' },
              {
                text: 'Xóa',
                style: 'destructive',
                onPress: async () => {
                  try {
                    await deleteSession(session.id);
                    loadSessions();
                  } catch (error) {
                    Alert.alert('Lỗi', 'Không thể xóa session');
                  }
                },
              },
            ]);
          }}
        >
          <Ionicons name='trash-outline' size={20} color='#ff4757' />
        </TouchableOpacity>
      </View>

      <Text style={styles.sessionInfo}>
        <Text style={styles.label}>ID: </Text>
        {session.id}
      </Text>

      <Text style={styles.sessionInfo}>
        <Text style={styles.label}>Ngày tạo: </Text>
        {new Date(session.createdAt).toLocaleString()}
      </Text>

      <Text style={styles.sessionInfo}>
        <Text style={styles.label}>Kích thước: </Text>
        {(session.size / 1024 / 1024).toFixed(2)} MB
      </Text>

      <Text style={styles.sessionInfo}>
        <Text style={styles.label}>Trạng thái: </Text>
        {session.isSent ? 'Đã gửi' : 'Chưa gửi'}
      </Text>

      <Text style={styles.sessionInfo}>
        <Text style={styles.label}>Số ảnh: </Text>
        {ScanSessionRepository.stringToImages(session.images).length}
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>SQLite Test - Scan Sessions</Text>
      </View>

      <View style={styles.actions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={createTestSession}
        >
          <Ionicons name='add' size={20} color='#fff' />
          <Text style={styles.actionButtonText}>Tạo Test Session</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={loadSessions}>
          <Ionicons name='refresh' size={20} color='#fff' />
          <Text style={styles.actionButtonText}>Tải lại</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.dangerButton]}
          onPress={deleteAllSessions}
        >
          <Ionicons name='trash' size={20} color='#fff' />
          <Text style={styles.actionButtonText}>Xóa tất cả</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.warningButton]}
          onPress={cleanupOldSessions}
        >
          <Ionicons name='time' size={20} color='#fff' />
          <Text style={styles.actionButtonText}>Dọn dẹp cũ</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Sessions ({sessions.length})</Text>

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size='large' color='#4361ee' />
            <Text style={styles.loadingText}>Đang tải...</Text>
          </View>
        ) : sessions.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name='document-outline' size={64} color='#ccc' />
            <Text style={styles.emptyText}>Không có sessions nào</Text>
            <Text style={styles.emptySubText}>
              Tạo session test để kiểm tra SQLite
            </Text>
          </View>
        ) : (
          <ScrollView style={styles.sessionsList}>
            {sessions.map(renderSession)}
          </ScrollView>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  header: {
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  actions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4361ee',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  dangerButton: {
    backgroundColor: '#ff4757',
  },
  warningButton: {
    backgroundColor: '#ffa502',
  },
  actionButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
  },
  emptySubText: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
  sessionsList: {
    flex: 1,
  },
  sessionCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sessionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sessionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  deleteButton: {
    padding: 8,
  },
  sessionInfo: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  label: {
    fontWeight: '600',
    color: '#333',
  },
});
