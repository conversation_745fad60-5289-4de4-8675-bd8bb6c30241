import i18next from 'i18next';
import { getLocales } from 'expo-localization';
import { initReactI18next } from 'react-i18next';
import enHome from '@core/translations/locales/en/home.json';
import enProfile from '@core/translations/locales/en/profile.json';
import enSignIn from '@core/translations/locales/en/sign-in.json';

import deHome from '@core/translations/locales/de/home.json';
import deProfile from '@core/translations/locales/de/profile.json';
import deSignIn from '@core/translations/locales/de/sign-in.json';

import vnHome from '@core/translations/locales/vn/home.json';
import vnProfile from '@core/translations/locales/vn/profile.json';
import vnSignIn from '@core/translations/locales/vn/sign-in.json';
import { reduxStorage } from '@core/store/storage';

// the translations
const resources: any = {
  en: {
    home: enHome,
    profile: enProfile,
    signIn: enSignIn,
  },
  de: {
    home: deHome,
    profile: deProfile,
    signIn: deSignIn,
  },
  vn: {
    home: vnHome,
    profile: vnProfile,
    signIn: vnSignIn,
  },
} as const;
// This is for situations where the user can change the language in the app.
const rootStorage: string = reduxStorage.getItem('persist:root')?.[
  '_j'
] as string;
let lng: string | null = null;

try {
  lng = rootStorage ? JSON.parse(JSON.parse(rootStorage).app).language : null;
} catch (e) {
  console.error(e);
}

// Generally, we should use the locale language as the default language.
const localeLng = getLocales()[0].languageCode as string;
const isLocaleLngSupported = resources?.[localeLng];

const defaultLocale = 'vn';
export const currentLanguage = i18next.language || defaultLocale;

i18next.use(initReactI18next).init({
  fallbackLng: 'en',
  resources,
  lng: lng ? lng : isLocaleLngSupported ? localeLng : defaultLocale,

  keySeparator: false,

  interpolation: {
    escapeValue: false,
  },
});

const t = i18next.t.bind(i18next);

export const getTranslation = (key: string, options?: any) => {
  return t(key, options);
};

export { t };
