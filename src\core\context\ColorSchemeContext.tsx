import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useColorScheme as useDeviceColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

type ColorScheme = 'light' | 'dark' | 'system';

interface ColorSchemeContextType {
  colorScheme: ColorScheme;
  currentTheme: 'light' | 'dark';
  setColorScheme: (scheme: ColorScheme) => void;
}

const ColorSchemeContext = createContext<ColorSchemeContextType | null>(null);

export const useColorScheme = () => {
  const context = useContext(ColorSchemeContext);
  if (!context) {
    throw new Error('useColorScheme must be used within a ColorSchemeProvider');
  }
  return context;
};

interface ColorSchemeProviderProps {
  children: ReactNode;
}

const STORAGE_KEY = '@color_scheme';

export const ColorSchemeProvider: React.FC<ColorSchemeProviderProps> = ({ children }) => {
  const deviceColorScheme = useDeviceColorScheme();
  const [colorScheme, setScheme] = useState<ColorScheme>('system');
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    const loadColorScheme = async () => {
      try {
        const savedScheme = await AsyncStorage.getItem(STORAGE_KEY);
        if (savedScheme) {
          setScheme(savedScheme as ColorScheme);
        }
      } catch (error) {
        console.error('Failed to load color scheme:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadColorScheme();
  }, []);

  const setColorScheme = async (newScheme: ColorScheme) => {
    setScheme(newScheme);
    try {
      await AsyncStorage.setItem(STORAGE_KEY, newScheme);
    } catch (error) {
      console.error('Failed to save color scheme:', error);
    }
  };

  // Determine the actual theme based on the selected scheme
  const currentTheme = colorScheme === 'system' 
    ? deviceColorScheme || 'light' 
    : colorScheme;

  const value = {
    colorScheme,
    currentTheme,
    setColorScheme
  };

  // Don't render until we've loaded from storage
  if (isLoading) {
    return null;
  }

  return (
    <ColorSchemeContext.Provider value={value}>
      {children}
    </ColorSchemeContext.Provider>
  );
}; 