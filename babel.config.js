module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      [
        'module:react-native-dotenv',
        {
          moduleName: '@env',
          path: '.env',
          blocklist: null,
          allowlist: null,
          safe: false,
          allowUndefined: true,
        },
      ],
      'react-native-reanimated/plugin',
      [
        'module-resolver',
        {
          root: ['./src'],
          extensions: ['.ios.js', '.android.js', '.js', '.ts', '.tsx', '.json'],
          alias: {
            '@': './src',
            '@components': './src/shared/components',
            '@atoms': './src/shared/components/atoms',
            '@molecules': './src/shared/components/molecules',
            '@organisms': './src/shared/components/organisms',
            '@hooks': './src/shared/hooks',
            '@utils': './src/shared/utils',
            '@types': './src/shared/types',
            '@features': './src/features',
            '@core': './src/core',
            '@i18n': './src/i18n',
            '@assets': './src/assets'
          }
        }
      ]
    ],
  };
};
