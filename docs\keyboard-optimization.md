# Tối ưu bàn phím trong ứng dụng

## Tổng quan

Tài liệu này mô tả các tối ưu bàn phím đã được triển khai trong ứng dụng để cải thiện trải nghiệm người dùng khi nhập dữ liệu.

## Các tính năng đã triển khai

### 1. Hook useKeyboardDismiss

**File:** `src/shared/hooks/useKeyboardDismiss.ts`

Hook này cung cấp các chức năng để ẩn bàn phím:
- `dismissKeyboard()`: Ẩn bàn phím
- `createTouchableProps()`: Tạo props cho TouchableOpacity để ẩn bàn phím khi tap

### 2. SmartKeyboardView Component ⭐ **MỚI**

**File:** `src/shared/components/molecules/SmartKeyboardView.tsx`

Component thông minh để xử lý keyboard với animation mượt mà:
- Animation mượt mà khi keyboard xuất hiện/ẩn
- Không sử dụng ScrollView để tránh UI xấu
- Touch-to-dismiss keyboard
- Tương thích với cả iOS và Android

**Props:**
- `behavior`: Hành vi keyboard avoiding (default: 'padding' cho iOS, 'height' cho Android)
- `keyboardVerticalOffset`: Offset cho keyboard (default: 0 cho iOS, 20 cho Android)
- `enableTouchToDismiss`: Cho phép tap để ẩn bàn phím (default: true)
- `animationDuration`: Thời gian animation (default: 250ms)

### 3. KeyboardAvoidingWrapper Component

**File:** `src/shared/components/molecules/KeyboardAvoidingWrapper.tsx`

Component wrapper cơ bản (được cải tiến):
- Mặc định tắt scroll để UI đẹp hơn (scrollEnabled = false)
- Chỉ flex khi keyboard không hiện
- Tắt bounce và over scroll
- Hỗ trợ contentContainerStyle

### 4. OptimizedSearchInput Component ⭐ **MỚI**

**File:** `src/shared/components/molecules/OptimizedSearchInput.tsx`

Component search input được tối ưu hoàn toàn:
- Tích hợp sẵn search icon và clear button
- Keyboard optimizations tự động
- Cross-platform clear button
- Styling đẹp và responsive

### 5. Input Component Optimization

**File:** `src/shared/components/atoms/Input/Input.tsx`

Đã thêm các props tối ưu bàn phím:
- `returnKeyType`: Loại nút return (default: 'done')
- `onSubmitEditing`: Callback khi nhấn return
- `blurOnSubmit`: Ẩn bàn phím khi submit (default: true)
- `autoCapitalize`: Tự động viết hoa (default: 'none')
- `keyboardType`: Loại bàn phím

## Các màn hình đã được tối ưu

### 1. SignInScreen ⭐ **CẢI TIẾN**

**Tối ưu:**
- Sử dụng SmartKeyboardView thay vì KeyboardAvoidingWrapper
- Animation mượt mà khi keyboard xuất hiện
- Username field: returnKeyType="next", focus vào password khi nhấn next
- Password field: returnKeyType="done", submit form khi nhấn done
- Touch-to-dismiss keyboard
- Auto-correct và auto-capitalize tắt

### 2. DocumentsHistoryScreen

**Tối ưu:**
- Search input: returnKeyType="search"
- Auto-correct và auto-capitalize tắt
- Clear button mode

### 3. AiModelsScreen

**Tối ưu:**
- Search input: returnKeyType="search"
- Auto-correct và auto-capitalize tắt
- Clear button mode

### 4. WebSocketTestScreen

**Tối ưu:**
- Message input: returnKeyType="done"
- Auto-correct và auto-capitalize tắt

### 5. WebviewScreen

**Tối ưu:**
- Modal input: returnKeyType="done"
- Auto-correct và auto-capitalize tắt
- Clear button mode

## Hướng dẫn sử dụng

### Sử dụng SmartKeyboardView (Khuyến nghị) ⭐

```tsx
import { SmartKeyboardView } from '@/shared/components/molecules/SmartKeyboardView';

function MyScreen() {
  return (
    <SmartKeyboardView
      enableTouchToDismiss={true}
      animationDuration={250}
    >
      {/* Nội dung màn hình */}
    </SmartKeyboardView>
  );
}
```

### Sử dụng OptimizedSearchInput ⭐

```tsx
import { OptimizedSearchInput } from '@/shared/components/molecules/OptimizedSearchInput';

function MyScreen() {
  const [searchText, setSearchText] = useState('');

  return (
    <OptimizedSearchInput
      value={searchText}
      onChangeText={setSearchText}
      placeholder="Tìm kiếm..."
      onSubmitEditing={() => console.log('Search:', searchText)}
      autoFocus={true}
    />
  );
}
```

### Sử dụng KeyboardAvoidingWrapper (Cơ bản)

```tsx
import { KeyboardAvoidingWrapper } from '@/shared/components/molecules/KeyboardAvoidingWrapper';

function MyScreen() {
  return (
    <KeyboardAvoidingWrapper
      scrollEnabled={false} // Mặc định false để UI đẹp hơn
      enableTouchToDismiss={true}
    >
      {/* Nội dung màn hình */}
    </KeyboardAvoidingWrapper>
  );
}
```

### Sử dụng Input component với tối ưu

```tsx
import { Input } from '@/shared/components/atoms/Input/Input';

function MyForm() {
  return (
    <>
      <Input
        placeholder="Username"
        returnKeyType="next"
        onSubmitEditing={() => passwordRef.current?.focus()}
        autoCapitalize="none"
        autoCorrect={false}
      />
      <Input
        ref={passwordRef}
        placeholder="Password"
        secureTextEntry
        returnKeyType="done"
        onSubmitEditing={handleSubmit}
        autoCapitalize="none"
        autoCorrect={false}
      />
    </>
  );
}
```

### Sử dụng useKeyboardDismiss hook

```tsx
import { useKeyboardDismiss } from '@/shared/hooks/useKeyboardDismiss';

function MyComponent() {
  const { dismissKeyboard, createTouchableProps } = useKeyboardDismiss();

  return (
    <TouchableOpacity {...createTouchableProps()}>
      {/* Nội dung */}
    </TouchableOpacity>
  );
}
```

## Best Practices

1. **Luôn sử dụng returnKeyType phù hợp:**
   - "next" cho field không phải cuối cùng
   - "done" cho field cuối cùng
   - "search" cho search input

2. **Tắt auto-correct và auto-capitalize cho:**
   - Username/email fields
   - Password fields
   - Search inputs
   - Technical inputs

3. **Sử dụng KeyboardAvoidingWrapper cho màn hình có form**

4. **Implement onSubmitEditing để chuyển focus hoặc submit form**

5. **Sử dụng clearButtonMode="while-editing" cho search inputs**

## Lợi ích

- **Trải nghiệm người dùng tốt hơn:** Navigation mượt mà giữa các input
- **Hiệu quả hơn:** Ít tap hơn để hoàn thành form
- **Tương thích đa nền tảng:** Hoạt động tốt trên cả iOS và Android
- **Accessibility:** Hỗ trợ tốt cho người dùng khuyết tật
- **Performance:** Tối ưu hiệu suất ứng dụng
