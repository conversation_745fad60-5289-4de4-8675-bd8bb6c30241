import { BaseStorage } from './baseStorage';
import { STORAGE_KEYS } from './constants';

/**
 * Type định nghĩa cấu trúc dự án lưu trữ
 * Phải phù hợp với interface DuAn từ features/duan
 */
export interface ProjectData {
    DuAnId: string;
    TenDuAn: string;
    MaDuAn: string;
    DonViId: string;
}

/**
 * Service quản lý lưu trữ dự án
 */
export class ProjectStorage extends BaseStorage {
    /**
     * Lưu dự án mặc định
     * @param projectId ID dự án mặc định
     */
    setDefaultProject(projectId: string): boolean {
        if (!projectId) return false;
        this.setString(STORAGE_KEYS.PROJECT.DEFAULT_ID, projectId);
        return true;
    }

    /**
     * Lấy ID dự án mặc định
     */
    getDefaultProject(): string | undefined {
        return this.getString(STORAGE_KEYS.PROJECT.DEFAULT_ID);
    }

    /**
     * X<PERSON>a dự án mặc định
     */
    clearDefaultProject(): void {
        this.deleteKey(STORAGE_KEYS.PROJECT.DEFAULT_ID);
    }

    /**
     * Lưu danh sách dự án đã sử dụng gần đây
     * @param projects Danh sách dự án
     */
    setRecentProjects(projects: ProjectData[]): void {
        if (!Array.isArray(projects)) {
            console.error('Provided projects is not an array');
            return;
        }
        this.setObject(STORAGE_KEYS.PROJECT.RECENT_LIST, projects);
    }

    /**
     * Lấy danh sách dự án đã sử dụng gần đây
     */
    getRecentProjects(): ProjectData[] {
        return this.getObject<ProjectData[]>(STORAGE_KEYS.PROJECT.RECENT_LIST) || [];
    }

    /**
     * Thêm dự án vào danh sách đã sử dụng gần đây
     * @param project Dự án cần thêm vào
     * @param maxItems Số lượng tối đa dự án lưu trữ
     */
    addRecentProject(project: ProjectData, maxItems: number = 3): ProjectData[] {
        if (!project || !project.DuAnId) return this.getRecentProjects();

        const recentProjects = this.getRecentProjects();

        // Lọc bỏ dự án hiện tại nếu đã có trong danh sách
        const filtered = recentProjects.filter(p => p.DuAnId !== project.DuAnId);

        // Thêm dự án mới vào đầu danh sách và giới hạn số lượng
        const updated = [project, ...filtered].slice(0, maxItems);

        // Lưu danh sách mới
        this.setRecentProjects(updated);

        return updated;
    }

    /**
     * Xóa toàn bộ dữ liệu dự án
     */
    clearAllProjectData(): void {
        this.deleteKey(STORAGE_KEYS.PROJECT.DEFAULT_ID);
        this.deleteKey(STORAGE_KEYS.PROJECT.RECENT_LIST);
    }
}

// Export singleton instance
export const projectStorage = new ProjectStorage(); 