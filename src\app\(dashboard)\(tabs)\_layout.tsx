// import { Tabs, useRouter } from 'expo-router';
// import { useTranslation } from 'react-i18next';
// import AntDesign from '@expo/vector-icons/AntDesign';
// import { TouchableOpacity, View, Text, Image } from 'react-native-ui-lib';
// import { FontAwesome6, Ionicons } from '@expo/vector-icons';
// import { useState } from 'react';

// import DocumentScanner, {
//   ScanDocumentResponse,
// } from 'react-native-document-scanner-plugin';
// import CustomScanButton from '@/src/components/UI/Button/CustomScanButton';
// import { useScan } from '@/src/context/ScanContext';

// export default function TabLayout() {
//   const { t } = useTranslation();
//   const [scannedImage, setScannedImage] = useState<string | null>(null);
//   const { addScannedImages } = useScan();
//   const router = useRouter();

// const startScan = async () => {
//   try {
//     const result: ScanDocumentResponse | undefined =
//       await DocumentScanner.scanDocument({ maxNumDocuments: 5 });

//     if (
//       result?.status === 'success' &&
//       Array.isArray(result.scannedImages) &&
//       result.scannedImages.length > 0
//     ) {
//       addScannedImages(result.scannedImages);
//       router.push('/scan');
//     }
//   } catch (error) {
//     console.error('Scan error:', error);
//   }
// };

//   return (
//     <>
//       <Tabs
//         screenOptions={{
//           headerShown: false,
//           tabBarStyle: {
//             bottom: 0,
//             left: 0,
//             right: 0,
//             height: 100,
//             alignItems: 'center',
//             backgroundColor: '#DDDBFF',
//             borderTopWidth: 1,
//             borderTopColor: '#eaeaea',
//           },
//         }}
//       >
//         <Tabs.Screen
//           name='home/index'
//           options={{
//             tabBarShowLabel: false,
//             tabBarIconStyle: {
//               position: 'absolute',
//               display: 'flex',
//               alignItems: 'center',
//               justifyContent: 'center',
//               height: 90,
//             },
//             tabBarIcon: ({ focused }) => (
//               <View style={{ alignItems: 'center', justifyContent: 'center' }}>
//                 <Ionicons
//                   name='file-tray-stacked-outline'
//                   size={24}
//                   color={focused ? '#2F4FCD' : 'black'}
//                 />
//               </View>
//             ),
//           }}
//         />

//         <Tabs.Screen
//           name='scan/index'
//           options={{
//             tabBarShowLabel: false,
//             tabBarButton: () => <CustomScanButton onPress={startScan} />,
//           }}
//         />

//         <Tabs.Screen
//           name='profile/index'
//           options={{
//             tabBarIconStyle: {
//               position: 'absolute',
//               display: 'flex',
//               alignItems: 'center',
//               justifyContent: 'center',
//               height: 90,
//             },
//             tabBarShowLabel: false,
//             tabBarIcon: ({ focused }) => (
//               <AntDesign
//                 name='setting'
//                 size={24}
//                 color={focused ? '#2F4FCD' : 'black'}
//               />
//             ),
//           }}
//         />
//       </Tabs>

//       {scannedImage && (
//         <Image
//           source={{ uri: scannedImage }}
//           resizeMode='contain'
//           style={{
//             position: 'absolute',
//             top: 120,
//             left: 0,
//             right: 0,
//             bottom: 0,
//             backgroundColor: '#fff',
//           }}
//         />
//       )}
//     </>
//   );
// }

import React from 'react';
import { View, Text, TouchableOpacity, Dimensions } from 'react-native';
import {
  CurvedBottomBar,
  CurvedBottomBarExpo,
} from 'react-native-curved-bottom-bar';
import { Ionicons, AntDesign, MaterialIcons } from '@expo/vector-icons';
import DocumentScanner, {
  ScanDocumentResponse,
} from 'react-native-document-scanner-plugin';
import { useRouter } from 'expo-router';
import LottieView from 'lottie-react-native';
import { useScan } from '@features/scan/context/ScanContext';
import { Colors } from 'react-native-ui-lib';
import { ROUTES } from '@core/constants/routes';
import Home from './home';
import Profile from './profile';
import Documents from './documents';
import Subscriptions from './subscriptions';

export default function TabLayout() {
  const router = useRouter();
  const { addScanSession } = useScan();
  const screenWidth = Dimensions.get('window').height;
  const iconSize = screenWidth * 0.027; // 5% width
  const renderTabBar = ({ routeName, selectedTab, navigate }: any) => {
    const color = routeName === selectedTab ? Colors.buttonPrimary : '#9999';
    let icon;
    if (routeName === 'home') {
      icon = <Ionicons name='home-outline' size={iconSize} color={color} />;
    } else if (routeName === 'documents') {
      icon = (
        <Ionicons name='document-text-outline' size={iconSize} color={color} />
      );
    } else if (routeName === 'subscriptions') {
      icon = <Ionicons name='bookmark-outline' size={iconSize} color={color} />;
    } else if (routeName === 'profile') {
      icon = (
        <AntDesign
          name='setting'
          style={{ padding: 1 }}
          size={iconSize}
          color={color}
        />
      );
    }
    return (
      <TouchableOpacity
        onPress={() => navigate(routeName)}
        style={{ alignItems: 'center', justifyContent: 'center' }}
      >
        {icon}
      </TouchableOpacity>
    );
  };

  const startScan = async () => {
    try {
      const result: ScanDocumentResponse | undefined =
        await DocumentScanner.scanDocument({ maxNumDocuments: 5 });
      if (
        result?.status === 'success' &&
        Array.isArray(result.scannedImages) &&
        result.scannedImages.length > 0
      ) {
        // Await addScanSession để đảm bảo currentSession được set trước khi navigate
        await addScanSession(result.scannedImages);
        router.push(ROUTES.SCAN);
      }
    } catch (error) {
      console.error('Scan error:', error);
    }
  };

  return (
    <CurvedBottomBarExpo.Navigator
      id='main-tabs'
      shadowStyle={{
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }}
      screenListeners={{}}
      defaultScreenOptions={{}}
      type='DOWN'
      style={{ backgroundColor: 'transparent' }}
      height={65}
      circleWidth={60}
      width={undefined}
      borderColor='transparent'
      borderWidth={0}
      circlePosition='CENTER'
      bgColor='white'
      initialRouteName='home'
      screenOptions={{ headerShown: false }}
      borderTopLeftRight
      backBehavior='none'
      renderCircle={() => (
        <TouchableOpacity
          onPress={() => {
            startScan();
          }}
          style={{
            width: 70,
            height: 70,
            borderRadius: 40,
            backgroundColor: Colors.buttonPrimary,
            justifyContent: 'center',
            alignItems: 'center',
            bottom: 28,
          }}
        >
          <LottieView
            autoPlay
            style={{
              width: 85,
              height: 85,
            }}
            source={require('@assets/lottie/scan_2.json')}
          />
        </TouchableOpacity>
      )}
      tabBar={renderTabBar}
    >
      <CurvedBottomBar.Screen
        header='false'
        name='home'
        position='LEFT'
        component={() => <Home />}
      />
      <CurvedBottomBar.Screen
        name='documents'
        position='LEFT'
        component={() => <Documents />}
      />
      <CurvedBottomBar.Screen
        name='subscriptions'
        position='RIGHT'
        component={() => <Subscriptions />}
      />
      <CurvedBottomBar.Screen
        name='profile'
        position='RIGHT'
        component={() => <Profile />}
      />
    </CurvedBottomBarExpo.Navigator>
  );
}
