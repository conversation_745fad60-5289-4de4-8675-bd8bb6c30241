import { API_BASE_URL } from '@env';

export const API_CONFIG = {
    // Hardcode base URL tạm thời cho testing
    //BASE_URL: API_BASE_URL,
    BASE_URL: API_BASE_URL,
    IMAGE_API_URL: 'http://api.nhattamsoft.vn:4546',
    ENDPOINTS: {
        IMAGE_EXTRACT: '/image_extract_multi',
        AUTH: {
            LOGIN: '/auth/login',
            REGISTER: '/auth/register',
            LOGOUT: '/auth/logout',
        },
        DUAN: {
            GET_ALL_DUAN: '/duan/get-list',
        }
        ,
        LOAIVANBAN: {
            GET_ALL_LOAIVANBAN: '/loaivanban/get-list',
        }
        ,
        USER: {
            PROFILE: '/user/profile',
            UPDATE: '/user/update',
        },
    },
    TIMEOUT: 30000,
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000,
    IMAGE_UPLOAD: {
        PARAMS: {
            loaiVanBan: '',
            duAnID: ''
        }
    }
};

export const API_ERROR_MESSAGES = {
    NETWORK_ERROR: 'Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.',
    TIMEOUT: 'Yêu cầu đã hết thời gian chờ. Vui lòng thử lại.',
    SERVER_ERROR: 'Đã xảy ra lỗi máy chủ. Vui lòng thử lại sau.',
    UNAUTHORIZED: 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.',
    FORBIDDEN: 'Bạn không có quyền thực hiện hành động này.',
    NOT_FOUND: 'Không tìm thấy tài nguyên yêu cầu.',
    VALIDATION_ERROR: 'Dữ liệu không hợp lệ. Vui lòng kiểm tra lại.',
    UNKNOWN_ERROR: 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.',
}; 