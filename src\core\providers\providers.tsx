import { ReactNode } from 'react';
import {
  DarkTheme,
  DefaultTheme,
  ThemeProvider,
} from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { Provider as ReduxProvider } from 'react-redux';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { QueryClientProvider } from '@tanstack/react-query';
import { LoaderProvider } from '@features/auth/context/LoaderContext';
import { SessionProvider } from '@features/auth/context/ctx';
import { queryClient } from '@core/api/queryClient';
import { store } from '@core/store';
import { ScanProvider } from '@features/scan/context/ScanContext';
import { TokenProvider } from '@features/tokens/tokenContext';
import {
  ColorSchemeProvider,
  useColorScheme,
} from '@core/context/ColorSchemeContext';

// Core providers (UI/UX)
const CoreProvidersWithTheme = ({ children }: { children: ReactNode }) => {
  const { currentTheme } = useColorScheme();

  return (
    <ThemeProvider value={currentTheme === 'dark' ? DarkTheme : DefaultTheme}>
      <SafeAreaProvider>
        <GestureHandlerRootView style={{ flex: 1 }}>
          <BottomSheetModalProvider>{children}</BottomSheetModalProvider>
        </GestureHandlerRootView>
      </SafeAreaProvider>
    </ThemeProvider>
  );
};

// Data providers (State management)
const DataProviders = ({ children }: { children: ReactNode }) => (
  <QueryClientProvider client={queryClient}>
    <ReduxProvider store={store}>{children}</ReduxProvider>
  </QueryClientProvider>
);

// Feature providers (Business logic)
const FeatureProviders = ({ children }: { children: ReactNode }) => (
  <LoaderProvider>
    <SessionProvider>
      <ScanProvider>
        <TokenProvider>{children}</TokenProvider>
      </ScanProvider>
    </SessionProvider>
  </LoaderProvider>
);

// Import here to avoid circular dependency
import { DatabaseProvider } from './DatabaseProvider';

const DatabaseProviders = ({ children }: { children: ReactNode }) => (
  <DatabaseProvider>{children}</DatabaseProvider>
);

const ProvidersWrapper = ({ children }: { children: ReactNode }) => {
  return (
    <ColorSchemeProvider>
      <CoreProvidersWithTheme>
        <DataProviders>
          <DatabaseProviders>
            <FeatureProviders>{children}</FeatureProviders>
          </DatabaseProviders>
        </DataProviders>
      </CoreProvidersWithTheme>
    </ColorSchemeProvider>
  );
};

export default ProvidersWrapper;
