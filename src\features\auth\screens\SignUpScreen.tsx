import { Link } from 'expo-router';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { View, Text, Button } from 'react-native-ui-lib';
import { ROUTES } from '@/core/constants/routes';

export const SignUpScreen = () => {
  return (
    <SafeAreaProvider>
      <View flex centerH centerV>
        <Text>Hello</Text>
        <View row>
          <Text>Bạn đã có tài khoản?</Text>
          <Link href={ROUTES.AUTH.SIGN_IN}>Đăng nhập</Link>
        </View>
      </View>
    </SafeAreaProvider>
  );
};
