import React from 'react';
import { View, TouchableOpacity } from 'react-native-ui-lib';
import { Ionicons } from '@expo/vector-icons';

type CalendarHeaderProps = {
  onToggleSidebar: () => void;
  showSidebar: boolean;
  onPressCalendar: () => void;
};

const CalendarHeader = ({
  onToggleSidebar,
  showSidebar,
  onPressCalendar,
}: CalendarHeaderProps) => {
  return (
    <View
      row
      spread
      centerV
      paddingH-16
      paddingT-80
      paddingB-12
      style={{ backgroundColor: '#FFFFFF' }}
    >
      <TouchableOpacity row style={{ gap: 10 }} onPress={onToggleSidebar}>
        <Ionicons
          name={!showSidebar ? 'menu' : 'close'}
          size={24}
          color='#000'
        />
        {!showSidebar && (
          <TouchableOpacity
            row
            centerV
            onPress={onPressCalendar}
            style={{ marginLeft: 12 }}
          >
            <Ionicons name='chevron-down' size={16} color='#000' />
          </TouchableOpacity>
        )}
      </TouchableOpacity>

      {!showSidebar && (
        <View row centerV>
          <TouchableOpacity onPress={() => console.log('Search')}>
            <Ionicons name='search' size={20} color='#000' />
          </TouchableOpacity>
          <TouchableOpacity onPress={onPressCalendar} marginL-16>
            <Ionicons name='calendar' size={20} color='#000' />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default CalendarHeader;
