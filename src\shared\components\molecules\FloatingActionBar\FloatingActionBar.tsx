import React, { useEffect, useRef } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Easing,
} from 'react-native';
import {
  Ionicons,
  Feather,
  MaterialCommunityIcons,
  AntDesign,
} from '@expo/vector-icons';

interface Props {
  onAdd: () => void;
  onShare: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onSave: () => void;
  onSend?: () => void;
  onSettings?: () => void;
  visible?: boolean;

  showAdd?: boolean;
  showShare?: boolean;
  showEdit?: boolean;
  showDelete?: boolean;
  showSave?: boolean;
  showSend?: boolean;
}

const FloatingActionBar: React.FC<Props> = ({
  onAdd,
  onShare,
  onEdit,
  onDelete,
  onSave,
  onSend,
  onSettings,
  visible = true,
  showEdit = true,
}) => {
  const translateY = useRef(new Animated.Value(100)).current; // start hidden

  useEffect(() => {
    Animated.timing(translateY, {
      toValue: visible ? 0 : 100,
      duration: 260,
      useNativeDriver: true,
      easing: Easing.out(Easing.quad),
    }).start();
  }, [visible]);

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateY }],
          opacity: translateY.interpolate({
            inputRange: [0, 100],
            outputRange: [1, 0],
          }),
        },
      ]}
    >
      <TouchableOpacity onPress={onAdd}>
        <Ionicons name='add-circle-outline' size={24} color='black' />
      </TouchableOpacity>
      <TouchableOpacity onPress={onShare}>
        <Feather name='share-2' size={24} color='black' />
      </TouchableOpacity>
      {showEdit && (
        <TouchableOpacity onPress={onEdit}>
          <Feather name='edit' size={24} color='black' />
        </TouchableOpacity>
      )}
      <TouchableOpacity onPress={onDelete}>
        <MaterialCommunityIcons
          name='trash-can-outline'
          size={24}
          color='black'
        />
      </TouchableOpacity>
      <TouchableOpacity onPress={onSave}>
        <Ionicons name='save-outline' size={24} color='black' />
      </TouchableOpacity>
      {onSend && (
        <TouchableOpacity onPress={onSend}>
          <Ionicons name='cloud-upload-outline' size={24} color='black' />
        </TouchableOpacity>
      )}
      <TouchableOpacity onPress={onSettings}>
        <Ionicons name='settings' size={24} color='black' />
      </TouchableOpacity>
    </Animated.View>
  );
};

export default FloatingActionBar;

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 32,
    alignSelf: 'center',
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
    gap: 24,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 12,
    shadowOffset: { width: 0, height: 4 },
    elevation: 4,
  },
});
