import React, { memo } from 'react';
import {
  Ionicons,
  <PERSON>ather,
  MaterialCommunityIcons,
  FontAwesome,
} from '@expo/vector-icons';
import { COLORS } from '@core/constants/theme';

export type IconType =
  | 'Ionicons'
  | 'Feather'
  | 'MaterialCommunityIcons'
  | 'FontAwesome';

interface IconProps {
  name: string;
  type?: IconType;
  size?: number;
  color?: string;
  style?: any;
}

const Icon: React.FC<IconProps> = memo(
  ({ name, type = 'Ionicons', size = 24, color = COLORS.text, style }) => {
    const props = {
      name: name as any,
      size,
      color,
      style,
    };

    switch (type) {
      case 'Ionicons':
        return <Ionicons {...props} />;
      case 'Feather':
        return <Feather {...props} />;
      case 'MaterialCommunityIcons':
        return <MaterialCommunityIcons {...props} />;
      case 'FontAwesome':
        return <FontAwesome {...props} />;
      default:
        return <Ionicons {...props} />;
    }
  },
);

export default Icon;
