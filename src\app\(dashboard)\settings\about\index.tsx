import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Linking,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import Constants from 'expo-constants';

const AboutScreen = () => {
  const router = useRouter();

  // App version from app.json or expo constants
  const appVersion = Constants.expoConfig?.version || '1.0.0';
  const buildNumber =
    Constants.expoConfig?.ios?.buildNumber ||
    Constants.expoConfig?.android?.versionCode ||
    '1';

  const handleEmailSupport = () => {
    Linking.openURL('mailto:<EMAIL>');
  };

  const handleOpenWebsite = () => {
    Linking.openURL('https://nhattamsoft.vn');
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name='arrow-back' size={24} color='#333' />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Về ứng dụng</Text>
        <View style={styles.rightPlaceholder} />
      </View>

      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          {/* Logo và tên ứng dụng */}
          <View style={styles.appInfoContainer}>
            <View style={styles.logoContainer}>
              <Ionicons name='document-text' size={60} color='#0F9D58' />
            </View>
            <Text style={styles.appName}>Scan Document</Text>
            <Text style={styles.appVersion}>
              Phiên bản {appVersion} ({buildNumber})
            </Text>
            <Text style={styles.copyright}>© 2023 NhatTamSoft</Text>
          </View>

          {/* Thông tin chi tiết */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Thông tin ứng dụng</Text>

            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Nhà phát triển</Text>
              <Text style={styles.infoValue}>NhatTamSoft</Text>
            </View>

            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Hệ điều hành</Text>
              <Text style={styles.infoValue}>
                {Platform.OS === 'ios' ? 'iOS' : 'Android'}
              </Text>
            </View>

            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Phiên bản OS</Text>
              <Text style={styles.infoValue}>{Platform.Version}</Text>
            </View>
          </View>

          {/* Liên hệ */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Liên hệ & Hỗ trợ</Text>

            <TouchableOpacity
              style={styles.contactItem}
              onPress={handleEmailSupport}
            >
              <Ionicons name='mail-outline' size={24} color='#0F9D58' />
              <Text style={styles.contactText}>Gửi email hỗ trợ</Text>
              <Ionicons name='chevron-forward' size={18} color='#999' />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.contactItem}
              onPress={handleOpenWebsite}
            >
              <Ionicons name='globe-outline' size={24} color='#0F9D58' />
              <Text style={styles.contactText}>Truy cập website</Text>
              <Ionicons name='chevron-forward' size={18} color='#999' />
            </TouchableOpacity>
          </View>

          {/* Chức năng nâng cao */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Thêm thông tin</Text>

            <TouchableOpacity style={styles.contactItem}>
              <Ionicons name='book-outline' size={24} color='#0F9D58' />
              <Text style={styles.contactText}>Điều khoản sử dụng</Text>
              <Ionicons name='chevron-forward' size={18} color='#999' />
            </TouchableOpacity>

            <TouchableOpacity style={styles.contactItem}>
              <Ionicons
                name='shield-checkmark-outline'
                size={24}
                color='#0F9D58'
              />
              <Text style={styles.contactText}>Chính sách bảo mật</Text>
              <Ionicons name='chevron-forward' size={18} color='#999' />
            </TouchableOpacity>

            <TouchableOpacity style={styles.contactItem}>
              <Ionicons name='star-outline' size={24} color='#0F9D58' />
              <Text style={styles.contactText}>Đánh giá ứng dụng</Text>
              <Ionicons name='chevron-forward' size={18} color='#999' />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  backButton: {
    padding: 4,
  },
  rightPlaceholder: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  appInfoContainer: {
    alignItems: 'center',
    padding: 24,
    marginBottom: 16,
    backgroundColor: '#fff',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  logoContainer: {
    width: 90,
    height: 90,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#E8F5E9',
    marginBottom: 16,
  },
  appName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  appVersion: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  copyright: {
    fontSize: 12,
    color: '#999',
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    color: '#333',
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  infoLabel: {
    fontSize: 15,
    color: '#666',
  },
  infoValue: {
    fontSize: 15,
    color: '#333',
    fontWeight: '500',
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  contactText: {
    flex: 1,
    fontSize: 15,
    color: '#333',
    marginLeft: 12,
  },
});

export default AboutScreen;
