{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "paths": {"@/*": ["./src/*"], "@components/*": ["./src/shared/components/*"], "@atoms/*": ["./src/shared/components/atoms/*"], "@molecules/*": ["./src/shared/components/molecules/*"], "@organisms/*": ["./src/shared/components/organisms/*"], "@hooks/*": ["./src/shared/hooks/*"], "@utils/*": ["./src/shared/utils/*"], "@types/*": ["./src/shared/types/*"], "@features/*": ["./src/features/*"], "@core/*": ["./src/core/*"], "@i18n/*": ["./src/i18n/*"], "@assets/*": ["./src/assets/*"]}}, "include": ["**/*.ts", "**/*.tsx", "src/e2e/jest.setup.ts", "src/@types/toast.d.ts"]}