import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { FontAwesome5 } from '@expo/vector-icons';
import { useTokens } from './tokenContext';
import { ROUTES } from '@/core/constants/routes';

type TokenWidgetProps = {
  size?: 'small' | 'medium' | 'large';
  showButton?: boolean;
};

const TokenWidget: React.FC<TokenWidgetProps> = ({
  size = 'medium',
  showButton = true,
}) => {
  const router = useRouter();
  const { tokenBalance, tokenCosts } = useTokens();

  // Determine if token balance is low (less than 5 document scans)
  const isLowBalance = tokenBalance < tokenCosts.scanDocument * 5;

  // Size variations
  const getSize = () => {
    switch (size) {
      case 'small':
        return {
          container: styles.containerSmall,
          icon: 16,
          text: styles.balanceTextSmall,
          button: styles.buttonSmall,
          buttonText: styles.buttonTextSmall,
        };
      case 'large':
        return {
          container: styles.containerLarge,
          icon: 22,
          text: styles.balanceTextLarge,
          button: styles.buttonLarge,
          buttonText: styles.buttonTextLarge,
        };
      default:
        return {
          container: styles.containerMedium,
          icon: 18,
          text: styles.balanceTextMedium,
          button: styles.buttonMedium,
          buttonText: styles.buttonTextMedium,
        };
    }
  };

  const sizeStyles = getSize();

  return (
    <View style={[styles.container, sizeStyles.container]}>
      <View style={styles.balanceContainer}>
        <FontAwesome5
          name='coins'
          size={sizeStyles.icon}
          color={isLowBalance ? '#EA4335' : '#2F4FCD'}
        />
        <Text
          style={[
            styles.balanceText,
            sizeStyles.text,
            isLowBalance && styles.balanceTextLow,
          ]}
        >
          {tokenBalance}
        </Text>
      </View>

      {showButton && (
        <TouchableOpacity
          style={[styles.button, sizeStyles.button]}
          onPress={() => router.push(ROUTES.TOKENS)}
        >
          <Text style={[styles.buttonText, sizeStyles.buttonText]}>
            {isLowBalance ? 'Mua ngay' : 'Nạp thêm'}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f5f6fa',
    borderRadius: 8,
    padding: 8,
  },
  containerSmall: {
    paddingVertical: 4,
    paddingHorizontal: 6,
  },
  containerMedium: {
    paddingVertical: 6,
    paddingHorizontal: 8,
  },
  containerLarge: {
    paddingVertical: 10,
    paddingHorizontal: 12,
  },
  balanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  balanceText: {
    fontWeight: '700',
    marginLeft: 6,
    color: '#2F4FCD',
  },
  balanceTextSmall: {
    fontSize: 13,
  },
  balanceTextMedium: {
    fontSize: 15,
  },
  balanceTextLarge: {
    fontSize: 18,
  },
  balanceTextLow: {
    color: '#EA4335',
  },
  button: {
    backgroundColor: '#2F4FCD',
    borderRadius: 6,
    marginLeft: 8,
  },
  buttonSmall: {
    paddingVertical: 2,
    paddingHorizontal: 6,
  },
  buttonMedium: {
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  buttonLarge: {
    paddingVertical: 6,
    paddingHorizontal: 10,
  },
  buttonText: {
    color: '#fff',
    fontWeight: '600',
  },
  buttonTextSmall: {
    fontSize: 10,
  },
  buttonTextMedium: {
    fontSize: 12,
  },
  buttonTextLarge: {
    fontSize: 14,
  },
});

export default TokenWidget;
