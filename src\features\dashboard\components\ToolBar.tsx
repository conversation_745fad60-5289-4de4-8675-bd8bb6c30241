import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons, MaterialCommunityIcons, Feather } from '@expo/vector-icons';

const tools = [
  { key: 'sign', label: 'Sign', icon: <Feather name='edit-3' size={24} /> },
  {
    key: 'protect',
    label: 'Protect',
    icon: <Ionicons name='key-outline' size={24} />,
  },
  {
    key: 'compress',
    label: 'Compress',
    icon: <MaterialCommunityIcons name='star-four-points-outline' size={24} />,
  },
  {
    key: 'other',
    label: 'Other',
    icon: <Ionicons name='apps-outline' size={24} />,
  },
];

export default function ToolBar() {
  const [active, setActive] = useState('sign');

  return (
    <View style={styles.container}>
      {tools.map(tool => (
        <TouchableOpacity
          key={tool.key}
          style={[styles.toolItem, active === tool.key && styles.activeItem]}
          onPress={() => setActive(tool.key)}
        >
          <View
            style={[
              styles.iconContainer,
              active === tool.key && styles.activeIcon,
            ]}
          >
            {React.cloneElement(tool.icon, {
              color: active === tool.key ? '#2F4FCD' : '#999',
            })}
          </View>
          <Text
            style={[styles.label, active === tool.key && styles.activeLabel]}
          >
            {tool.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
  },
  toolItem: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    padding: 12,
    borderRadius: 12,
    backgroundColor: '#f2f2f2',
    marginBottom: 4,
  },
  label: {
    fontSize: 12,
    color: '#999',
  },
  activeItem: {},
  activeIcon: {
    backgroundColor: '#e7ebff',
  },
  activeLabel: {
    color: '#2F4FCD',
    fontWeight: '600',
  },
});
