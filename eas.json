{"cli": {"version": ">= 4.1.2"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "env": {"EXPO_USE_UPDATES": "1"}}, "preview": {"distribution": "internal", "env": {"EXPO_USE_UPDATES": "1"}}, "production": {"ios": {"distribution": "store", "buildConfiguration": "Release"}, "env": {"API_BASE_URL": "http://core.qlda.nhattamsoft.vn/api/v1", "EXPO_USE_UPDATES": "1"}, "channel": "production"}, "test": {"android": {"gradleCommand": ":app:assembleRelease :app:assembleAndroidTest -DtestBuildType=release", "withoutCredentials": true}, "ios": {"simulator": true}}, "test_debug": {"android": {"gradleCommand": ":app:assembleDebug :app:assembleAndroidTest -DtestBuildType=debug", "withoutCredentials": true}, "ios": {"buildConfiguration": "Debug", "simulator": true}, "env": {"EXPO_USE_UPDATES": "1"}, "channel": "test_debug"}}, "submit": {"production": {}}}