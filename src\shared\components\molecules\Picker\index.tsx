import React, { useState } from 'react';
import { But<PERSON>, Picker } from 'react-native-ui-lib';

const SamplePicker = () => {
  const [value, setValue] = useState();

  const [selected, setSelected] = useState<string | number | undefined>();
  const options = [
    { value: 'du lieu tuy chon 1', label: 'Tuỳ chọn 1' },
    { value: 'du lieu tuy chon 2', label: 'Tuỳ chọn 2' },
  ];

  return (
    <Picker
      value={selected}
      onChange={val => {
        const label = options.find(o => o.value === val)?.label;
        const value = options.find(o => o.value === val)?.value;
      }}
      showSearch
      useSafeArea
      showCharCounter
      showSoftInputOnFocus
      showLoader={false}
      topBarProps={{ title: 'Danh sách chọn' }}
      searchPlaceholder='Tìm kiếm'
      items={options}
      floatingPlaceholder
      enableModalBlur={false}
      renderInput={(value: any, label?: string) => <Button label={'Press'} />}
    />
  );
};

export default SamplePicker;
