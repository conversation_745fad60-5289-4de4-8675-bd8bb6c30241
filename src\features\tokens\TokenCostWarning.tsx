import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTokens } from './tokenContext';
import { FontAwesome5, Ionicons } from '@expo/vector-icons';

interface TokenCostWarningProps {
  operationType: 'scan' | 'ocr' | 'storage';
  onProceed: () => void;
  onCancel: () => void;
}

const TokenCostWarning: React.FC<TokenCostWarningProps> = ({ 
  operationType,
  onProceed,
  onCancel
}) => {
  const { tokenBalance, tokenCosts } = useTokens();
  
  // Get cost for the selected operation
  const getOperationCost = () => {
    switch (operationType) {
      case 'scan':
        return tokenCosts.scanDocument;
      case 'ocr':
        return tokenCosts.ocrProcessing;
      case 'storage':
        return tokenCosts.cloudStorage;
      default:
        return 0;
    }
  };
  
  const operationCost = getOperationCost();
  const hasEnoughTokens = tokenBalance >= operationCost;
  
  // Get operation text for display
  const getOperationText = () => {
    switch (operationType) {
      case 'scan':
        return 'quét tài liệu';
      case 'ocr':
        return 'nhận dạng văn bản';
      case 'storage':
        return 'lưu trữ tài liệu';
      default:
        return 'thực hiện tác vụ';
    }
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <FontAwesome5 name="coins" size={32} color="#FBBC05" />
      </View>
      
      <Text style={styles.title}>
        {hasEnoughTokens 
          ? `Thực hiện ${getOperationText()}`
          : 'Không đủ token'
        }
      </Text>
      
      <Text style={styles.description}>
        {hasEnoughTokens
          ? `Thao tác này sẽ sử dụng ${operationCost} token từ tài khoản của bạn.`
          : `Bạn cần thêm ${operationCost - tokenBalance} token để ${getOperationText()}.`
        }
      </Text>
      
      <View style={styles.balanceContainer}>
        <Text style={styles.balanceLabel}>Số dư hiện tại:</Text>
        <View style={styles.balanceValueContainer}>
          <FontAwesome5 name="coins" size={16} color="#2F4FCD" />
          <Text style={styles.balanceValue}>{tokenBalance} token</Text>
        </View>
      </View>
      
      {hasEnoughTokens ? (
        <View style={styles.buttonsContainer}>
          <TouchableOpacity 
            style={styles.cancelButton} 
            onPress={onCancel}
          >
            <Text style={styles.cancelButtonText}>Hủy</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.proceedButton}
            onPress={onProceed}
          >
            <Text style={styles.proceedButtonText}>Tiếp tục</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.buttonsContainer}>
          <TouchableOpacity 
            style={styles.cancelButton}
            onPress={onCancel}
          >
            <Text style={styles.cancelButtonText}>Đóng</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.addTokensButton}
            onPress={() => {
              // Navigate to token purchase screen
              onCancel();
              // This would be handled by the parent component to navigate
            }}
          >
            <FontAwesome5 name="coins" size={14} color="#fff" style={styles.buttonIcon} />
            <Text style={styles.addTokensButtonText}>Mua token</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    width: '100%',
  },
  iconContainer: {
    backgroundColor: 'rgba(251, 188, 5, 0.1)',
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  description: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  balanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f6fa',
    paddingVertical: 10,
    paddingHorizontal: 14,
    borderRadius: 8,
    width: '100%',
    marginBottom: 24,
    justifyContent: 'space-between',
  },
  balanceLabel: {
    fontSize: 14,
    color: '#666',
  },
  balanceValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  balanceValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2F4FCD',
    marginLeft: 6,
  },
  buttonsContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#f5f6fa',
    borderRadius: 8,
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  proceedButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#2F4FCD',
    borderRadius: 8,
    alignItems: 'center',
    marginLeft: 8,
  },
  proceedButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
  addTokensButton: {
    flex: 1,
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#2F4FCD',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  addTokensButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
  buttonIcon: {
    marginRight: 6,
  },
});

export default TokenCostWarning; 