import DocumentScanner, {
  ScanDocumentResponse,
} from 'react-native-document-scanner-plugin';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as ImageManipulator from 'expo-image-manipulator';
// eslint-disable-next-line import/no-duplicates
import { Platform } from 'react-native';
import { v4 as uuidv4 } from 'uuid';
import { DocumentRepository } from '@core/database/repositories/documentRepository';
// eslint-disable-next-line import/no-duplicates
import { Image } from 'react-native';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';

export interface ScanOptions {
  maxNumDocuments?: number;
  quality?: 'low' | 'medium' | 'high';
  compression?: boolean;
  autoCrop?: boolean;
}

export interface ScanResult {
  id: string;
  images: string[];
  timestamp: number;
  size: number;
}

export class ScanService {
  private static instance: ScanService;
  private readonly TEMP_DIR: string;
  private readonly MAX_BATCH_SIZE = 5;
  private readonly COMPRESSION_QUALITY = 0.8;
  private readonly maxImageSize = 5 * 1024 * 1024; // 5MB
  private documentRepository: DocumentRepository;

  private constructor() {
    this.TEMP_DIR = `${FileSystem.cacheDirectory}scan_temp/`;
    this.initializeTempDir();
    this.documentRepository = new DocumentRepository();
  }

  public static getInstance(): ScanService {
    if (!ScanService.instance) {
      ScanService.instance = new ScanService();
    }
    return ScanService.instance;
  }

  private async initializeTempDir() {
    try {
      const dirInfo = await FileSystem.getInfoAsync(this.TEMP_DIR);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.TEMP_DIR, {
          intermediates: true,
        });
      }
    } catch (error) {
      console.error('Error initializing temp directory:', error);
    }
  }

  public async startScan(
    options: ScanOptions = {},
  ): Promise<ScanResult | null> {
    try {
      const scanOptions = {
        maxNumDocuments: options.maxNumDocuments || this.MAX_BATCH_SIZE,
        quality: options.quality || 'medium',
        autoCrop: options.autoCrop ?? true,
      };

      const result = await DocumentScanner.scanDocument(scanOptions);

      if (result?.status === 'success' && Array.isArray(result.scannedImages)) {
        const processedImages = await this.processImages(
          result.scannedImages,
          options.compression,
        );
        const totalSize = await this.calculateTotalSize(processedImages);

        return {
          id: uuidv4(),
          images: processedImages,
          timestamp: Date.now(),
          size: totalSize,
        };
      }

      return null;
    } catch (error) {
      console.error('Scan error:', error);
      throw error;
    }
  }

  public async processImages(
    images: string[],
    shouldCompress: boolean = true,
  ): Promise<string[]> {
    console.log(`Bắt đầu xử lý ${images.length} ảnh`, { shouldCompress });
    const processedImages: string[] = [];

    // Xử lý song song để tăng tốc độ
    const processPromises = images.map(async (image, index) => {
      try {
        console.log(`Đang xử lý ảnh ${index + 1}/${images.length}: ${image}`);
        let processedImage = image;

        if (shouldCompress) {
          processedImage = await this.compressImage(image);
        }

        // Lưu ảnh vào thư mục tạm
        const fileName = `${uuidv4()}.jpg`;
        const filePath = `${this.TEMP_DIR}${fileName}`;

        await FileSystem.copyAsync({
          from: processedImage,
          to: filePath,
        });

        console.log(`Đã lưu ảnh ${index + 1} vào ${filePath}`);
        return filePath;
      } catch (error) {
        console.error(`Lỗi xử lý ảnh ${index + 1}:`, error);
        return null;
      }
    });

    // Đợi tất cả các ảnh được xử lý
    const results = await Promise.all(processPromises);

    // Lọc bỏ các kết quả null
    const filtered = results.filter(result => result !== null) as string[];
    console.log(
      `Hoàn thành xử lý ảnh: ${filtered.length}/${images.length} ảnh thành công`,
    );
    return filtered;
  }

  private async compressImage(uri: string): Promise<string> {
    try {
      // Kiểm tra kích thước file
      const fileInfo = await FileSystem.getInfoAsync(uri);
      if (!fileInfo.exists) {
        throw new Error('File does not exist');
      }

      // Nếu file nhỏ hơn giới hạn, giữ nguyên
      if (fileInfo.size && fileInfo.size <= this.maxImageSize) {
        return uri;
      }

      // Tính toán mức nén dựa trên kích thước
      let compressionQuality = 0.8; // Mặc định
      let resizeWidth = 2000; // Mặc định

      if (fileInfo.size > 10 * 1024 * 1024) {
        // Ảnh > 10MB: nén mạnh
        compressionQuality = 0.6;
        resizeWidth = 1800;
      } else if (fileInfo.size > 5 * 1024 * 1024) {
        // Ảnh 5-10MB: nén vừa
        compressionQuality = 0.7;
        resizeWidth = 2000;
      }

      // Tạo file mới với tên ngẫu nhiên trong thư mục tạm
      const fileName = `compressed_${uuidv4()}.jpg`;
      const newUri = `${this.TEMP_DIR}${fileName}`;

      // Sử dụng ImageManipulator để thực sự nén ảnh
      const manipulatorResult = await ImageManipulator.manipulateAsync(
        uri,
        [{ resize: { width: resizeWidth } }],
        {
          compress: compressionQuality,
          format: ImageManipulator.SaveFormat.JPEG,
        },
      );

      // Copy ảnh đã nén vào thư mục tạm
      await FileSystem.copyAsync({
        from: manipulatorResult.uri,
        to: newUri,
      });

      // Lấy thông tin kích thước file sau khi nén
      const compressedInfo = await FileSystem.getInfoAsync(newUri);
      const originalSize = fileInfo.exists ? fileInfo.size || 0 : 0;
      const compressedSize = compressedInfo.exists
        ? compressedInfo.size || 0
        : 0;

      console.log(
        `Compressed image from ${originalSize} bytes to ${compressedSize} bytes`,
      );

      return newUri;
    } catch (error) {
      console.error('Error compressing image:', error);
      return uri; // Trả về ảnh gốc nếu có lỗi
    }
  }

  private async calculateTotalSize(images: string[]): Promise<number> {
    let totalSize = 0;

    for (const image of images) {
      try {
        const fileInfo = await FileSystem.getInfoAsync(image);
        if (fileInfo.exists) {
          totalSize += fileInfo.size || 0;
        }
      } catch (error) {
        console.error('Error calculating file size:', error);
      }
    }

    return totalSize;
  }

  public async shareImages(images: string[]): Promise<void> {
    try {
      if (!(await Sharing.isAvailableAsync())) {
        throw new Error('Sharing is not available on this device');
      }

      const tempFiles = await Promise.all(
        images.map(async uri => {
          const filename = uri.split('/').pop() || 'image.jpg';
          const tempUri = this.TEMP_DIR + filename;
          await FileSystem.copyAsync({
            from: uri,
            to: tempUri,
          });
          return tempUri;
        }),
      );

      await Sharing.shareAsync(tempFiles[0], {
        mimeType: 'image/jpeg',
        dialogTitle: 'Share Document',
        UTI: 'public.jpeg',
      });

      // Cleanup temp files
      await Promise.all(tempFiles.map(uri => FileSystem.deleteAsync(uri)));
    } catch (error) {
      console.error('Error sharing images:', error);
      throw error;
    }
  }

  public async saveImages(images: string[]): Promise<void> {
    try {
      // const { status } = await MediaLibrary.requestPermissionsAsync();
      // if (status !== 'granted') {
      //     throw new Error('Permission to access media library was denied');
      // }
      // const savePromises = images.map(async (uri) => {
      //     const asset = await MediaLibrary.createAssetAsync(uri);
      //     await MediaLibrary.createAlbumAsync('Scanned Documents', asset, false);
      // });
      // await Promise.all(savePromises);
    } catch (error) {
      console.error('Error saving images:', error);
      throw error;
    }
  }

  public async cleanupTempFiles(): Promise<void> {
    try {
      const dirInfo = await FileSystem.getInfoAsync(this.TEMP_DIR);
      if (dirInfo.exists) {
        const files = await FileSystem.readDirectoryAsync(this.TEMP_DIR);
        await Promise.all(
          files.map(file =>
            FileSystem.deleteAsync(`${this.TEMP_DIR}${file}`, {
              idempotent: true,
            }),
          ),
        );
      }
    } catch (error) {
      console.error('Error cleaning up temp files:', error);
    }
  }

  public async getStorageInfo(): Promise<{ used: number; total: number }> {
    try {
      const files = await FileSystem.readDirectoryAsync(this.TEMP_DIR);
      let totalSize = 0;

      for (const file of files) {
        const fileInfo = await FileSystem.getInfoAsync(
          `${this.TEMP_DIR}${file}`,
        );
        if (fileInfo.exists) {
          totalSize += fileInfo.size || 0;
        }
      }

      return {
        used: totalSize,
        total: Platform.OS === 'ios' ? 1024 * 1024 * 1024 : 0, // 1GB for iOS, unlimited for Android
      };
    } catch (error) {
      console.error('Error getting storage info:', error);
      return { used: 0, total: 0 };
    }
  }

  /**
   * Save a scanned image to the database and file system
   * @param imageUri Original image URI (can be a temporary URI)
   * @param title Document title
   * @param description Optional document description
   * @param projectId Optional project ID
   * @param userId Optional user ID
   * @returns Promise with the ID of the saved document
   */
  async saveScannedDocument(
    imageUri: string,
    title: string,
    description?: string,
    projectId?: string,
    userId?: string,
  ): Promise<string> {
    try {
      // Create permanent storage directory if it doesn't exist
      const documentsDir = `${FileSystem.documentDirectory}scanned_documents/`;
      const dirInfo = await FileSystem.getInfoAsync(documentsDir);

      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(documentsDir, {
          intermediates: true,
        });
      }

      // Get file info to determine size and type
      const fileInfo = await FileSystem.getInfoAsync(imageUri);
      const fileSize = fileInfo.exists ? (fileInfo as any).size || 0 : 0;

      // Generate filename with timestamp
      const timestamp = Date.now();
      const fileExtension = imageUri.split('.').pop() || 'jpg';
      const fileName = `scan_${timestamp}.${fileExtension}`;
      const filePath = `${documentsDir}${fileName}`;

      // Copy file to permanent storage
      await FileSystem.copyAsync({
        from: imageUri,
        to: filePath,
      });

      // Create thumbnail
      const thumbnailFileName = `thumb_${timestamp}.jpg`;
      const thumbnailPath = `${documentsDir}${thumbnailFileName}`;

      // Get image dimensions
      const { width, height } = await new Promise<{
        width: number;
        height: number;
      }>(resolve => {
        Image.getSize(
          imageUri,
          (width, height) => {
            resolve({ width, height });
          },
          () => {
            resolve({ width: 800, height: 600 }); // Default if we can't get dimensions
          },
        );
      });

      // Calculate thumbnail size (max width/height of 300px)
      const MAX_THUMB_SIZE = 300;
      const aspectRatio = width / height;
      let thumbWidth = width;
      let thumbHeight = height;

      if (width > height && width > MAX_THUMB_SIZE) {
        thumbWidth = MAX_THUMB_SIZE;
        thumbHeight = thumbWidth / aspectRatio;
      } else if (height > MAX_THUMB_SIZE) {
        thumbHeight = MAX_THUMB_SIZE;
        thumbWidth = thumbHeight * aspectRatio;
      }

      // Create thumbnail using image-manipulator
      const thumbnail = await manipulateAsync(
        imageUri,
        [
          {
            resize: {
              width: Math.round(thumbWidth),
              height: Math.round(thumbHeight),
            },
          },
        ],
        { format: SaveFormat.JPEG, compress: 0.7 },
      );

      await FileSystem.copyAsync({
        from: thumbnail.uri,
        to: thumbnailPath,
      });

      // Save document to database
      const docId = await this.documentRepository.add({
        title,
        description,
        filePath,
        fileType: `image/${fileExtension}`,
        fileSize,
        thumbnailPath,
        projectId,
        userId,
        isSynchronized: false,
      });

      return docId;
    } catch (error) {
      console.error('Error saving scanned document:', error);
      throw error;
    }
  }

  /**
   * Get all saved documents
   * @param options Optional filtering options
   * @returns Promise with array of documents
   */
  async getAllDocuments(options?: { projectId?: string; userId?: string }) {
    return this.documentRepository.getAll(options);
  }

  /**
   * Get a document by ID
   * @param id Document ID
   * @returns Promise with document or null if not found
   */
  async getDocumentById(id: string) {
    return this.documentRepository.getById(id);
  }

  /**
   * Delete a document by ID (removes from database and file system)
   * @param id Document ID
   */
  async deleteDocument(id: string) {
    try {
      // Get document to find file paths
      const document = await this.documentRepository.getById(id);

      if (document) {
        // Delete the files first
        if (document.filePath) {
          const fileInfo = await FileSystem.getInfoAsync(document.filePath);
          if (fileInfo.exists) {
            await FileSystem.deleteAsync(document.filePath);
          }
        }

        if (document.thumbnailPath) {
          const thumbInfo = await FileSystem.getInfoAsync(
            document.thumbnailPath,
          );
          if (thumbInfo.exists) {
            await FileSystem.deleteAsync(document.thumbnailPath);
          }
        }

        // Then delete the database record
        await this.documentRepository.delete(id);
      }
    } catch (error) {
      console.error('Error deleting document:', error);
      throw error;
    }
  }
}

export default ScanService;
