import { useCallback } from 'react';
import { Keyboard } from 'react-native';

export const useKeyboardDismiss = () => {
  const dismissKeyboard = useCallback(() => {
    Keyboard.dismiss();
  }, []);

  const createTouchableProps = useCallback(
    () => ({
      activeOpacity: 1,
      onPress: dismissKeyboard,
    }),
    [dismissKeyboard],
  );

  return {
    dismissKeyboard,
    createTouchableProps,
  };
};
