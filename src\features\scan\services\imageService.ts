import { imageApi } from '@core/client/axios';
import { API_CONFIG } from '@core/api/config';
import { getCurrentProjectId, getCurrentDocumentType } from '@core/api/apiConfigManager';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import { aiModelStorage } from '@/core/storage/aiModelStorage';

export interface UploadProgress {
    total: number;
    loaded: number;
    percentage: number;
}

export interface ImageUploadResult {
    success: boolean;
    data?: any;
    error?: string;
}

export const imageService = {
    /**
     * Upload multiple images to the server
     * @param images Array of image URIs to upload
     * @param onProgress Optional callback for upload progress
     */
    uploadImages: async (
        images: string[],
        onProgress?: (progress: UploadProgress) => void
    ): Promise<ImageUploadResult> => {
        try {
            const formData = new FormData();

            // Log initial images array
            console.log('Initial images array:', images);

            // Add images to formData
            for (let i = 0; i < images.length; i++) {
                const imageUri = images[i];
                const filename = imageUri.split('/').pop() || `image_${i}.jpg`;

                // Get file info
                const fileInfo = await FileSystem.getInfoAsync(imageUri);
                if (!fileInfo.exists) {
                    throw new Error(`File not found: ${imageUri}`);
                }

                // Create file object from URI
                const file = {
                    uri: Platform.OS === 'ios' ? imageUri.replace('file://', '') : imageUri,
                    name: filename,
                    type: 'image/jpeg',
                    size: fileInfo.size,
                };

                // Log file details before appending
                console.log('Adding file to FormData:', {
                    index: i,
                    uri: file.uri,
                    name: file.name,
                    type: file.type,
                    size: file.size,
                    exists: fileInfo.exists
                });

                formData.append('files', file as any);
            }

            // Log FormData contents in detail
            console.log('FormData contents before sending:', {
                entries: Array.from(formData.entries()).map(([key, value]) => {
                    console.log('FormData entry:', { key, value });
                    return {
                        key,
                        value: value instanceof File ? {
                            name: value.name,
                            type: value.type,
                            size: value.size,
                        } : value,
                    };
                }),
            });

            // Get params from API_CONFIG via apiConfigManager to ensure we have the latest values
            const loaiVanBan = getCurrentDocumentType();
            const duAnID = getCurrentProjectId();
            const defaultModel = aiModelStorage.getDefaultModel();

            // Kiểm tra và báo lỗi nếu không có dự án hoặc loại tài liệu
            if (!loaiVanBan) {
                throw new Error('Vui lòng chọn loại tài liệu trước khi gửi ảnh');
            }

            if (!duAnID) {
                throw new Error('Vui lòng chọn dự án trước khi gửi ảnh');
            }

            if (!defaultModel) {
                throw new Error('Vui lòng chọn mô hình AI trước khi gửi ảnh');
            }

            const params = {
                loaiVanBan,
                duAnID
            };
            console.log("params", params)
            // Tạo URL đầy đủ để in ra log
            const fullURL = `${API_CONFIG.IMAGE_API_URL}${defaultModel?.Url}?loaiVanBan=${params.loaiVanBan}&duAnID=${params.duAnID}`;
            console.log('FULL SCAN URL:', fullURL);

            // Log full request details
            console.log('Sending request with:', {
                url: API_CONFIG.ENDPOINTS.IMAGE_EXTRACT,
                params,
                formData: formData,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'multipart/form-data',
                }
            });

            const response = await imageApi.post(API_CONFIG.ENDPOINTS.IMAGE_EXTRACT, formData, {
                params,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'multipart/form-data',
                },
                onUploadProgress: (progressEvent) => {
                    if (progressEvent.total) {
                        const percentage = Math.round((progressEvent.loaded / progressEvent.total) * 100);
                        console.log('Upload progress:', {
                            loaded: progressEvent.loaded,
                            total: progressEvent.total,
                            percentage
                        });
                        onProgress?.({
                            total: progressEvent.total,
                            loaded: progressEvent.loaded,
                            percentage,
                        });
                    }
                },
            });

            return {
                success: true,
                data: response.data,
            };
        } catch (error: any) {
            console.error('Upload error:', error);
            return {
                success: false,
                error: error.message || 'Upload failed',
            };
        }
    },

    /**
     * Get image metadata by URI
     * @param imageUri The image URI to get metadata for
     */
    getImageInfo: async (imageUri: string) => {
        try {
            const fileInfo = await FileSystem.getInfoAsync(imageUri, {
                size: true,
                md5: true
            });
            return fileInfo;
        } catch (error) {
            console.error('Error getting image info:', error);
            throw error;
        }
    }
}; 