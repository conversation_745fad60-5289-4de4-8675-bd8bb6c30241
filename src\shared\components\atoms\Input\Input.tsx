import React from 'react';
import { TextField, View } from 'react-native-ui-lib';
import type { TextFieldProps } from 'react-native-ui-lib';
import type { ReturnKeyTypeOptions } from 'react-native';

interface InputProps
  extends Omit<
    TextFieldProps,
    'error' | 'leadingAccessory' | 'trailingAccessory'
  > {
  label?: string;
  placeholder?: string;
  value?: string;
  onChangeText?: (text: string) => void;
  error?: string | boolean;
  hint?: string;
  secureTextEntry?: boolean;
  disabled?: boolean;
  multiline?: boolean;
  maxLength?: number;
  leadingAccessory?: React.ReactElement;
  trailingAccessory?: React.ReactElement;
  style?: object;
  testID?: string;
  // Keyboard optimization props
  returnKeyType?: ReturnKeyTypeOptions;
  onSubmitEditing?: () => void;
  blurOnSubmit?: boolean;
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  keyboardType?:
    | 'default'
    | 'email-address'
    | 'numeric'
    | 'phone-pad'
    | 'number-pad'
    | 'decimal-pad';
}

export const Input: React.FC<InputProps> = ({
  label,
  placeholder,
  value,
  onChangeText,
  error,
  hint,
  secureTextEntry,
  disabled,
  multiline,
  maxLength,
  leadingAccessory,
  trailingAccessory,
  style,
  testID,
  // Keyboard optimization props
  returnKeyType = 'done',
  onSubmitEditing,
  blurOnSubmit = true,
  autoCapitalize = 'none',
  keyboardType = 'default',
  ...props
}) => {
  // Convert string error to boolean
  const errorState = error ? true : false;
  const errorMessage = typeof error === 'string' ? error : '';

  return (
    <View style={{ marginBottom: 16 }}>
      <TextField
        label={label}
        placeholder={placeholder}
        value={value}
        onChangeText={onChangeText}
        error={errorState}
        hint={hint || errorMessage}
        secureTextEntry={secureTextEntry}
        editable={!disabled}
        multiline={multiline}
        maxLength={maxLength}
        leadingAccessory={leadingAccessory}
        trailingAccessory={trailingAccessory}
        testID={testID}
        style={style}
        // Keyboard optimization props
        returnKeyType={returnKeyType}
        onSubmitEditing={onSubmitEditing}
        blurOnSubmit={blurOnSubmit}
        autoCapitalize={autoCapitalize}
        keyboardType={keyboardType}
        {...props}
      />
    </View>
  );
};
