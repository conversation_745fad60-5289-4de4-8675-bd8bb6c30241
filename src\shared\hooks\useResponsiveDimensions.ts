import { useState, useEffect } from 'react';
import { Dimensions, ScaledSize, Platform } from 'react-native';
import { ResponsiveUtils } from '@core/utils/helpers';

interface ResponsiveDimensions {
  width: number;
  height: number;
  wp: (percentage: number) => number;
  hp: (percentage: number) => number;
  rf: (size: number) => number;
  rs: (size: number, minSize?: number, maxSize?: number) => number;
  isSmallScreen: boolean;
  isLargeScreen: boolean;
  deviceSize: 'small' | 'medium' | 'large';
  getResponsiveSpacing: (baseSpacing: number) => number;
  getResponsiveBorderRadius: (baseBorderRadius: number) => number;
  getScanFrameSize: (sizePercent?: number, maxSize?: number) => number;
}

/**
 * Hook for responsive dimensions that updates on orientation change
 * Provides utilities for responsive design across different screen sizes
 */
export const useResponsiveDimensions = (): ResponsiveDimensions => {
  const [dimensions, setDimensions] = useState(() => Dimensions.get('window'));

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setDimensions(window);
    });

    return () => subscription?.remove();
  }, []);

  return {
    width: dimensions.width,
    height: dimensions.height,
    wp: ResponsiveUtils.wp,
    hp: ResponsiveUtils.hp,
    rf: ResponsiveUtils.rf,
    rs: ResponsiveUtils.rs,
    isSmallScreen: ResponsiveUtils.isSmallScreen(),
    isLargeScreen: ResponsiveUtils.isLargeScreen(),
    deviceSize: ResponsiveUtils.getDeviceSize(),
    getResponsiveSpacing: ResponsiveUtils.getResponsiveSpacing,
    getResponsiveBorderRadius: ResponsiveUtils.getResponsiveBorderRadius,
    getScanFrameSize: ResponsiveUtils.getScanFrameSize,
  };
};

/**
 * Hook specifically for QR Scanner responsive values
 */
export const useQRScannerDimensions = () => {
  const responsive = useResponsiveDimensions();

  const scanConfig = {
    sizePercent: 0.75,
    maxSize: responsive.rs(300, 250, 350),
    offsetY:
      Platform.OS === 'android'
        ? 0 // Center on screen for Android
        : responsive.rs(-14, -20, -10), // Keep iOS offset
  };

  const scanFrameSize = responsive.getScanFrameSize(
    scanConfig.sizePercent,
    scanConfig.maxSize,
  );

  return {
    ...responsive,
    scanFrameSize,
    scanAreaOffsetY: scanConfig.offsetY,
    // Animation values
    scanLineRange: {
      start: responsive.rs(20, 15, 25),
      end: responsive.rs(180, 150, 210),
    },
    // Icon sizes
    iconSizes: {
      header: responsive.rs(24, 20, 28),
      qrCenter: responsive.rs(90, 75, 105),
      pickImage: responsive.rs(20, 18, 22),
    },
    // Button sizes
    buttonSizes: {
      header: responsive.rs(44, 40, 48),
    },
    // Corner sizes
    cornerSizes: {
      width: responsive.rs(40, 35, 45),
      height: responsive.rs(40, 35, 45),
      borderWidth: responsive.rs(5, 4, 6),
      offset: responsive.rs(-3, -2, -4),
      shadowRadius: responsive.rs(6, 5, 7),
    },
    // Glow effect sizes
    glowSizes: {
      outer: responsive.rs(60, 50, 70),
      inner: responsive.rs(30, 25, 35),
      outerShadowRadius: responsive.rs(50, 40, 60),
      innerShadowRadius: responsive.rs(35, 30, 40),
    },
    // Scan line
    scanLine: {
      height: responsive.rs(4, 3, 5),
      shadowRadius: responsive.rs(12, 10, 14),
      borderRadius: responsive.rs(2, 1.5, 2.5),
    },
    // Frame border
    frameBorder: {
      width: responsive.rs(2, 1.5, 2.5),
      shadowRadius: responsive.rs(25, 20, 30),
    },
  };
};
