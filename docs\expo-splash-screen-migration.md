# Migration to Expo SplashScreen

## Tổng quan

Đã thay đổi từ custom SplashScreen component sang sử dụng SplashScreen của Expo để tối ưu hiệu suất và tận dụng native splash screen.

## Những thay đổi đã thực hiện

### 1. Cậ<PERSON> nhật `src/app/_layout.tsx`

**Trước:**
- Sử dụng custom `SplashScreen` component
- Quản lý state `showSplash` với `useState`
- Hiển thị custom component trong 2 giây

**Sau:**
- Import `* as SplashScreen from 'expo-splash-screen'`
- Gọi `SplashScreen.preventAutoHideAsync()` để ngăn auto-hide
- Sử dụng `SplashScreen.hideAsync()` để ẩn splash screen
- Loại bỏ state management phức tạp

### 2. Cập nhật `app.config.ts`

**Thêm cấu hình splash:**
```typescript
splash: {
  image: './src/assets/images/splash.png',
  resizeMode: 'contain',
  backgroundColor: '#1E3A8A', // Màu xanh dương
},
```

## Lợi ích của việc sử dụng Expo SplashScreen

### 1. **Hiệu suất tốt hơn**
- Native splash screen hiển thị ngay lập tức khi app khởi động
- Không cần load JavaScript để hiển thị splash screen
- Giảm thời gian "màn hình trắng"

### 2. **Trải nghiệm người dùng mượt mà hơn**
- Splash screen xuất hiện ngay khi tap vào app icon
- Chuyển tiếp mượt mà từ splash sang app content
- Không có delay hoặc flicker

### 3. **Đơn giản hóa code**
- Loại bỏ custom component phức tạp
- Ít state management hơn
- Code dễ maintain hơn

### 4. **Tương thích tốt với platform**
- Tuân theo best practices của iOS và Android
- Tự động adapt với different screen sizes
- Support dark mode và light mode

## Cách hoạt động

### 1. **App Startup Flow:**
```
1. User taps app icon
2. Native splash screen hiển thị ngay lập tức (từ app.config.ts)
3. JavaScript bundle load
4. Fonts load (useFonts hook)
5. Sau 2 giây, gọi SplashScreen.hideAsync()
6. App content hiển thị
```

### 2. **Key Functions:**
- `SplashScreen.preventAutoHideAsync()`: Ngăn splash tự động ẩn
- `SplashScreen.hideAsync()`: Ẩn splash screen khi ready
- `useFonts()`: Load fonts trước khi ẩn splash

## Cấu hình Splash Screen

### Image Requirements:
- **iOS:** 1242x2208px (iPhone 6 Plus resolution)
- **Android:** 1080x1920px (Full HD resolution)
- **Format:** PNG với transparent background
- **Location:** `./src/assets/images/splash.png`

### Background Color:
- Sử dụng màu xanh dương `#1E3A8A` để match với app theme
- Có thể thay đổi trong `app.config.ts`

### Resize Mode:
- `contain`: Giữ aspect ratio, fit trong screen
- `cover`: Fill toàn bộ screen, có thể crop
- `stretch`: Stretch để fill screen

## Testing

### 1. **Development:**
```bash
expo start
# Splash screen sẽ hiển thị khi app khởi động
```

### 2. **Production Build:**
```bash
eas build --profile preview --platform ios
# Test trên device thực để thấy native splash screen
```

## Troubleshooting

### 1. **Splash không hiển thị:**
- Kiểm tra path của image trong `app.config.ts`
- Đảm bảo image tồn tại trong `src/assets/images/`
- Rebuild app sau khi thay đổi config

### 2. **Splash hiển thị quá lâu:**
- Kiểm tra `SplashScreen.hideAsync()` được gọi
- Đảm bảo fonts load thành công
- Check console logs for errors

### 3. **Splash không match design:**
- Thay đổi `backgroundColor` trong config
- Tạo image mới với correct dimensions
- Thử different `resizeMode`

## Migration Notes

### Files đã thay đổi:
- ✅ `src/app/_layout.tsx` - Updated to use Expo SplashScreen
- ✅ `app.config.ts` - Added splash configuration

### Files có thể xóa (nếu không dùng ở nơi khác):
- `src/features/splash/components/SplashScreen.tsx` - Custom component
- Related styles và animations

### Dependencies:
- ✅ `expo-splash-screen` - Đã có trong package.json
- Không cần install thêm packages

## 🎬 Lottie Integration (NEW)

### Hybrid Approach Implementation:
1. **Native Splash (Instant):** Static image hiển thị ngay
2. **Lottie Splash (After JS):** Animated splash với Lottie files

### New Components:
- `LottieSplashScreen.tsx` - Main Lottie splash component
- `LottieSplashDemo.tsx` - Demo component để test animations

### Available Lottie Files:
- `welcome_1.json` - Main welcome animation
- `welcome_2.json` - Alternative welcome
- `scan_2.json` - Scanning animation
- `document_2.json` - Document animation
- `qr-code.json` - QR code animation

### Features:
- ✅ Gradient background với brand colors
- ✅ Main Lottie animation với auto-play
- ✅ Loading indicator animation (loop)
- ✅ Text overlay với shadows
- ✅ Responsive design
- ✅ Smooth transitions
- ✅ Performance optimized

### Usage:
```typescript
<LottieSplashScreen
  onAnimationFinish={handleAnimationFinish}
/>
```

## Best Practices

### 1. **Timing:**
- Native splash: Instant display
- Lottie splash: 2-3 seconds animation
- Total splash time: 3-4 seconds max

### 2. **Design:**
- Use consistent brand colors
- Keep animations simple and focused
- Ensure readability on all devices
- Test on different screen sizes

### 3. **Performance:**
- Keep Lottie files under 100KB
- Use cacheComposition for better performance
- Test on low-end devices
- Optimize animation complexity
