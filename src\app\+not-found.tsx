import { Link, Stack } from 'expo-router';
import { StyleSheet } from 'react-native';
import { Text, View } from 'react-native-ui-lib';
import React from 'react';
import LottieView from 'lottie-react-native';
import { ROUTES } from '@/core/constants/routes';

export default function NotFoundScreen() {
  return (
    <>
      <Stack.Screen options={{ title: 'Đang phát triển' }} />
      <View style={styles.container}>
        <LottieView
          autoPlay
          loop
          style={styles.animation}
          source={require('@assets/lottie/page_not_found.json')}
        />
        <Text style={styles.title}>Chức năng đang phát triển</Text>
        <Text style={styles.description}>
          Tính năng này hiện chưa khả dụng. Vui lòng quay lại sau nhé!
        </Text>

        <Link href={ROUTES.AUTH.SIGN_IN}>
          <View style={styles.button}>
            <Text style={styles.buttonText}>V<PERSON> trang chủ</Text>
          </View>
        </Link>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  animation: {
    width: 180,
    height: 180,
    marginBottom: 30,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    color: '#333',
    textAlign: 'center',
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 25,
  },
  button: {
    backgroundColor: '#2e78b7',
    paddingVertical: 12,
    paddingHorizontal: 25,
    borderRadius: 8,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});
