import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text } from 'react-native-ui-lib';
import { Ionicons } from '@expo/vector-icons';
import { useRouter, usePathname } from 'expo-router';

const BOTTOM_ITEMS = [
  {
    name: 'Apps',
    icon: 'apps',
    route: '/(dashboard)/apps-dashboard',
  },
  {
    name: 'Scan',
    icon: 'scan',
    route: '/(dashboard)/scan',
  },
  {
    name: 'Settings',
    icon: 'settings',
    route: '/(dashboard)/settings',
  },
];

export default function BottomBar() {
  const router = useRouter();
  const pathname = usePathname();

  return (
    <View style={styles.container}>
      {BOTTOM_ITEMS.map((item) => {
        const isActive = pathname === item.route;
        return (
          <TouchableOpacity
            key={item.name}
            style={styles.item}
            onPress={() => router.push(item.route)}
          >
            <Ionicons
              name={item.icon as any}
              size={24}
              color={isActive ? '#4B55E1' : '#666'}
            />
            <Text
              style={[
                styles.label,
                { color: isActive ? '#4B55E1' : '#666' },
              ]}
            >
              {item.name}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  item: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  label: {
    fontSize: 12,
    marginTop: 4,
  },
}); 