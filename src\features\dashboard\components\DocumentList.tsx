import React, { useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import { Text } from 'react-native-ui-lib';
import { Ionicons } from '@expo/vector-icons';
import { useScan } from '@features/scan/context/ScanContext';

interface DocumentListProps {
  documents: Array<{
    id: string;
    title: string;
    createdAt: number;
    images: string[];
  }>;
  onDocumentPress: (id: string) => void;
  onRefresh: () => void;
  refreshing: boolean;
}

export default function DocumentList({
  documents,
  onDocumentPress,
  onRefresh,
  refreshing,
}: DocumentListProps) {
  const renderItem = useCallback(
    ({ item }: { item: DocumentListProps['documents'][0] }) => (
      <TouchableOpacity
        style={styles.documentItem}
        onPress={() => onDocumentPress(item.id)}
      >
        <View style={styles.documentIcon}>
          <Ionicons name="document-text" size={24} color="#4B55E1" />
        </View>
        <View style={styles.documentInfo}>
          <Text style={styles.documentTitle} numberOfLines={1}>
            {item.title}
          </Text>
          <Text style={styles.documentMeta}>
            {new Date(item.createdAt).toLocaleDateString()} • {item.images.length} ảnh
          </Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color="#666" />
      </TouchableOpacity>
    ),
    [onDocumentPress]
  );

  const keyExtractor = useCallback((item: DocumentListProps['documents'][0]) => item.id, []);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Tài liệu gần đây</Text>
      <FlatList
        data={documents}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        initialNumToRender={10}
        maxToRenderPerBatch={10}
        windowSize={5}
        removeClippedSubviews={true}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="document-text-outline" size={48} color="#666" />
            <Text style={styles.emptyText}>Chưa có tài liệu nào</Text>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 12,
  },
  listContent: {
    paddingBottom: 16,
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#222',
    borderRadius: 12,
    padding: 12,
    marginBottom: 8,
  },
  documentIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(75, 85, 225, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  documentInfo: {
    flex: 1,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 4,
  },
  documentMeta: {
    fontSize: 12,
    color: '#999',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginTop: 8,
  },
}); 