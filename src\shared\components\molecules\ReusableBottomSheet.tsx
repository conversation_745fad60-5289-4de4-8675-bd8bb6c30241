import React, {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useRef,
  useMemo,
} from 'react';
import {
  BottomSheetModal,
  BottomSheetModalProps,
  BottomSheetView,
  BottomSheetBackdrop,
} from '@gorhom/bottom-sheet';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';

export type ReusableBottomSheetRef = {
  present: () => void;
  close: () => void;
};

interface Props extends Partial<BottomSheetModalProps> {
  title?: string;
  children: React.ReactNode;
  snapPoints?: (string | number)[];
  defaultSnapPoint?: string;
  defaultIndex?: number;
}

const ReusableBottomSheet = forwardRef<ReusableBottomSheetRef, Props>(
  (
    { children, title = 'Details', snapPoints, defaultIndex, ...props },
    ref,
  ) => {
    const modalRef = useRef<BottomSheetModal>(null);
    const snapPointsMemo = useMemo(() => ['50%', '75%', '90%'], [snapPoints]);
    const present = useCallback(() => {
      modalRef.current?.present();
    }, []);

    const close = useCallback(() => {
      modalRef.current?.dismiss();
    }, []);

    useImperativeHandle(ref, () => ({
      present,
      close,
    }));

    return (
      <BottomSheetModal
        ref={modalRef}
        index={defaultIndex}
        snapPoints={snapPointsMemo}
        enablePanDownToClose
        enableDismissOnClose
        enableDynamicSizing
        enableBlurKeyboardOnGesture
        backgroundStyle={styles.modalBackground}
        handleIndicatorStyle={styles.indicator}
        backdropComponent={props => (
          <BottomSheetBackdrop
            {...props}
            disappearsOnIndex={-1}
            appearsOnIndex={0}
            opacity={0.4}
            pressBehavior='close'
          />
        )}
        {...props}
      >
        <BottomSheetView style={styles.content}>
          <View style={styles.header}>
            <View></View>
            <Text style={styles.title}>{title}</Text>
            <TouchableOpacity onPress={close}>
              <Text style={styles.closeText}>×</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.body}>{children}</View>
        </BottomSheetView>
      </BottomSheetModal>
    );
  },
);

const styles = StyleSheet.create({
  modalBackground: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    backgroundColor: '#fff',
  },
  indicator: {
    backgroundColor: '#ccc',
    width: 40,
    height: 4,
  },
  backdrop: {
    backgroundColor: 'rgba(0, 0, 0, 0.4)', // Mờ 40%
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderColor: '#ddd',
  },
  title: {
    fontSize: 17,
    fontWeight: 'bold',
    color: '#333',
  },
  closeText: {
    fontSize: 24,
    lineHeight: 24,
    color: '#888',
  },
  body: {
    flex: 1,
    paddingTop: 12,
  },
});

export default ReusableBottomSheet;
