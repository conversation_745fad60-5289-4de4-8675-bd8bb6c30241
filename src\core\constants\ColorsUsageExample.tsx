import React from 'react';
import { View, Text } from 'react-native';
import { Colors } from 'react-native-ui-lib';

/**
 * V<PERSON> dụ cách sử dụng React Native UI Lib Colors
 *
 * Có 2 cách chính:
 * 1. Sử dụng design tokens (k<PERSON><PERSON>ến nghị)
 * 2. Sử dụng modifiers (tiện lợi)
 */

export const ColorsUsageExample = () => {
  return (
    <View>
      {/* Cách 1: Sử dụng design tokens trực tiếp */}
      <View style={{ backgroundColor: Colors.$backgroundPrimary }}>
        <Text style={{ color: Colors.$textInverted }}>
          Primary Background với Inverted Text
        </Text>
      </View>

      {/* Cách 2: Sử dụng modifiers (tiện lợi hơn) */}
      <View bg-$backgroundSuccess>
        <Text $textInverted>
          Success Background với Inverted Text (sử dụng modifiers)
        </Text>
      </View>

      {/* Cách 3: <PERSON>ế<PERSON> hợp với style */}
      <View
        style={{
          backgroundColor: Colors.$backgroundWarning,
          padding: 16,
          borderRadius: 8,
        }}
      >
        <Text style={{ color: Colors.$textDefault }}>
          Warning Background với Default Text
        </Text>
      </View>

      {/* Cách 4: Sử dụng custom colors */}
      <View style={{ backgroundColor: Colors.buttonPrimary }}>
        <Text style={{ color: Colors.white }}>Custom Button Color</Text>
      </View>
    </View>
  );
};

/**
 * CÁC DESIGN TOKENS CÓ SẴN:
 *
 * Background Tokens:
 * - $backgroundDefault: Background mặc định
 * - $backgroundElevated: Background nổi
 * - $backgroundNeutral: Background trung tính
 * - $backgroundPrimary: Background chính
 * - $backgroundSuccess: Background thành công
 * - $backgroundWarning: Background cảnh báo
 * - $backgroundDanger: Background nguy hiểm
 * - $backgroundDisabled: Background vô hiệu hóa
 *
 * Text Tokens:
 * - $textDefault: Text mặc định
 * - $textNeutral: Text trung tính
 * - $textPrimary: Text chính
 * - $textSuccess: Text thành công
 * - $textWarning: Text cảnh báo
 * - $textDanger: Text nguy hiểm
 * - $textDisabled: Text vô hiệu hóa
 * - $textInverted: Text đảo ngược
 *
 * Icon Tokens:
 * - $iconDefault: Icon mặc định
 * - $iconNeutral: Icon trung tính
 * - $iconPrimary: Icon chính
 * - $iconSuccess: Icon thành công
 * - $iconWarning: Icon cảnh báo
 * - $iconDanger: Icon nguy hiểm
 * - $iconDisabled: Icon vô hiệu hóa
 * - $iconInverted: Icon đảo ngược
 *
 * Outline Tokens:
 * - $outlineDefault: Viền mặc định
 * - $outlineNeutral: Viền trung tính
 * - $outlinePrimary: Viền chính
 * - $outlineSuccess: Viền thành công
 * - $outlineWarning: Viền cảnh báo
 * - $outlineDanger: Viền nguy hiểm
 * - $outlineDisabled: Viền vô hiệu hóa
 *
 * CUSTOM COLORS:
 * - buttonPrimary: '#1E3AAF'
 * - buttonSecondary: '#4CAF50'
 * - buttonBorder: '#FFFFFF50'
 * - tintColor: '#E6B9A6'
 * - gray100, gray500, gray900: Legacy colors
 * - black, white, grey, blue: Legacy colors
 */

/**
 * MIGRATION GUIDE - HƯỚNG DẪN CHUYỂN ĐỔI:
 *
 * TỪ:
 * Colors.light.background -> Colors.$backgroundDefault
 * Colors.light.text -> Colors.$textDefault
 * Colors.light.primary -> Colors.$backgroundPrimary
 *
 * SANG:
 * Sử dụng modifiers:
 * <View bg-$backgroundDefault>
 * <Text $textDefault>
 * <View bg-$backgroundPrimary>
 *
 * HOẶC:
 * Sử dụng trực tiếp:
 * Colors.$backgroundDefault
 * Colors.$textDefault
 * Colors.$backgroundPrimary
 */
