import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Image,
} from 'react-native';
import { useRouter } from 'expo-router';
import { <PERSON><PERSON><PERSON>, Wizard } from 'react-native-ui-lib';
import { ROUTES } from '@/core/constants/routes';

const APPS = [
  {
    id: 'scan',
    name: '<PERSON><PERSON>t Tà<PERSON>',
    icon: { uri: 'https://img.icons8.com/color/96/document--v1.png' },
  },
  {
    id: 'sign',
    name: '<PERSON><PERSON>',
    icon: { uri: 'https://img.icons8.com/color/96/signature.png' },
  },
  {
    id: 'manage',
    name: '<PERSON><PERSON>',
    icon: { uri: 'https://img.icons8.com/color/96/folder-invoices--v1.png' },
  },
  {
    id: 'crm',
    name: 'CRM',
    icon: { uri: 'https://img.icons8.com/color/96/conference-call.png' },
  },
  {
    id: 'accounting',
    name: '<PERSON><PERSON>',
    icon: { uri: 'https://img.icons8.com/color/96/accounting.png' },
  },
  {
    id: 'hrm',
    name: 'Nhân Sự',
    icon: {
      uri: 'https://img.icons8.com/?size=100&id=79lZXUdA8mYs&format=png&color=000000',
    },
  },
  {
    id: 'inventory',
    name: 'Kho',
    icon: { uri: 'https://img.icons8.com/color/96/warehouse.png' },
  },
  {
    id: 'sales',
    name: 'Bán Hàng',
    icon: { uri: 'https://img.icons8.com/color/96/sales-performance.png' },
  },
  {
    id: 'support',
    name: 'Hỗ Trợ',
    icon: { uri: 'https://img.icons8.com/color/96/customer-support.png' },
  },
];

export default function AppSelectionScreen() {
  const router = useRouter();
  const [selectedAppId, setSelectedAppId] = useState<string | null>(null);
  const [activeStep, setActiveStep] = useState(0); // Step hiện tại

  const handleSelect = useCallback((appId: string) => {
    setSelectedAppId(appId);
  }, []);

  const renderItem = ({ item }: { item: (typeof APPS)[0] }) => {
    const isSelected = selectedAppId === item.id;
    return (
      <TouchableOpacity
        style={[styles.item]}
        onPress={() => handleSelect(item.id)}
      >
        <View style={styles.iconContainer}>
          <Image source={item.icon} style={styles.icon} />
          {isSelected && (
            <View style={styles.checkMark}>
              <Text style={styles.checkText}>✓</Text>
            </View>
          )}
        </View>
        <Text style={styles.label}>{item.name}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <Wizard activeIndex={activeStep} containerStyle={styles.wizard}>
        <Wizard.Step label='Chọn ứng dụng' state={Wizard.States.ENABLED} />
        <Wizard.Step label='Chọn chức năng' state={Wizard.States.ENABLED} />
      </Wizard>
      <FlatList
        data={APPS}
        numColumns={3}
        keyExtractor={item => item.id}
        renderItem={renderItem}
        contentContainerStyle={{ paddingBottom: 30 }}
      />

      <ActionBar
        actions={[
          { label: 'Bỏ qua', onPress: () => console.log('Skip') },
          {
            label: 'Tiếp tục',
            onPress: () => {
              if (selectedAppId) {
                router.push({
                  pathname: ROUTES.FEATURE_SELECTION,
                  params: { appId: selectedAppId },
                });
              } else {
                console.log('Vui lòng chọn ứng dụng!');
              }
            },
            disabled: !selectedAppId,
          },
        ]}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  wizard: {
    marginBottom: 25,
    backgroundColor: '',
    paddingHorizontal: '15%',
  },
  container: {
    flex: 1,
    paddingTop: '15%',
    paddingHorizontal: 30,
    backgroundColor: '#fff',
  },

  title: {
    fontSize: 22,
    fontWeight: '500',
    color: '#333',
  },
  headerIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
    gap: 5,
  },
  item: {
    flex: 1 / 3,
    alignItems: 'center',
    marginBottom: 24,
    borderRadius: 10,
    overflow: 'hidden',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#eef1ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    position: 'relative',
  },
  icon: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
  label: {
    fontSize: 14,
    color: '#444',
    textAlign: 'center',
  },
  checkMark: {
    position: 'absolute',
    top: 4,
    right: 4,
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: '#4B55E1',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkText: {
    color: '#fff',
    fontSize: 12,
  },
});
