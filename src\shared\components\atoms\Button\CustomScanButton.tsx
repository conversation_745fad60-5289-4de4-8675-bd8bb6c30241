import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native-ui-lib';
import { FontAwesome6 } from '@expo/vector-icons';
import FloatingActionBar from '@molecules/FloatingActionBar/FloatingActionBar';
import { useScan } from '@features/scan/context/ScanContext';
import { Colors } from 'react-native-ui-lib';

interface Props {
  onPress: () => void;
}

const CustomScanButton: React.FC<Props> = ({ onPress }) => (
  <View
    style={{
      position: 'absolute',
      alignItems: 'center',
      justifyContent: 'center',
      width: 120,
      height: 90,
      left: '50%',
      marginLeft: -60,
    }}
  >
    <TouchableOpacity
      onPress={onPress}
      style={{
        backgroundColor: '#4B55E1',
        height: 50,
        width: 120,
        borderRadius: 12,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOpacity: 0.2,
        shadowRadius: 12,
        shadowOffset: { width: 0, height: 2 },
      }}
    >
      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
        <FontAwesome6 name='add' size={16} color='white' />
        <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 14 }}>
          SCAN
        </Text>
      </View>
    </TouchableOpacity>
  </View>
);

export default CustomScanButton;
