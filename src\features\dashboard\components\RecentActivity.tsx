import React from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import { Text } from 'react-native-ui-lib';
import { Ionicons } from '@expo/vector-icons';

interface Activity {
  id: string;
  type: 'scan' | 'share' | 'delete';
  title: string;
  description: string;
  timestamp: string;
}

const ACTIVITIES: Activity[] = [
  {
    id: '1',
    type: 'scan',
    title: 'Document Scanned',
    description: 'Invoice.pdf was scanned',
    timestamp: '2 hours ago',
  },
  {
    id: '2',
    type: 'share',
    title: 'Files Shared',
    description: '3 files were shared with <PERSON>',
    timestamp: '5 hours ago',
  },
  {
    id: '3',
    type: 'delete',
    title: 'Files Deleted',
    description: '5 old documents were removed',
    timestamp: '1 day ago',
  },
];

export default function RecentActivity() {
  const getIcon = (type: Activity['type']) => {
    switch (type) {
      case 'scan':
        return 'scan';
      case 'share':
        return 'share';
      case 'delete':
        return 'trash';
      default:
        return 'document';
    }
  };

  const getIconColor = (type: Activity['type']) => {
    switch (type) {
      case 'scan':
        return '#007AFF';
      case 'share':
        return '#34C759';
      case 'delete':
        return '#FF3B30';
      default:
        return '#666';
    }
  };

  const renderItem = ({ item }: { item: Activity }) => (
    <View style={styles.activityItem}>
      <View
        style={[
          styles.iconContainer,
          { backgroundColor: getIconColor(item.type) + '20' },
        ]}
      >
        <Ionicons
          name={getIcon(item.type) as any}
          size={20}
          color={getIconColor(item.type)}
        />
      </View>
      <View style={styles.activityContent}>
        <Text style={styles.activityTitle}>{item.title}</Text>
        <Text style={styles.activityDescription}>{item.description}</Text>
      </View>
      <Text style={styles.timestamp}>{item.timestamp}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Recent Activity</Text>
      <FlatList
        data={ACTIVITIES}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        scrollEnabled={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    margin: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 16,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 4,
  },
  activityDescription: {
    fontSize: 14,
    color: '#666',
  },
  timestamp: {
    fontSize: 12,
    color: '#999',
  },
}); 