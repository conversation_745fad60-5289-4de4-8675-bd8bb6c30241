import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-ui-lib';
import { Ionicons } from '@expo/vector-icons';

interface StorageUsageProps {
  used: number;
  total: number;
}

export default function StorageUsage({ used, total }: StorageUsageProps) {
  const percentage = (used / total) * 100;
  const formattedUsed = formatSize(used);
  const formattedTotal = formatSize(total);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Storage Usage</Text>
        <View style={styles.storageInfo}>
          <Ionicons name='hardware-chip-outline' size={20} color='#666' />
          <Text style={styles.storageText}>
            {formattedUsed} of {formattedTotal} used
          </Text>
        </View>
      </View>
      <View style={styles.progressContainer}>
        <View style={styles.progressBackground}>
          <View
            style={[
              styles.progressFill,
              { width: `${percentage}%` },
              percentage > 90 && styles.progressWarning,
            ]}
          />
        </View>
        <Text style={styles.percentage}>{Math.round(percentage)}%</Text>
      </View>
    </View>
  );
}

function formatSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    margin: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
  },
  storageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  storageText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBackground: {
    flex: 1,
    height: 8,
    backgroundColor: '#F2F2F7',
    borderRadius: 4,
    marginRight: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#34C759',
    borderRadius: 4,
  },
  progressWarning: {
    backgroundColor: '#FF3B30',
  },
  percentage: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#000',
    width: 40,
    textAlign: 'right',
  },
});
