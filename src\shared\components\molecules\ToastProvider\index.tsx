import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Toast, {
  BaseToast,
  ErrorToast,
  ToastConfig,
} from 'react-native-toast-message';
import { useTheme } from '@core/theme/theme';

const { width, height } = Dimensions.get('window');

const CustomToast: React.FC<{
  type: 'success' | 'error' | 'info';
  text1?: string;
  text2?: string;
  isShowCloseButton?: boolean;
  onPress?: () => void;
  onPressClose?: () => void;
}> = ({ type, text1, text2, onPress, isShowCloseButton, onPressClose }) => {
  const { colors } = useTheme();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(-150)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const getToastConfig = () => {
    switch (type) {
      case 'success':
        return {
          backgroundColor: '#4CAF50',
          icon: 'checkmark-circle',
          iconColor: '#FFFFFF',
          borderColor: '#45A049',
        };
      case 'error':
        return {
          backgroundColor: '#F44336',
          icon: 'close-circle',
          iconColor: '#FFFFFF',
          borderColor: '#D32F2F',
        };
      case 'info':
        return {
          backgroundColor: '#2196F3',
          icon: 'information-circle',
          iconColor: '#FFFFFF',
          borderColor: '#1976D2',
        };
      default:
        return {
          backgroundColor: colors.primary,
          icon: 'information-circle',
          iconColor: '#FFFFFF',
          borderColor: colors.primary,
        };
    }
  };

  const config = getToastConfig();

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: config.backgroundColor,
          borderColor: config.borderColor,
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      <TouchableOpacity
        style={styles.content}
        onPress={onPress}
        activeOpacity={0.8}
      >
        <View style={styles.iconContainer}>
          <Ionicons
            name={config.icon as any}
            size={24}
            color={config.iconColor}
          />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.title}>{text1 || 'Thông báo'}</Text>
          {text2 && <Text style={styles.message}>{text2}</Text>}
        </View>
        {isShowCloseButton && (
          <TouchableOpacity style={styles.closeButton} onPress={onPressClose}>
            <Ionicons name='close' size={20} color='rgba(255, 255, 255, 0.8)' />
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

const toastConfig: ToastConfig = {
  success: props => <CustomToast {...props} type='success' />,
  error: props => <CustomToast {...props} type='error' />,
  info: props => <CustomToast {...props} type='info' />,
};

const ToastProvider: React.FC = () => {
  return <Toast config={toastConfig} />;
};

const styles = StyleSheet.create({
  container: {
    width: width * 0.95,
    marginHorizontal: 16,
    borderRadius: 16,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 10,
    marginBottom: 20,
    marginTop: 20,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 2,
  },
  message: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 20,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
});

export default ToastProvider;
