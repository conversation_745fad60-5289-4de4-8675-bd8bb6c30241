// Token usage calculation utilities

/**
 * Calculates how many documents can be scanned with the available tokens
 * @param tokenBalance Current token balance
 * @param tokenCostPerScan Token cost per scan
 */
export const calculateScansAvailable = (tokenBalance: number, tokenCostPerScan: number): number => {
    if (tokenCostPerScan <= 0) return 0; // Prevent division by zero
    return Math.floor(tokenBalance / tokenCostPerScan);
};

/**
 * Formats a token balance with appropriate text
 * @param balance The token balance to format
 */
export const formatTokenBalance = (balance: number): string => {
    return balance.toLocaleString('vi-VN');
};

/**
 * Determines if the token balance is low
 * @param balance Current token balance
 * @param threshold Threshold for low balance (as a number of scans)
 * @param costPerScan Cost per scan
 */
export const isLowTokenBalance = (
    balance: number,
    threshold: number = 5,
    costPerScan: number = 2
): boolean => {
    return balance < (threshold * costPerScan);
};

/**
 * Calculates the token savings percentage compared to smaller packages
 * @param largerPackage The larger token package
 * @param smallerPackage The smaller token package for comparison
 */
export const calculateTokenSavings = (
    largerPackageAmount: number,
    largerPackagePrice: number,
    smallerPackageAmount: number,
    smallerPackagePrice: number
): number => {
    // Calculate cost per token for each package
    const largerCostPerToken = largerPackagePrice / largerPackageAmount;
    const smallerCostPerToken = smallerPackagePrice / smallerPackageAmount;

    // Calculate savings percentage
    const savingsPercentage = ((smallerCostPerToken - largerCostPerToken) / smallerCostPerToken) * 100;

    return Math.round(savingsPercentage);
};

/**
 * Formats token price with proper currency symbol
 * @param price The price to format
 */
export const formatTokenPrice = (price: number): string => {
    return `${price.toLocaleString('vi-VN')} ₫`;
};

/**
 * Validates if a transaction can be completed based on token balance
 * @param currentBalance Current token balance
 * @param operationCost Cost of the operation in tokens
 */
export const canCompleteTransaction = (currentBalance: number, operationCost: number): boolean => {
    return currentBalance >= operationCost;
};

/**
 * Returns a message for insufficient tokens
 * @param operationType The type of operation attempted
 * @param requiredTokens The number of tokens required
 */
export const getInsufficientTokensMessage = (
    operationType: 'scan' | 'ocr' | 'storage',
    requiredTokens: number
): string => {
    const operationTypeText = {
        scan: 'quét tài liệu',
        ocr: 'nhận dạng văn bản',
        storage: 'lưu trữ tài liệu'
    };

    return `Bạn không đủ token để ${operationTypeText[operationType]}. Cần thêm ${requiredTokens} token.`;
}; 