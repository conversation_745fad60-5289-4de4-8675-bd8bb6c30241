import React from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { useDuAnQuery } from '../hooks/useDuAnQuery';
import { DuAn } from '../services/duanService';
import { useProfile } from '@features/auth/hooks/useAuth';

interface DuAnListProps {
  donViId?: string;
}

/**
 * Component to display a list of dự án (projects)
 */
export const DuAnList: React.FC<DuAnListProps> = ({ donViId }) => {
  // Lấy thông tin profile của user đăng nhập
  const { data: userProfile } = useProfile();

  // Sử dụng DonViID từ profile, nếu không thì dùng donViId từ props
  const userDonViId = userProfile?.DonViID;
  const effectiveDonViId = donViId || userDonViId || '';

  const { data, isLoading, isError, error } = useDuAnQuery({
    donViId: effectiveDonViId,
    enabled: !!effectiveDonViId,
  });

  if (!effectiveDonViId) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>Chưa có thông tin đơn vị</Text>
      </View>
    );
  }

  if (isLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size='large' color='#0000ff' />
        <Text style={styles.loadingText}>Đang tải dự án...</Text>
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>Lỗi: {(error as Error).message}</Text>
      </View>
    );
  }

  if (!data || !data.Items || data.Items.length === 0) {
    return (
      <View style={styles.centered}>
        <Text style={styles.emptyText}>Không có dự án nào</Text>
      </View>
    );
  }

  const renderItem = ({ item }: { item: DuAn }) => (
    <View style={styles.item}>
      <Text style={styles.title}>{item.TenDuAn}</Text>
      <Text style={styles.id}>ID: {item.DuAnId}</Text>
      {item.MaDuAn ? <Text style={styles.code}>Mã: {item.MaDuAn}</Text> : null}
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.header}>Danh sách dự án</Text>
      <Text style={styles.subheader}>Tổng số: {data.TotalCount}</Text>
      <FlatList
        data={data.Items}
        renderItem={renderItem}
        keyExtractor={item => item.DuAnId}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  header: {
    fontSize: 22,
    fontWeight: 'bold',
    marginTop: 16,
    marginHorizontal: 16,
    color: '#333',
  },
  subheader: {
    fontSize: 14,
    marginHorizontal: 16,
    marginBottom: 16,
    color: '#666',
  },
  listContent: {
    padding: 8,
  },
  item: {
    backgroundColor: 'white',
    padding: 16,
    marginHorizontal: 8,
    marginBottom: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  id: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  code: {
    fontSize: 12,
    color: '#666',
  },
  loadingText: {
    marginTop: 8,
    color: '#666',
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
  },
  emptyText: {
    color: '#666',
    textAlign: 'center',
  },
});
