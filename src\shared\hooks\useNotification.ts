import { useCallback } from 'react';
import { NotificationService } from '../services/NotificationService';

export function useNotification() {
    const sendNotification = useCallback(
        async (title: string, body: string, type: 'basic' | 'premium' = 'basic') => {
            try {
                const service = NotificationService.getInstance();
                await service.configure();
                await service.sendNotification({ title, body, type });
            } catch (err) {
                console.error('Notification error:', err);
            }
        },
        []
    );

    return { sendNotification };
}