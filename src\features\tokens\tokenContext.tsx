import React, { createContext, useState, useContext, ReactNode } from 'react';

// Types for token operations
type TokenOperation = {
  id: string;
  date: string;
  type: 'purchase' | 'scan' | 'ocr' | 'storage';
  amount: number;
  document?: string;
  paymentMethod?: string;
};

// Types for token costs
type TokenCosts = {
  scanDocument: number;
  ocrProcessing: number;
  cloudStorage: number;
};

// Types for token packages
type TokenPackage = {
  id: string;
  amount: number;
  price: string;
  discount: string;
};

// Context value type
type TokenContextValue = {
  tokenBalance: number;
  tokenHistory: TokenOperation[];
  tokenCosts: TokenCosts;
  tokenPackages: TokenPackage[];
  // Actions
  addTokens: (amount: number, paymentMethod: string) => void;
  useTokens: (amount: number, operationType: 'scan' | 'ocr' | 'storage', documentName?: string) => boolean;
  checkSufficientTokens: (amount: number) => boolean;
};

// Create the context with default values
export const TokenContext = createContext<TokenContextValue>({
  tokenBalance: 0,
  tokenHistory: [],
  tokenCosts: {
    scanDocument: 2,
    ocrProcessing: 5,
    cloudStorage: 1
  },
  tokenPackages: [],
  addTokens: () => {},
  useTokens: () => false,
  checkSufficientTokens: () => false
});

// Provider props type
type TokenProviderProps = {
  children: ReactNode;
};

// Default token packages
const defaultTokenPackages: TokenPackage[] = [
  { id: '1', amount: 50, price: '49.000 ₫', discount: '0%' },
  { id: '2', amount: 100, price: '89.000 ₫', discount: '10%' },
  { id: '3', amount: 300, price: '249.000 ₫', discount: '15%' },
  { id: '4', amount: 1000, price: '799.000 ₫', discount: '20%' },
];

// Token provider component
export const TokenProvider: React.FC<TokenProviderProps> = ({ children }) => {
  const [tokenBalance, setTokenBalance] = useState<number>(150);
  const [tokenHistory, setTokenHistory] = useState<TokenOperation[]>([
    { id: '1', date: '25/07/2023', type: 'scan', amount: -2, document: 'Tài liệu ATTP' },
    { id: '2', date: '24/07/2023', type: 'scan', amount: -2, document: 'Báo cáo quý 2' },
    { id: '3', date: '23/07/2023', type: 'purchase', amount: 100, paymentMethod: 'Thẻ tín dụng' }
  ]);
  
  const [tokenCosts] = useState<TokenCosts>({
    scanDocument: 2,
    ocrProcessing: 5,
    cloudStorage: 1
  });
  
  const [tokenPackages] = useState<TokenPackage[]>(defaultTokenPackages);

  // Add tokens (purchase)
  const addTokens = (amount: number, paymentMethod: string) => {
    setTokenBalance(prev => prev + amount);
    
    // Add to history
    const newOperation: TokenOperation = {
      id: Date.now().toString(),
      date: new Date().toLocaleDateString('vi-VN'),
      type: 'purchase',
      amount: amount,
      paymentMethod
    };
    
    setTokenHistory(prev => [newOperation, ...prev]);
  };

  // Use tokens for operations
  const useTokens = (amount: number, operationType: 'scan' | 'ocr' | 'storage', documentName?: string): boolean => {
    if (tokenBalance < amount) {
      return false; // Not enough tokens
    }
    
    setTokenBalance(prev => prev - amount);
    
    // Add to history
    const newOperation: TokenOperation = {
      id: Date.now().toString(),
      date: new Date().toLocaleDateString('vi-VN'),
      type: operationType,
      amount: -amount,
      document: documentName
    };
    
    setTokenHistory(prev => [newOperation, ...prev]);
    return true;
  };

  // Check if user has sufficient tokens
  const checkSufficientTokens = (amount: number): boolean => {
    return tokenBalance >= amount;
  };

  return (
    <TokenContext.Provider 
      value={{ 
        tokenBalance, 
        tokenHistory, 
        tokenCosts,
        tokenPackages,
        addTokens, 
        useTokens,
        checkSufficientTokens
      }}
    >
      {children}
    </TokenContext.Provider>
  );
};

// Custom hook to use the token context
export const useTokens = () => useContext(TokenContext); 