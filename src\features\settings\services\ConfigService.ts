import { SettingsRepository } from '@core/database/repositories/settingsRepository';

// Khóa cài đặt trong SQLite
const CONFIG_KEYS = {
  DOCUMENT_TYPE: 'document_type',
  PROJECT_ID: 'project_id',
  PROJECT_NAME: 'project_name',
};

export interface ProjectConfig {
  id: string;
  name: string;
}

export interface DocumentTypeConfig {
  id: string;
  name: string;
}

export class ConfigService {
  private settingsRepository: SettingsRepository;

  constructor() {
    this.settingsRepository = new SettingsRepository();
  }

  // <PERSON><PERSON>u cấu hình dự án
  async saveProjectConfig(project: ProjectConfig): Promise<void> {
    await this.settingsRepository.setSetting(
      CONFIG_KEYS.PROJECT_ID,
      project.id,
    );
    await this.settingsRepository.setSetting(
      CONFIG_KEYS.PROJECT_NAME,
      project.name,
    );

    console.log(`<PERSON><PERSON> lưu cấu hình dự án: ${project.name} (${project.id})`);
  }

  // L<PERSON>y cấu hình dự án
  async getProjectConfig(): Promise<ProjectConfig | null> {
    const projectId = await this.settingsRepository.getSetting(
      CONFIG_KEYS.PROJECT_ID,
    );
    const projectName = await this.settingsRepository.getSetting(
      CONFIG_KEYS.PROJECT_NAME,
    );

    if (!projectId) return null;

    return {
      id: projectId,
      name: projectName || 'Dự án không tên',
    };
  }

  // Lưu loại tài liệu
  async saveDocumentType(
    documentType: string,
    documentName?: string,
  ): Promise<void> {
    await this.settingsRepository.setSetting(
      CONFIG_KEYS.DOCUMENT_TYPE,
      documentType,
    );

    // Lưu tên loại tài liệu nếu được cung cấp
    if (documentName) {
      await this.settingsRepository.setSetting(
        `${CONFIG_KEYS.DOCUMENT_TYPE}_name`,
        documentName,
      );
    }

    console.log(`Đã lưu loại tài liệu: ${documentType}`);
  }

  // Lấy loại tài liệu
  async getDocumentType(): Promise<string | null> {
    return await this.settingsRepository.getSetting(CONFIG_KEYS.DOCUMENT_TYPE);
  }

  // Lấy chi tiết loại tài liệu
  async getDocumentTypeDetail(): Promise<DocumentTypeConfig | null> {
    const typeId = await this.settingsRepository.getSetting(
      CONFIG_KEYS.DOCUMENT_TYPE,
    );

    if (!typeId) return null;

    const typeName = await this.settingsRepository.getSetting(
      `${CONFIG_KEYS.DOCUMENT_TYPE}_name`,
    );

    return {
      id: typeId,
      name: typeName || typeId,
    };
  }

  // Xóa cấu hình dự án
  async clearProjectConfig(): Promise<void> {
    await this.settingsRepository.deleteSetting(CONFIG_KEYS.PROJECT_ID);
    await this.settingsRepository.deleteSetting(CONFIG_KEYS.PROJECT_NAME);
  }

  // Xóa cấu hình loại tài liệu
  async clearDocumentType(): Promise<void> {
    await this.settingsRepository.deleteSetting(CONFIG_KEYS.DOCUMENT_TYPE);
    await this.settingsRepository.deleteSetting(
      `${CONFIG_KEYS.DOCUMENT_TYPE}_name`,
    );
  }

  // Lấy tất cả cài đặt
  async getAllConfig(): Promise<Record<string, string>> {
    return await this.settingsRepository.getAllSettings();
  }
}
