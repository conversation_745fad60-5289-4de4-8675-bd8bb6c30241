import React, { createContext, useContext, useState } from 'react';
import { LoaderScreen } from 'react-native-ui-lib';

const LoaderContext = createContext<{
  showLoader: (message?: string) => void;
  hideLoader: () => void;
  isLoading: boolean;
  message?: string;
}>({
  showLoader: () => {},
  hideLoader: () => {},
  isLoading: false,
});

export const useLoader = () => useContext(LoaderContext);

export const LoaderProvider = ({ children }: { children: React.ReactNode }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string | undefined>();

  const showLoader = (msg?: string) => {
    setMessage(msg);
    setIsLoading(true);
  };

  const hideLoader = () => {
    setIsLoading(false);
    setMessage(undefined);
  };

  return (
    <LoaderContext.Provider
      value={{ showLoader, hideLoader, isLoading, message }}
    >
      {children}
      {isLoading && <LoaderOverlay message={message} />}
    </LoaderContext.Provider>
  );
};

const LoaderOverlay = ({ message }: { message?: string }) => {
  return <LoaderScreen overlay backgroundColor='rgba(0,0,0,0.25)' />;
};
