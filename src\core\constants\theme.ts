// <PERSON><PERSON><PERSON> hình <PERSON>act Native UI Lib cho dark mode
require('react-native-ui-lib/config').setConfig({ appScheme: 'default' });

// Import Colors để đảm bảo nó được load
import Colors from './Colors';

// Export Colors để sử dụng trong các component khác
export { Colors };

// Legacy COLORS object để tương thích ngược
export const COLORS = {
  // Primary colors
  primary: '#2F4FCD',
  primaryLight: '#4B55E1',
  primaryDark: '#1E3A9A',
  primaryGradient: ['#2F4FCD', '#4B55E1'],

  // Secondary colors
  secondary: '#34C759',
  secondaryLight: '#4CD964',
  secondaryDark: '#2FB350',

  // Background colors
  background: Colors.$backgroundDefault,
  backgroundLight: '#FFFFFF',
  backgroundDark: '#E9ECEF',
  backgroundCard: '#FFFFFF',
  backgroundOverlay: 'rgba(0, 0, 0, 0.5)',
  backgroundElevated: Colors.$backgroundElevated,
  backgroundNeutral: Colors.$backgroundNeutral,
  backgroundPrimary: Colors.$backgroundPrimary,
  backgroundSuccess: Colors.$backgroundSuccess,
  backgroundWarning: Colors.$backgroundWarning,
  backgroundDanger: Colors.$backgroundDanger,

  // Text colors
  text: Colors.$textDefault,
  textLight: '#6C757D',
  textLighter: '#ADB5BD',
  textWhite: '#FFFFFF',
  textMuted: '#868E96',
  textNeutral: Colors.$textNeutral,
  textPrimary: Colors.$textPrimary,
  textSuccess: Colors.$textSuccess,
  textWarning: Colors.$textWarning,
  textDanger: Colors.$textDanger,
  textInverted: Colors.$textInverted,

  // Status colors
  success: '#34C759',
  successLight: '#4CD964',
  successDark: '#2FB350',

  warning: '#FF9500',
  warningLight: '#FFA533',
  warningDark: '#CC7700',

  error: '#FF3B30',
  errorLight: '#FF5E55',
  errorDark: '#CC2F26',

  info: '#007AFF',
  infoLight: '#3395FF',
  infoDark: '#0066CC',

  // Border & Divider
  border: '#E9ECEF',
  borderLight: '#F1F3F5',
  borderDark: '#DEE2E6',
  divider: '#E9ECEF',

  // Overlay
  overlay: 'rgba(0, 0, 0, 0.6)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',
  overlayDark: 'rgba(0, 0, 0, 0.8)',

  // Shadow
  shadow: 'rgba(0, 0, 0, 0.1)',
  shadowLight: 'rgba(0, 0, 0, 0.05)',
  shadowDark: 'rgba(0, 0, 0, 0.2)',

  // Social colors
  facebook: '#1877F2',
  google: '#DB4437',
  apple: '#000000',

  // Icon colors
  icon: Colors.$iconDefault,
  iconNeutral: Colors.$iconNeutral,
  iconPrimary: Colors.$iconPrimary,
  iconSuccess: Colors.$iconSuccess,
  iconWarning: Colors.$iconWarning,
  iconDanger: Colors.$iconDanger,

  // Outline colors
  outline: Colors.$outlineDefault,
  outlineNeutral: Colors.$outlineNeutral,
  outlinePrimary: Colors.$outlinePrimary,
  outlineSuccess: Colors.$outlineSuccess,
  outlineWarning: Colors.$outlineWarning,
  outlineDanger: Colors.$outlineDanger,

  // Legacy colors
  black: Colors.black,
  white: Colors.white,
  grey: Colors.grey,
  blue: Colors.blue,
  tintColor: Colors.tintColor,

  // Button colors
  buttonPrimary: Colors.buttonPrimary,
  buttonSecondary: Colors.buttonSecondary,
  buttonBorder: Colors.buttonBorder,
} as const;

export const FONT_SIZES = {
  xs: 12,
  sm: 14,
  base: 14,
  md: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 30,
  '4xl': 36,
  xxl: 24,
  xxxl: 32,
} as const;

export const FONT_WEIGHTS = {
  light: '300',
  regular: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
} as const;

export const SPACING = {
  xs: 4,
  sm: 8,
  base: 16,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  xxl: 48,
} as const;

export const BORDER_RADIUS = {
  none: 0,
  sm: 4,
  base: 8,
  md: 12,
  lg: 16,
  xl: 24,
  full: 9999,
} as const;

export const SHADOWS = {
  sm: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
  },
  base: {
    shadowColor: COLORS.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
} as const;

export const ANIMATION = {
  duration: {
    fast: 200,
    base: 300,
    slow: 500,
  },
} as const;
