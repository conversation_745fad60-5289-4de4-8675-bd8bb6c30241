# 🎨 Theme System - Hướng dẫn sử dụng

## 📁 Cấu trúc Theme

Dự án có 2 file theme chính:

### 1. **Colors System** (`src/core/constants/Colors.ts`)
- **<PERSON><PERSON><PERSON> đích**: Quản lý màu sắc với design tokens
- **Tính năng**: Dark mode tự động, design tokens, modifiers
- **Sử dụng**: Cho tất cả màu sắc trong app

### 2. **Theme System** (`src/core/theme/theme.tsx`)
- **<PERSON>ục đích**: Quản lý typography, spacing, shadows, component themes
- **Tính năng**: Typography, spacing, border radius, shadows, component themes
- **Sử dụng**: Cho layout, typography, và component styling

## 🚀 Cách sử dụng

### Import Theme

```typescript
// Import từ theme system
import { useTheme, useThemeMode, Colors, Typography, Spacings } from '@core/theme/theme';

// Hoặc import colors trực tiếp
import { Colors } from 'react-native-ui-lib';
```

### Sử dụng Colors (Design Tokens)

```typescript
// Sử dụng modifiers (khuyến nghị)
<View bg-$backgroundPrimary>
<Text $textDefault>
<Button bg-$buttonPrimary $textInverted>

// Sử dụng trực tiếp
<View style={{ backgroundColor: Colors.$backgroundPrimary }}>
<Text style={{ color: Colors.$textDefault }}>
```

### Sử dụng Typography

```typescript
// Sử dụng typography tokens
<Text text-h1>Heading 1</Text>
<Text text-h2>Heading 2</Text>
<Text text-body>Body text</Text>
<Text text-button>Button text</Text>

// Sử dụng trực tiếp
<Text style={{ 
  fontSize: Typography.h1.fontSize,
  fontFamily: Typography.h1.fontFamily,
  fontWeight: Typography.h1.fontWeight 
}}>
  Custom Heading
</Text>
```

### Sử dụng Spacing

```typescript
// Sử dụng spacing tokens
<View padding-s4>
<View margin-s2>
<View paddingHorizontal-s3>

// Sử dụng trực tiếp
<View style={{ padding: Spacings.s4 }}>
```

### Sử dụng Border Radius

```typescript
// Sử dụng border radius tokens
<View br20>
<View br30>
<View br40>

// Sử dụng trực tiếp
<View style={{ borderRadius: BorderRadiuses.br30 }}>
```

### Sử dụng Shadows

```typescript
// Sử dụng shadow tokens
<View shadow-s1>
<View shadow-s2>
<View shadow-s3>

// Sử dụng trực tiếp
<View style={Shadows.s2}>
```

## 🎯 Component Themes

### Button Theme
```typescript
// Button tự động sử dụng theme
<Button label="Primary Button" />

// Override theme
<Button 
  backgroundColor={Colors.$buttonSecondary}
  label="Secondary Button" 
/>
```

### Text Theme
```typescript
// Text tự động sử dụng theme
<Text>Default text</Text>

// Override theme
<Text 
  color={Colors.$textPrimary}
  fontSize={Typography.h2.fontSize}
>
  Custom text
</Text>
```

### Card Theme
```typescript
// Card tự động sử dụng theme
<Card>
  <Text>Card content</Text>
</Card>
```

### TextField Theme
```typescript
// TextField tự động sử dụng theme
<TextField placeholder="Enter text" />
```

## 🌙 Dark Mode

### Sử dụng Theme Mode
```typescript
import { useThemeMode } from '@core/theme/theme';

const MyComponent = () => {
  const { theme, toggleTheme, isDark } = useThemeMode();

  return (
    <View>
      <Text>Current theme: {theme}</Text>
      <Button label="Toggle Theme" onPress={toggleTheme} />
      <Text>Is Dark: {isDark ? 'Yes' : 'No'}</Text>
    </View>
  );
};
```

### Dark Mode tự động
```typescript
// Colors tự động thích ứng với dark mode
<View bg-$backgroundDefault>  // Tự động thay đổi theo theme
<Text $textDefault>           // Tự động thay đổi theo theme
<Button bg-$buttonPrimary>    // Tự động thay đổi theo theme
```

## 📋 Typography Tokens

| Token | Font Size | Font Weight | Font Family | Use Case |
|-------|-----------|-------------|-------------|----------|
| `h1` | 32px | 700 | sfpd-bold | Main headings |
| `h2` | 28px | 600 | sfpd-semibold | Section headings |
| `h3` | 24px | 600 | sfpd-semibold | Subsection headings |
| `h4` | 20px | 600 | sfpd-semibold | Small headings |
| `body` | 16px | 400 | sfpd-regular | Body text |
| `bodySmall` | 14px | 400 | sfpd-regular | Small body text |
| `caption` | 12px | 400 | sfpd-regular | Captions, labels |
| `button` | 14px | 500 | sfpd-medium | Button text |
| `buttonLarge` | 16px | 600 | sfpd-semibold | Large button text |

## 📏 Spacing Tokens

| Token | Value | Use Case |
|-------|-------|----------|
| `s1` | 4px | Tiny spacing |
| `s2` | 8px | Small spacing |
| `s3` | 12px | Medium-small spacing |
| `s4` | 16px | Medium spacing |
| `s5` | 24px | Large spacing |
| `s6` | 32px | Extra large spacing |
| `s7` | 40px | Huge spacing |
| `s8` | 48px | Massive spacing |
| `s9` | 56px | Extreme spacing |
| `s10` | 64px | Maximum spacing |

## 🔲 Border Radius Tokens

| Token | Value | Use Case |
|-------|-------|----------|
| `br10` | 2px | Subtle rounding |
| `br20` | 4px | Small rounding |
| `br30` | 8px | Medium rounding |
| `br40` | 12px | Large rounding |
| `br50` | 16px | Extra large rounding |
| `br60` | 20px | Huge rounding |
| `br70` | 24px | Massive rounding |
| `br80` | 32px | Extreme rounding |
| `br90` | 40px | Maximum rounding |
| `br100` | 50px | Circular elements |

## 🌟 Shadow Tokens

| Token | Shadow | Use Case |
|-------|--------|----------|
| `s1` | Light shadow | Subtle elevation |
| `s2` | Medium shadow | Card elevation |
| `s3` | Heavy shadow | Modal elevation |
| `s4` | Extreme shadow | Floating elements |

## 🎨 Ví dụ sử dụng đầy đủ

```typescript
import React from 'react';
import { View, Text } from 'react-native';
import { Button, Card } from 'react-native-ui-lib';
import { useTheme, useThemeMode, Colors, Typography, Spacings } from '@core/theme/theme';

const ExampleComponent = () => {
  const { colors, typography, spacings } = useTheme();
  const { theme, toggleTheme, isDark } = useThemeMode();

  return (
    <View style={{ padding: spacings.s4 }}>
      {/* Typography */}
      <Text text-h1>Main Heading</Text>
      <Text text-h2>Section Heading</Text>
      <Text text-body>Body text with proper spacing and typography.</Text>
      
      {/* Spacing */}
      <View style={{ marginTop: spacings.s5 }}>
        <Text text-h3>Spacing Example</Text>
      </View>
      
      {/* Colors with dark mode */}
      <View 
        bg-$backgroundElevated 
        style={{ 
          padding: spacings.s4, 
          marginTop: spacings.s4,
          borderRadius: 8 
        }}
      >
        <Text $textDefault>Card content</Text>
        <Text $textNeutral>Secondary text</Text>
      </View>
      
      {/* Buttons */}
      <View style={{ marginTop: spacings.s5, gap: spacings.s3 }}>
        <Button bg-$buttonPrimary $textInverted label="Primary Button" />
        <Button bg-$buttonSecondary $textInverted label="Secondary Button" />
        <Button bg-$buttonSuccess $textInverted label="Success Button" />
      </View>
      
      {/* Theme toggle */}
      <View style={{ marginTop: spacings.s6 }}>
        <Button 
          label={`Switch to ${isDark ? 'Light' : 'Dark'} Mode`}
          onPress={toggleTheme}
          bg-$buttonWarning
          $textDefault
        />
      </View>
    </View>
  );
};

export default ExampleComponent;
```

## ⚠️ Lưu ý quan trọng

1. **Import Colors trước**: Đảm bảo import Colors từ `@core/constants/Colors` trước khi sử dụng
2. **Không xung đột**: Theme system và Colors system hoạt động độc lập nhưng bổ sung cho nhau
3. **Typography**: Sử dụng typography tokens thay vì hardcode font sizes
4. **Spacing**: Sử dụng spacing tokens thay vì hardcode pixel values
5. **Dark Mode**: Colors tự động thích ứng, nhưng cần test thủ công

## 🔧 Customization

### Thêm Typography mới
```typescript
// Trong theme.tsx
Typography.loadTypographies({
  // ... existing typography
  customHeading: {
    fontSize: 18,
    fontWeight: '600',
    lineHeight: 24,
    fontFamily: 'sfpd-semibold',
  },
});
```

### Thêm Spacing mới
```typescript
// Trong theme.tsx
Spacings.loadSpacings({
  // ... existing spacing
  custom: 36,
});
```

### Thêm Shadow mới
```typescript
// Trong theme.tsx
Shadows.loadShadows({
  // ... existing shadows
  custom: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.44,
    shadowRadius: 10.32,
  },
});
```

## 🎉 Kết luận

Hệ thống theme này cung cấp:
- ✅ **Design Tokens** cho màu sắc
- ✅ **Typography System** nhất quán
- ✅ **Spacing System** có quy tắc
- ✅ **Component Themes** tự động
- ✅ **Dark Mode** hỗ trợ
- ✅ **Type Safety** với TypeScript

Sử dụng kết hợp cả hai hệ thống để có một design system hoàn chỉnh! 🎨 