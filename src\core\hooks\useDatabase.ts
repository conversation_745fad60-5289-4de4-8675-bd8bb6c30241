import { useDatabaseContext } from '@core/providers/DatabaseProvider';

export const useDatabase = () => {
    return useDatabaseContext();
};

export const useDocumentRepository = () => {
    const { documentRepository } = useDatabaseContext();
    return documentRepository;
};

export const useSettingsRepository = () => {
    const { settingsRepository } = useDatabaseContext();
    return settingsRepository;
}; 