import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons, FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { AnimatedView } from '@/shared/animations/components';
import { BarChart } from 'react-native-gifted-charts';
import { useTokens } from '@features/tokens/tokenContext';
import Header from '@/shared/components/molecules/Header';

type IconName = 'arrow-back';

const TokenHistoryScreen = () => {
  const router = useRouter();
  const { tokenHistory, tokenBalance } = useTokens();
  const [filter, setFilter] = useState('all'); // all, purchases, usage

  // Filter history based on selected filter
  const filteredHistory = tokenHistory.filter(item => {
    if (filter === 'all') return true;
    if (filter === 'purchases') return item.type === 'purchase';
    if (filter === 'usage') return item.type !== 'purchase';
    return true;
  });

  // Calculate total tokens purchased and used
  const totalPurchased = tokenHistory
    .filter(item => item.type === 'purchase')
    .reduce((sum, item) => sum + item.amount, 0);

  const totalUsed = tokenHistory
    .filter(item => item.type !== 'purchase')
    .reduce((sum, item) => sum + Math.abs(item.amount), 0);

  // Prepare data for weekly usage chart
  const usageData = [
    { value: 10, label: 'T2', frontColor: '#4285F4' },
    { value: 15, label: 'T3', frontColor: '#4285F4' },
    { value: 8, label: 'T4', frontColor: '#4285F4' },
    { value: 20, label: 'T5', frontColor: '#4285F4' },
    { value: 12, label: 'T6', frontColor: '#4285F4' },
    { value: 5, label: 'T7', frontColor: '#4285F4' },
    { value: 0, label: 'CN', frontColor: '#4285F4' },
  ];

  // Render a transaction item
  const renderTransactionItem = ({
    item,
  }: {
    item: (typeof tokenHistory)[0];
  }) => (
    <View style={styles.transactionItem}>
      <View style={styles.transactionHeader}>
        <View style={styles.transactionLeft}>
          <View
            style={[
              styles.transactionIconContainer,
              {
                backgroundColor:
                  item.type === 'purchase'
                    ? 'rgba(52, 168, 83, 0.1)'
                    : 'rgba(234, 67, 53, 0.1)',
              },
            ]}
          >
            {item.type === 'purchase' ? (
              <FontAwesome5 name='coins' size={16} color='#34A853' />
            ) : (
              <Ionicons name='scan-outline' size={16} color='#EA4335' />
            )}
          </View>
          <View>
            <Text style={styles.transactionTitle}>
              {item.type === 'purchase'
                ? 'Mua token'
                : getOperationTypeText(item.type)}
            </Text>
            <Text style={styles.transactionDate}>{item.date}</Text>
          </View>
        </View>
        <Text
          style={[
            styles.transactionAmount,
            { color: item.type === 'purchase' ? '#34A853' : '#EA4335' },
          ]}
        >
          {item.type === 'purchase' ? '+' : ''}
          {item.amount}
        </Text>
      </View>

      {item.type === 'purchase' && (
        <View style={styles.transactionDetails}>
          <Text style={styles.transactionDetailLabel}>
            Phương thức thanh toán:
          </Text>
          <Text style={styles.transactionDetailValue}>
            {item.paymentMethod}
          </Text>
        </View>
      )}

      {item.type !== 'purchase' && item.document && (
        <View style={styles.transactionDetails}>
          <Text style={styles.transactionDetailLabel}>Tên tài liệu:</Text>
          <Text style={styles.transactionDetailValue}>{item.document}</Text>
        </View>
      )}
    </View>
  );

  // Get text for operation type
  const getOperationTypeText = (
    type: 'scan' | 'ocr' | 'storage' | string,
  ): string => {
    switch (type) {
      case 'scan':
        return 'Quét tài liệu';
      case 'ocr':
        return 'Nhận dạng văn bản';
      case 'storage':
        return 'Lưu trữ tài liệu';
      default:
        return 'Sử dụng token';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header title='Lịch sử giao dịch' />

      <ScrollView style={styles.scrollView}>
        {/* Token Usage Statistics */}
        <AnimatedView style={styles.statsContainer} duration={600}>
          <Text style={styles.sectionTitle}>Thống kê sử dụng</Text>

          <View style={styles.statsBoxesContainer}>
            <View style={styles.statsBox}>
              <Text style={styles.statsNumber}>{totalPurchased}</Text>
              <Text style={styles.statsLabel}>Token đã mua</Text>
            </View>

            <View style={styles.statsBox}>
              <Text style={styles.statsNumber}>{totalUsed}</Text>
              <Text style={styles.statsLabel}>Token đã dùng</Text>
            </View>

            <View style={styles.statsBox}>
              <Text style={styles.statsNumber}>{tokenBalance}</Text>
              <Text style={styles.statsLabel}>Còn lại</Text>
            </View>
          </View>

          <View style={styles.chartContainer}>
            <Text style={styles.chartTitle}>Sử dụng trong tuần</Text>
            <BarChart
              data={usageData}
              barWidth={24}
              spacing={20}
              barBorderRadius={4}
              xAxisThickness={0}
              yAxisThickness={0}
              xAxisLabelTextStyle={{ color: '#666', fontSize: 12 }}
              hideYAxisText
              noOfSections={4}
              maxValue={25}
              disablePress
            />
          </View>
        </AnimatedView>

        {/* Transaction History */}
        <AnimatedView
          style={styles.historyContainer}
          duration={600}
          delay={200}
        >
          <Text style={styles.sectionTitle}>Lịch sử giao dịch</Text>

          <View style={styles.filterContainer}>
            <TouchableOpacity
              style={[
                styles.filterButton,
                filter === 'all' && styles.filterButtonActive,
              ]}
              onPress={() => setFilter('all')}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  filter === 'all' && styles.filterButtonTextActive,
                ]}
              >
                Tất cả
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.filterButton,
                filter === 'purchases' && styles.filterButtonActive,
              ]}
              onPress={() => setFilter('purchases')}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  filter === 'purchases' && styles.filterButtonTextActive,
                ]}
              >
                Mua token
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.filterButton,
                filter === 'usage' && styles.filterButtonActive,
              ]}
              onPress={() => setFilter('usage')}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  filter === 'usage' && styles.filterButtonTextActive,
                ]}
              >
                Sử dụng
              </Text>
            </TouchableOpacity>
          </View>

          <FlatList
            data={filteredHistory}
            renderItem={renderTransactionItem}
            keyExtractor={item => item.id}
            scrollEnabled={false}
            style={styles.transactionList}
            contentContainerStyle={styles.transactionListContent}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>Không có dữ liệu giao dịch</Text>
              </View>
            }
          />
        </AnimatedView>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f6fa',
  },
  placeholderView: {
    width: 40,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  statsContainer: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  statsBoxesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statsBox: {
    flex: 1,
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f5f6fa',
    borderRadius: 12,
    marginHorizontal: 4,
  },
  statsNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: '#2F4FCD',
    marginBottom: 4,
  },
  statsLabel: {
    fontSize: 12,
    color: '#666',
  },
  chartContainer: {
    marginTop: 8,
  },
  chartTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    marginBottom: 12,
  },
  historyContainer: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  filterContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#f5f6fa',
  },
  filterButtonActive: {
    backgroundColor: '#2F4FCD',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#666',
  },
  filterButtonTextActive: {
    color: '#fff',
    fontWeight: '500',
  },
  transactionList: {
    marginTop: 8,
  },
  transactionListContent: {
    paddingBottom: 8,
  },
  transactionItem: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  transactionIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
    color: '#666',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '700',
  },
  transactionDetails: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
  },
  transactionDetailLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  transactionDetailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
});

export default TokenHistoryScreen;
