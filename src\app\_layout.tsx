import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { useEffect, useState } from 'react';
import NetInfo from '@react-native-community/netinfo';
import ProvidersWrapper from '@core/providers/providers';
import { setIsConnectedToInternet } from '@core/store/slices/appSlice';
import { store } from '@core/store';
import '@core/translations';
import * as SplashScreen from 'expo-splash-screen';
import { UpdateService } from '@core/services/updateService';
import { ThemeProvider } from '@core/theme/theme';
import { StatusBar } from 'expo-status-bar';
import 'react-native-get-random-values';
import ToastProvider from '@/shared/components/molecules/ToastProvider';
import LottieSplashScreen from '@/features/splash/components/LottieSplashScreen';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

// Constants
const FONTS = {
  'sfpd-regular': require('assets/fonts/SF-Pro-Display-Regular.ttf'),
  'sfpd-medium': require('assets/fonts/SF-Pro-Display-Medium.ttf'),
  'sfpd-semibold': require('assets/fonts/SF-Pro-Display-Semibold.ttf'),
  'sfpd-bold': require('assets/fonts/SF-Pro-Display-Bold.ttf'),
};

const ROUTE_CONFIG = {
  initialRouteName: 'index',
  dashboard: {
    initialRouteName: 'apps-dashboard',
  },
};

export { ErrorBoundary } from 'expo-router';

export const unstable_settings = ROUTE_CONFIG;

export default function RootLayout() {
  const [loaded, error] = useFonts(FONTS);
  const [showLottieSplash, setShowLottieSplash] = useState(true);

  // Expo Router uses Error Boundaries to catch errors in the navigation tree.
  useEffect(() => {
    if (error) throw error;
  }, [error]);

  // Check internet connectivity
  useEffect(() => {
    const unsubscribeNetInfo = NetInfo.addEventListener(state => {
      store.dispatch(setIsConnectedToInternet(state.isConnected as boolean));
    });
    return () => unsubscribeNetInfo();
  }, []);

  // Hide native splash screen when fonts are loaded
  useEffect(() => {
    async function hideSplash() {
      if (loaded) {
        // Ẩn native splash screen ngay khi fonts load xong
        await SplashScreen.hideAsync();
      }
    }

    hideSplash();
  }, [loaded]);

  const handleLottieAnimationFinish = () => {
    setShowLottieSplash(false);
    // Kiểm tra cập nhật sau khi Lottie animation hoàn thành
    UpdateService.handleUpdates(true);
  };

  // Show Lottie splash screen while fonts are loading or Lottie is playing
  if (!loaded || showLottieSplash) {
    return (
      <LottieSplashScreen onAnimationFinish={handleLottieAnimationFinish} />
    );
  }

  return (
    <ThemeProvider>
      <RootLayoutNav />
      <StatusBar style='auto' />
      <ToastProvider />
    </ThemeProvider>
  );
}

function RootLayoutNav() {
  return (
    <ProvidersWrapper>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen
          name='(auth)/sign-in/index'
          options={{
            title: 'Sign In',
            gestureEnabled: false,
            animation: 'fade',
          }}
        />
        <Stack.Screen
          name='(dashboard)'
          options={{
            headerShown: false,
            gestureEnabled: false,
            fullScreenGestureEnabled: false,
            animation: 'fade',
          }}
        />
      </Stack>
    </ProvidersWrapper>
  );
}
