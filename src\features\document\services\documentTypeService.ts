import { api } from '@core/client/axios';

export interface DocumentType {
    NghiepVuID: string;
    NghiepVu: string;
}

export interface DocumentTypeListParams {
    PageNumber?: number;
    PageSize?: number;
    SearchText?: string;
}

export interface DocumentTypeResponse {
    Items: DocumentType[];
    PageNumber: number;
    TotalPages: number;
    TotalCount: number;
    HasPreviousPage: boolean;
    HasNextPage: boolean;
}

export const documentTypeService = {
    /**
     * Get list of document types with pagination
     */
    getList: async (params?: DocumentTypeListParams): Promise<DocumentTypeResponse> => {
        const { data } = await api.get<DocumentTypeResponse>('/LoaiVanBan/get-list', {
            params
        });
        return data;
    },

    /**
     * Get single document type by ID
     */
    getById: async (id: string): Promise<DocumentType | null> => {
        try {
            const { data } = await api.get<DocumentType>(`/LoaiVanBan/${id}`);
            return data;
        } catch (error) {
            console.error('Error fetching document type:', error);
            return null;
        }
    },

    /**
     * Find document type by ID in existing list
     */
    findById: (id: string, items: DocumentType[]): DocumentType | undefined => {
        return items.find(item => item.NghiepVuID === id);
    },

    /**
     * Get document type name by ID from existing list
     */
    getNameById: (id: string, items: DocumentType[]): string | undefined => {
        return items.find(item => item.NghiepVuID === id)?.NghiepVu;
    },

    /**
     * Check if document type ID exists in list
     */
    hasId: (id: string, items: DocumentType[]): boolean => {
        return items.some(item => item.NghiepVuID === id);
    }
}; 