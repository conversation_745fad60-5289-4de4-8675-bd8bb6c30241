import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

const LanguageScreen = () => {
  const router = useRouter();
  const [selectedLanguage, setSelectedLanguage] = useState('vi');

  const languages = [
    {
      code: 'vi',
      name: 'Tiếng Việt',
      nativeName: 'Tiếng Việt',
      flag: '🇻🇳',
    },
    {
      code: 'en',
      name: 'Tiếng Anh',
      nativeName: 'English',
      flag: '🇬🇧',
    },
    {
      code: 'zh',
      name: 'Tiếng Trung',
      nativeName: '中文',
      flag: '🇨🇳',
    },
    {
      code: 'ko',
      name: 'Tiếng Hàn',
      nativeName: '한국어',
      flag: '🇰🇷',
    },
    {
      code: 'ja',
      name: 'Tiếng Nhật',
      nativeName: '日本語',
      flag: '🇯🇵',
    },
  ];

  const handleSelectLanguage = (code: string) => {
    setSelectedLanguage(code);
    // Implement language change logic here
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name='arrow-back' size={24} color='#333' />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Cài đặt ngôn ngữ</Text>
        <View style={styles.rightPlaceholder} />
      </View>

      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Ngôn ngữ hiển thị</Text>
            <Text style={styles.sectionDescription}>
              Chọn ngôn ngữ hiển thị cho ứng dụng. Thay đổi sẽ được áp dụng ngay
              lập tức.
            </Text>

            <View style={styles.languageList}>
              {languages.map(language => (
                <TouchableOpacity
                  key={language.code}
                  style={[
                    styles.languageItem,
                    selectedLanguage === language.code &&
                      styles.selectedLanguage,
                  ]}
                  onPress={() => handleSelectLanguage(language.code)}
                >
                  <View style={styles.languageContent}>
                    <Text style={styles.languageFlag}>{language.flag}</Text>
                    <View style={styles.languageTextContainer}>
                      <Text style={styles.languageName}>{language.name}</Text>
                      <Text style={styles.languageNativeName}>
                        {language.nativeName}
                      </Text>
                    </View>
                  </View>

                  {selectedLanguage === language.code && (
                    <View style={styles.checkContainer}>
                      <Ionicons
                        name='checkmark-circle'
                        size={22}
                        color='#673AB7'
                      />
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.infoSection}>
            <Ionicons
              name='information-circle-outline'
              size={22}
              color='#666'
            />
            <Text style={styles.infoText}>
              Chức năng này đang phát triển. Hiện tại ứng dụng chỉ hỗ trợ đầy đủ
              Tiếng Việt.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  backButton: {
    padding: 4,
  },
  rightPlaceholder: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
    lineHeight: 20,
  },
  languageList: {
    marginTop: 10,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingHorizontal: 16,
    backgroundColor: '#f8f9fa',
    marginBottom: 10,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  selectedLanguage: {
    backgroundColor: '#F5F0FF',
    borderColor: '#673AB7',
  },
  languageContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageFlag: {
    fontSize: 24,
    marginRight: 14,
  },
  languageTextContainer: {
    flex: 1,
  },
  languageName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  languageNativeName: {
    fontSize: 13,
    color: '#666',
    marginTop: 2,
  },
  checkContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#666',
    marginLeft: 10,
    lineHeight: 20,
  },
});

export default LanguageScreen;
