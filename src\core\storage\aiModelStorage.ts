import { BaseStorage } from './baseStorage';
import { STORAGE_KEYS } from './constants';
import { AiModel } from '@/features/ai-models/types';

/**
 * Service quản lý lưu trữ AI models
 */
export class AiModelStorage extends BaseStorage {
    /**
     * Lưu AI model mặc định
     * @param model Model cần lưu
     */
    setDefaultModel(model: AiModel): boolean {
        if (!model?.Model) return false;
        this.setObject(STORAGE_KEYS.AI_MODEL.DEFAULT_MODEL, model);
        return true;
    }

    /**
     * Lấy AI model mặc định
     */
    getDefaultModel(): AiModel | undefined {
        return this.getObject<AiModel>(STORAGE_KEYS.AI_MODEL.DEFAULT_MODEL) || undefined;
    }

    /**
     * Xóa AI model mặc định
     */
    clearDefaultModel(): void {
        this.deleteKey(STORAGE_KEYS.AI_MODEL.DEFAULT_MODEL);
    }

    /**
     * <PERSON><PERSON><PERSON> danh sách AI models đã sử dụng gần đây
     * @param models Danh sách models
     */
    setRecentModels(models: AiModel[]): void {
        if (!Array.isArray(models)) {
            console.error('Provided models is not an array');
            return;
        }
        this.setObject(STORAGE_KEYS.AI_MODEL.RECENT_MODELS, models);
    }

    /**
     * Lấy danh sách AI models đã sử dụng gần đây
     */
    getRecentModels(): AiModel[] {
        return this.getObject<AiModel[]>(STORAGE_KEYS.AI_MODEL.RECENT_MODELS) || [];
    }

    /**
     * Thêm AI model vào danh sách đã sử dụng gần đây
     * @param model Model cần thêm vào
     * @param maxItems Số lượng tối đa models lưu trữ
     */
    addRecentModel(model: AiModel, maxItems: number = 3): AiModel[] {
        if (!model?.Model) return this.getRecentModels();

        const recentModels = this.getRecentModels();

        // Lọc bỏ model hiện tại nếu đã có trong danh sách
        const filtered = recentModels.filter(m => m.Model !== model.Model);

        // Thêm model mới vào đầu danh sách và giới hạn số lượng
        const updated = [model, ...filtered].slice(0, maxItems);

        // Lưu danh sách mới
        this.setRecentModels(updated);

        return updated;
    }

    /**
     * Xóa toàn bộ dữ liệu AI models
     */
    clearAllModelData(): void {
        this.deleteKey(STORAGE_KEYS.AI_MODEL.DEFAULT_MODEL);
        this.deleteKey(STORAGE_KEYS.AI_MODEL.RECENT_MODELS);
    }
}

// Export singleton instance
export const aiModelStorage = new AiModelStorage(); 