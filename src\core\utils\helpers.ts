// Common utility functions

import { Dimensions, Platform } from 'react-native';

// Device utilities
export const DEVICE = {
  width: Dimensions.get('window').width,
  height: Dimensions.get('window').height,
  isIOS: Platform.OS === 'ios',
  isAndroid: Platform.OS === 'android',
  isWeb: Platform.OS === 'web',
} as const;

// Responsive design utilities
export const ResponsiveUtils = {
  // Get responsive width based on percentage
  wp: (percentage: number): number => {
    const { width } = Dimensions.get('window');
    return (width * percentage) / 100;
  },

  // Get responsive height based on percentage
  hp: (percentage: number): number => {
    const { height } = Dimensions.get('window');
    return (height * percentage) / 100;
  },

  // Get responsive font size based on screen width
  rf: (size: number): number => {
    const { width } = Dimensions.get('window');
    const baseWidth = 375; // iPhone 6/7/8 width as base
    return (size * width) / baseWidth;
  },

  // Get responsive size with min/max constraints
  rs: (size: number, minSize?: number, maxSize?: number): number => {
    const { width } = Dimensions.get('window');
    const baseWidth = 375;
    let responsiveSize = (size * width) / baseWidth;

    if (minSize && responsiveSize < minSize) {
      responsiveSize = minSize;
    }
    if (maxSize && responsiveSize > maxSize) {
      responsiveSize = maxSize;
    }

    return responsiveSize;
  },

  // Check if device is small screen (width < 375)
  isSmallScreen: (): boolean => {
    const { width } = Dimensions.get('window');
    return width < 375;
  },

  // Check if device is large screen (width > 414)
  isLargeScreen: (): boolean => {
    const { width } = Dimensions.get('window');
    return width > 414;
  },

  // Get device size category
  getDeviceSize: (): 'small' | 'medium' | 'large' => {
    const { width } = Dimensions.get('window');
    if (width < 375) return 'small';
    if (width > 414) return 'large';
    return 'medium';
  },

  // Get responsive scan frame size for QR scanner
  getScanFrameSize: (
    sizePercent: number = 0.75,
    maxSize: number = 300,
  ): number => {
    const { width } = Dimensions.get('window');
    return Math.min(width * sizePercent, maxSize);
  },

  // Get responsive spacing based on device size
  getResponsiveSpacing: (baseSpacing: number): number => {
    const deviceSize = ResponsiveUtils.getDeviceSize();
    switch (deviceSize) {
      case 'small':
        return baseSpacing * 0.8;
      case 'large':
        return baseSpacing * 1.2;
      default:
        return baseSpacing;
    }
  },

  // Get responsive border radius
  getResponsiveBorderRadius: (baseBorderRadius: number): number => {
    const deviceSize = ResponsiveUtils.getDeviceSize();
    switch (deviceSize) {
      case 'small':
        return baseBorderRadius * 0.8;
      case 'large':
        return baseBorderRadius * 1.2;
      default:
        return baseBorderRadius;
    }
  },
};

// String utilities
export const StringUtils = {
  // Capitalize first letter
  capitalize: (str: string): string => {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  },

  // Truncate text with ellipsis
  truncate: (str: string, length: number): string => {
    return str.length > length ? str.substring(0, length) + '...' : str;
  },

  // Remove special characters and spaces
  cleanString: (str: string): string => {
    return str.replace(/[^a-zA-Z0-9]/g, '');
  },

  // Format phone number
  formatPhone: (phone: string): string => {
    const cleaned = phone.replace(/\D/g, '');
    const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
    if (match) {
      return '(' + match[1] + ') ' + match[2] + '-' + match[3];
    }
    return phone;
  },

  // Generate random string
  randomString: (length: number = 8): string => {
    const chars =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
};

// Number utilities
export const NumberUtils = {
  // Format number with commas
  formatNumber: (num: number): string => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  },

  // Format currency
  formatCurrency: (amount: number, currency: string = 'VND'): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  },

  // Format file size
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // Clamp number between min and max
  clamp: (num: number, min: number, max: number): number => {
    return Math.min(Math.max(num, min), max);
  },
};

// Date utilities
export const DateUtils = {
  // Format date to Vietnamese format
  formatDate: (date: Date): string => {
    return new Intl.DateTimeFormat('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  },

  // Format date time
  formatDateTime: (date: Date): string => {
    return new Intl.DateTimeFormat('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  },

  // Get relative time (e.g., "2 hours ago")
  getRelativeTime: (date: Date): string => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Vừa xong';
    if (diffInSeconds < 3600)
      return `${Math.floor(diffInSeconds / 60)} phút trước`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)} giờ trước`;
    if (diffInSeconds < 2592000)
      return `${Math.floor(diffInSeconds / 86400)} ngày trước`;
    if (diffInSeconds < 31536000)
      return `${Math.floor(diffInSeconds / 2592000)} tháng trước`;
    return `${Math.floor(diffInSeconds / 31536000)} năm trước`;
  },

  // Check if date is today
  isToday: (date: Date): boolean => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  },

  // Check if date is yesterday
  isYesterday: (date: Date): boolean => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return date.toDateString() === yesterday.toDateString();
  },
};

// Array utilities
export const ArrayUtils = {
  // Remove duplicates from array
  removeDuplicates: <T>(array: T[]): T[] => {
    return [...new Set(array)];
  },

  // Group array by key
  groupBy: <T>(array: T[], key: keyof T): Record<string, T[]> => {
    return array.reduce(
      (groups, item) => {
        const group = String(item[key]);
        groups[group] = groups[group] || [];
        groups[group].push(item);
        return groups;
      },
      {} as Record<string, T[]>,
    );
  },

  // Shuffle array
  shuffle: <T>(array: T[]): T[] => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  },

  // Chunk array into smaller arrays
  chunk: <T>(array: T[], size: number): T[][] => {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  },
};

// Object utilities
export const ObjectUtils = {
  // Deep clone object
  deepClone: <T>(obj: T): T => {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime()) as T;
    if (obj instanceof Array)
      return obj.map(item => ObjectUtils.deepClone(item)) as T;
    if (typeof obj === 'object') {
      const clonedObj = {} as T;
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = ObjectUtils.deepClone(obj[key]);
        }
      }
      return clonedObj;
    }
    return obj;
  },

  // Pick specific keys from object
  pick: <T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> => {
    const result = {} as Pick<T, K>;
    keys.forEach(key => {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        result[key] = obj[key];
      }
    });
    return result;
  },

  // Omit specific keys from object
  omit: <T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> => {
    const result = { ...obj };
    keys.forEach(key => {
      delete result[key];
    });
    return result;
  },

  // Check if object is empty
  isEmpty: (obj: any): boolean => {
    if (obj == null) return true;
    if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
    return Object.keys(obj).length === 0;
  },
};

// Validation utilities
export const ValidationUtils = {
  // Validate email
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Validate phone number (Vietnamese format)
  isValidPhone: (phone: string): boolean => {
    const phoneRegex = /^[0-9]{10,11}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  },

  // Validate password strength
  isStrongPassword: (password: string): boolean => {
    const passwordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(password);
  },

  // Validate Vietnamese ID card
  isValidVietnameseId: (id: string): boolean => {
    const idRegex = /^[0-9]{9,12}$/;
    return idRegex.test(id);
  },
};

// Async utilities
export const AsyncUtils = {
  // Delay execution
  delay: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  // Retry function with exponential backoff
  retry: async <T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000,
  ): Promise<T> => {
    try {
      return await fn();
    } catch (error) {
      if (maxRetries <= 0) throw error;
      await AsyncUtils.delay(delay);
      return AsyncUtils.retry(fn, maxRetries - 1, delay * 2);
    }
  },

  // Debounce function
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number,
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  },

  // Throttle function
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number,
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  },
};
