import { ExpoConfig } from 'expo/config';

const config: ExpoConfig = {
  name: 'NTSOFT Document AI',
  slug: 'nts-document-ai',
  version: '1.0.0',
  updates: {
    url: 'https://u.expo.dev/1be061df-42eb-486c-bab3-9464268554e2',
    fallbackToCacheTimeout: 0, // tuỳ chọn nhưng nên thêm để luôn dùng bản mới nhất nếu có
    checkAutomatically: 'ON_LOAD',
  },
  orientation: 'portrait',
  icon: './src/assets/images/appstore.png',
  scheme: 'nts-document-ai',
  userInterfaceStyle: 'automatic',
  newArchEnabled: true,
  // splash: {
  //   image: './src/assets/images/splash.png',
  //   resizeMode: 'contain',
  //   backgroundColor: '#1E3A8A', // <PERSON><PERSON><PERSON>anh d<PERSON>
  // },
  assetBundlePatterns: ['**/*'],
  owner: 'quocdung111',
  extra: {
    eas: {
      projectId: '1be061df-42eb-486c-bab3-9464268554e2',
    },
    API_BASE_URL: process.env.API_BASE_URL,
  },
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.nts.docai',
    buildNumber: '24',
    infoPlist: {
      NSAppTransportSecurity: {
        NSAllowsArbitraryLoads: true,
      },
      NSPhotoLibraryUsageDescription:
        'Ứng dụng cần truy cập thư viện ảnh để lưu và chia sẻ tài liệu đã quét.',
      NSCameraUsageDescription:
        'Ứng dụng cần truy cập camera để quét tài liệu.',
      NSMicrophoneUsageDescription:
        'Ứng dụng cần truy cập microphone để hỗ trợ các tính năng ghi âm (nếu có).',
      NSPhotoLibraryAddUsageDescription:
        'Ứng dụng cần lưu tài liệu đã quét vào thư viện ảnh của bạn.',
      NSFaceIDUsageDescription:
        'Ứng dụng cần sử dụng Face ID để giúp bạn đăng nhập nhanh chóng và an toàn hơn.',
    },
  },
  android: {
    versionCode: 3,
    adaptiveIcon: {
      foregroundImage: './src/assets/images/logo.png',
      backgroundColor: '#ffffff',
    },
    package: 'com.nts.docai',
    permissions: [
      'android.permission.USE_BIOMETRIC',
      'android.permission.USE_FINGERPRINT',
    ],
  },
  web: {
    bundler: 'metro',
    favicon: './src/assets/images/favicon.png',
  },
  plugins: [
    'expo-font',
    'expo-router',
    'expo-localization',
    'expo-secure-store',
    [
      'expo-sqlite',
      {
        enableFTS: true, // Enable full-text search support
      },
    ],
    [
      'react-native-vision-camera',
      {
        cameraPermissionText: '$(PRODUCT_NAME) needs access to your Camera.',

        // optionally, if you want to record audio:
        enableMicrophonePermission: true,
        enableCodeScanner: true,
        microphonePermissionText:
          '$(PRODUCT_NAME) needs access to your Microphone.',
      },
    ],
    'react-native-bottom-tabs',
    [
      'react-native-share',
      {
        ios: ['fb', 'instagram', 'twitter', 'tiktoksharesdk'],
        android: [
          'com.facebook.katana',
          'com.instagram.android',
          'com.twitter.android',
          'com.zhiliaoapp.musically',
        ],
        enableBase64ShareAndroid: true,
      },
    ],
    [
      'react-native-document-scanner-plugin',
      {
        cameraPermission: 'We need camera access, so you can scan documents',
      },
    ],
    [
      'expo-camera',
      {
        cameraPermission: 'Allow $(PRODUCT_NAME) to access your camera',
        microphonePermission: 'Allow $(PRODUCT_NAME) to access your microphone',
        recordAudioAndroid: true,
      },
    ],
    [
      'expo-local-authentication',
      {
        faceIDPermission:
          'Allow $(PRODUCT_NAME) to use Face ID for easy login.',
      },
    ],
    [
      'expo-build-properties',
      {
        ios: {
          useFrameworks: 'static',
          newArchEnabled: true,
          config: {
            EXUpdatesURL:
              'https://u.expo.dev/1be061df-42eb-486c-bab3-9464268554e2',
            EXUpdatesRuntimeVersion: '1.0.0',
            usesNonExemptEncryption: false,
          },
        },
        android: {
          newArchEnabled: true,
          kotlinVersion: '1.9.25',
        },
      },
    ],
    [
      '@config-plugins/detox',
      {
        subdomains: '*',
      },
    ],
  ],
  experiments: {
    tsconfigPaths: true,
  },
  runtimeVersion: {
    policy: 'appVersion',
  },
  cli: {
    appVersionSource: 'appVersion',
  },
} as any;

export default config;
