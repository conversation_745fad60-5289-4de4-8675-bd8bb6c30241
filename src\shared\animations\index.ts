import { useRef, useEffect } from 'react';
import { Animated, Easing, ViewStyle, TextStyle, ImageStyle } from 'react-native';

type AnimatedStyles = ViewStyle | TextStyle | ImageStyle;

/**
 * Hook để tạo fade-in animation
 * @param duration Thời gian thực hiện animation (ms)
 * @param delay Độ trễ trước khi bắt đầu animation (ms)
 * @param initialValue Giá trị ban đầu của opacity (0-1)
 * @param useNativeDriver Sử dụng native driver để tối ưu performance 
 */
export const useFadeIn = (
    duration = 500,
    delay = 0,
    initialValue = 0,
    useNativeDriver = true
) => {
    const opacity = useRef(new Animated.Value(initialValue)).current;

    useEffect(() => {
        Animated.timing(opacity, {
            toValue: 1,
            duration,
            delay,
            useNativeDriver,
            easing: Easing.ease
        }).start();
    }, []);

    return opacity;
};

/**
 * Hook để tạo scale animation
 * @param duration Thời gian thực hiện animation (ms)
 * @param delay Độ trễ trước khi bắt đầu animation (ms)
 * @param initialValue Giá trị ban đầu của scale (thường < 1)
 */
export const useScale = (
    duration = 500,
    delay = 0,
    initialValue = 0.9
) => {
    const scale = useRef(new Animated.Value(initialValue)).current;

    useEffect(() => {
        Animated.timing(scale, {
            toValue: 1,
            duration,
            delay,
            useNativeDriver: true,
            easing: Easing.elastic(1)
        }).start();
    }, []);

    return scale;
};

/**
 * Hook để tạo slide animation theo chiều dọc
 * @param duration Thời gian thực hiện animation (ms)
 * @param delay Độ trễ trước khi bắt đầu animation (ms)
 * @param fromValue Giá trị vị trí ban đầu
 */
export const useSlideY = (
    duration = 500,
    delay = 0,
    fromValue = 50
) => {
    const translateY = useRef(new Animated.Value(fromValue)).current;

    useEffect(() => {
        Animated.timing(translateY, {
            toValue: 0,
            duration,
            delay,
            useNativeDriver: true,
            easing: Easing.out(Easing.cubic)
        }).start();
    }, []);

    return translateY;
};

/**
 * Hook để tạo slide animation theo chiều ngang
 * @param duration Thời gian thực hiện animation (ms)
 * @param delay Độ trễ trước khi bắt đầu animation (ms)
 * @param fromValue Giá trị vị trí ban đầu
 */
export const useSlideX = (
    duration = 500,
    delay = 0,
    fromValue = 50
) => {
    const translateX = useRef(new Animated.Value(fromValue)).current;

    useEffect(() => {
        Animated.timing(translateX, {
            toValue: 0,
            duration,
            delay,
            useNativeDriver: true,
            easing: Easing.out(Easing.cubic)
        }).start();
    }, []);

    return translateX;
};

/**
 * Hook tạo combo các hiệu ứng xuất hiện phổ biến (fade + scale + slide)
 * @param duration Thời gian animation
 * @param delay Thời gian trễ 
 * @param slideValue Khoảng cách hiệu ứng slide
 */
export const useAppearAnimation = (
    duration = 600,
    delay = 0,
    slideValue = 30
) => {
    const opacity = useRef(new Animated.Value(0)).current;
    const scale = useRef(new Animated.Value(0.9)).current;
    const translateY = useRef(new Animated.Value(slideValue)).current;

    useEffect(() => {
        Animated.parallel([
            Animated.timing(opacity, {
                toValue: 1,
                duration,
                delay,
                useNativeDriver: true,
                easing: Easing.ease
            }),
            Animated.timing(scale, {
                toValue: 1,
                duration,
                delay,
                useNativeDriver: true,
                easing: Easing.elastic(1)
            }),
            Animated.timing(translateY, {
                toValue: 0,
                duration,
                delay,
                useNativeDriver: true,
                easing: Easing.out(Easing.cubic)
            })
        ]).start();
    }, []);

    return {
        opacity,
        scale,
        translateY,
        style: {
            opacity,
            transform: [
                { scale },
                { translateY }
            ]
        }
    };
};

/**
 * Hook để tạo staggered animation (hiệu ứng hiện lần lượt)
 * @param itemCount Số lượng items
 * @param staggerDelay Thời gian trễ giữa các items
 * @param duration Thời gian thực hiện animation
 * @param initialDelay Thời gian trễ ban đầu
 */
export const useStaggeredAnimation = (
    itemCount: number,
    staggerDelay = 50,
    duration = 400,
    initialDelay = 0
) => {
    const animations = useRef<Animated.Value[]>([]);

    // Lần đầu render, tạo đủ số animation values
    useEffect(() => {
        animations.current = Array(itemCount)
            .fill(0)
            .map(() => new Animated.Value(0));

        // Tạo staggered animation
        const staggerAnimations = animations.current.map((anim, index) => {
            return Animated.timing(anim, {
                toValue: 1,
                duration,
                delay: initialDelay + (index * staggerDelay),
                useNativeDriver: true,
                easing: Easing.ease
            });
        });

        Animated.stagger(staggerDelay, staggerAnimations).start();
    }, [itemCount]);

    // Function để lấy animation style cho 1 item cụ thể
    const getAnimationStyle = (index: number): AnimatedStyles => {
        if (!animations.current[index]) return {};

        return {
            opacity: animations.current[index],
            transform: [
                {
                    translateY: animations.current[index].interpolate({
                        inputRange: [0, 1],
                        outputRange: [20, 0]
                    })
                },
                {
                    scale: animations.current[index].interpolate({
                        inputRange: [0, 1],
                        outputRange: [0.9, 1]
                    })
                }
            ]
        };
    };

    return { getAnimationStyle };
};

/**
 * Hook để tạo hiệu ứng nhấp nháy cho skeleton loading
 * @param duration Thời gian của một chu kỳ nhấp nháy
 */
export const useSkeletonAnimation = (duration = 1500) => {
    const opacity = useRef(new Animated.Value(0.3)).current;

    useEffect(() => {
        const animation = Animated.loop(
            Animated.sequence([
                Animated.timing(opacity, {
                    toValue: 0.6,
                    duration: duration / 2,
                    useNativeDriver: true,
                    easing: Easing.ease
                }),
                Animated.timing(opacity, {
                    toValue: 0.3,
                    duration: duration / 2,
                    useNativeDriver: true,
                    easing: Easing.ease
                })
            ])
        );

        animation.start();

        return () => {
            animation.stop();
        };
    }, []);

    return opacity;
};

/**
 * Hook tạo hiệu ứng press animation
 * @returns object chứa các handlers và style để dùng trong components
 */
export const usePressAnimation = () => {
    const scale = useRef(new Animated.Value(1)).current;

    const handlePressIn = () => {
        Animated.timing(scale, {
            toValue: 0.95,
            duration: 150,
            useNativeDriver: true,
            easing: Easing.ease
        }).start();
    };

    const handlePressOut = () => {
        Animated.timing(scale, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
            easing: Easing.elastic(1)
        }).start();
    };

    return {
        scale,
        style: { transform: [{ scale }] },
        handlers: {
            onPressIn: handlePressIn,
            onPressOut: handlePressOut
        }
    };
};

/**
 * Hook để tạo animation dựa trên scroll position
 * @param scrollY Animated.Value từ onScroll event
 * @param inputRange Range của giá trị scroll
 * @param outputRange Range của giá trị output tương ứng
 */
export const useScrollAnimation = (
    scrollY: Animated.Value,
    inputRange: number[],
    outputRange: number[]
) => {
    return scrollY.interpolate({
        inputRange,
        outputRange,
        extrapolate: 'clamp'
    });
};

/**
 * Tạo hiệu ứng nhấn cho TouchableOpacity
 * @param onPress Callback khi press
 */
export const withPressAnimation = (onPress: () => void) => {
    const { handlers, style } = usePressAnimation();

    return {
        ...handlers,
        onPress,
        style,
    };
};

/**
 * Tạo hiệu ứng loading skeleton
 * @param count Số lượng skeleton items
 * @param renderItem Function render mỗi skeleton item
 */
export const createSkeletonLoader = (
    count: number,
    renderItem: (index: number, opacity: Animated.Value) => React.ReactNode
) => {
    const skeletonOpacity = useSkeletonAnimation();
    return Array(count).fill(0).map((_, index) => renderItem(index, skeletonOpacity));
};

/**
 * Hiệu ứng nút nhấn với scale animation
 * @param scale Animated.Value cho hiệu ứng scale
 */
export const buttonScaleInterpolate = (scale: Animated.Value) => {
    return {
        transform: [{ scale }]
    };
};

/**
 * Tạo hiệu ứng ripple từ vị trí chạm
 * (Utility function - không phải hook)
 */
export const createRippleAnimation = (
    size: number,
    position: { x: number, y: number },
    onAnimationEnd?: () => void
) => {
    const rippleAnim = new Animated.Value(0);

    Animated.timing(rippleAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true
    }).start(onAnimationEnd);

    const rippleStyle = {
        position: 'absolute' as 'absolute',
        width: size,
        height: size,
        borderRadius: size / 2,
        backgroundColor: 'rgba(255, 255, 255, 0.3)',
        opacity: rippleAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [0.5, 0]
        }),
        transform: [
            {
                scale: rippleAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, 2]
                })
            }
        ],
        left: position.x - size / 2,
        top: position.y - size / 2
    };

    return { rippleAnim, rippleStyle };
}; 