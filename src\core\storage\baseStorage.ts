import { MMKV } from 'react-native-mmkv';

/**
 * <PERSON><PERSON><PERSON> cơ sở cho các service lưu trữ sử dụng MMKV
 * Cung cấp các phương thức tiện ích để làm việc với dữ liệu
 */
export class BaseStorage {
    protected storage: MMKV;

    constructor() {
        this.storage = new MMKV();
    }

    /**
     * Lấy chuỗi từ storage
     */
    protected getString(key: string): string | undefined {
        return this.storage.getString(key);
    }

    /**
     * Lưu chuỗi vào storage
     */
    protected setString(key: string, value: string): void {
        this.storage.set(key, value);
    }

    /**
     * Lấy đối tượng từ storage (tự động parse JSON)
     */
    protected getObject<T>(key: string): T | null {
        const data = this.storage.getString(key);
        if (!data) return null;

        try {
            return JSON.parse(data) as T;
        } catch (e) {
            console.error(`Error parsing JSON from key ${key}:`, e);
            return null;
        }
    }

    /**
     * <PERSON><PERSON><PERSON> đối tượng vào storage (tự động stringify)
     */
    protected setObject<T>(key: string, value: T): void {
        try {
            this.storage.set(key, JSON.stringify(value));
        } catch (e) {
            console.error(`Error saving object to key ${key}:`, e);
        }
    }

    /**
     * Xóa khóa khỏi storage
     */
    protected deleteKey(key: string): void {
        this.storage.delete(key);
    }

    /**
     * Kiểm tra xem khóa có tồn tại trong storage không
     */
    protected hasKey(key: string): boolean {
        return this.storage.contains(key);
    }
} 