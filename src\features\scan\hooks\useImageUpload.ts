import { useMutation, useQueryClient } from '@tanstack/react-query';
import { imageService, UploadProgress } from '../services/imageService';
import { useState } from 'react';
import { scanKeys } from '@core/api/keys/scanKeys';
import { useToast } from '@/shared/hooks/useToast';

export const useImageUpload = () => {

    const queryClient = useQueryClient();
    const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);
    const { showSuccess, showError, showInfo } = useToast();
    return useMutation({
        mutationFn: (images: string[]) =>
            imageService.uploadImages(images, (progress) => {
                setUploadProgress(progress);
            }),
        onMutate: () => {
            setUploadProgress(null);
            showSuccess('Đang gửi ảnh...', 'success');
        },
        onSuccess: (response) => {
            console.log("response", response.data.status)
            setUploadProgress(null);
            if (response?.data?.status === "success") {
                console.log(response.data.code)
                showSuccess("Gửi ảnh thành công", 'success');
                queryClient.invalidateQueries({ queryKey: scanKeys.results() });
            } else {
                showError(response?.data?.message || 'Gửi ảnh thất bại!', 'error');
            }
        },
        onError: (error: Error) => {
            setUploadProgress(null);
            console.error('Upload error:', error);
            showError('Gửi ảnh thất bại!', 'error');
        },
    });
};

export const useUploadProgress = (uploadId?: string) => {
    const [progress, setProgress] = useState<number>(0);

    // Custom hook để theo dõi tiến trình upload
    return {
        progress,
        setProgress,
        isComplete: progress === 100,
    };
}; 