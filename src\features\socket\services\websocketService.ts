import { WebSocketMessage, WebSocketConfig, WebSocketEventHandlers, WebSocketState } from '../types';

export class WebSocketService {
    private ws: WebSocket | null = null;
    private config: WebSocketConfig;
    private eventHandlers: WebSocketEventHandlers;
    private reconnectAttempts = 0;
    private reconnectTimer: NodeJS.Timeout | null = null;
    private state: WebSocketState = {
        isConnected: false,
        isConnecting: false,
        error: null,
        lastMessage: null,
        messageHistory: []
    };

    constructor(config: WebSocketConfig, eventHandlers: WebSocketEventHandlers = {}) {
        this.config = config;
        this.eventHandlers = eventHandlers;
    }

    public connect(): Promise<void> {
        return new Promise((resolve, reject) => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                resolve();
                return;
            }

            this.state.isConnecting = true;
            this.state.error = null;

            try {
                this.ws = new WebSocket(this.config.url, this.config.protocols);

                this.ws.onopen = (event) => {
                    console.log('WebSocket connected');
                    this.state.isConnected = true;
                    this.state.isConnecting = false;
                    this.reconnectAttempts = 0;
                    this.eventHandlers.onOpen?.(event);
                    resolve();
                };

                this.ws.onclose = (event) => {
                    console.log('WebSocket disconnected');
                    this.state.isConnected = false;
                    this.state.isConnecting = false;
                    this.eventHandlers.onClose?.(event);

                    if (!event.wasClean) {
                        this.attemptReconnect();
                    }
                };

                this.ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    this.state.error = 'Connection error';
                    this.state.isConnecting = false;
                    this.eventHandlers.onError?.(error);
                    reject(error);
                };

                this.ws.onmessage = (event) => {
                    try {
                        const message: WebSocketMessage = JSON.parse(event.data);
                        message.timestamp = Date.now();

                        this.state.lastMessage = message;
                        this.state.messageHistory.push(message);

                        // Keep only last 100 messages
                        if (this.state.messageHistory.length > 100) {
                            this.state.messageHistory = this.state.messageHistory.slice(-100);
                        }

                        this.eventHandlers.onMessage?.(message);
                    } catch (error) {
                        console.error('Failed to parse WebSocket message:', error);
                    }
                };

            } catch (error) {
                this.state.isConnecting = false;
                this.state.error = 'Failed to create WebSocket connection';
                reject(error);
            }
        });
    }

    public disconnect(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }

        this.state.isConnected = false;
        this.state.isConnecting = false;
    }

    public send(message: WebSocketMessage): boolean {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            console.error('WebSocket is not connected');
            return false;
        }

        try {
            const messageStr = JSON.stringify(message);
            this.ws.send(messageStr);
            return true;
        } catch (error) {
            console.error('Failed to send WebSocket message:', error);
            return false;
        }
    }

    public sendPing(): boolean {
        return this.send({ action: 'ping' });
    }

    public getState(): WebSocketState {
        return { ...this.state };
    }

    public clearMessageHistory(): void {
        this.state.messageHistory = [];
    }

    private attemptReconnect(): void {
        if (this.reconnectAttempts >= (this.config.maxReconnectAttempts || 5)) {
            console.log('Max reconnection attempts reached');
            return;
        }

        this.reconnectAttempts++;
        const delay = (this.config.reconnectInterval || 1000) * this.reconnectAttempts;

        console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);

        this.reconnectTimer = setTimeout(() => {
            this.connect().catch((error) => {
                console.error('Reconnection failed:', error);
            });
        }, delay);
    }
} 