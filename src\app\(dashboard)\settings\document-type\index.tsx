import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  StatusBar,
  TextInput,
  RefreshControl,
  SectionList,
  Animated,
  Platform,
  Dimensions,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { API_CONFIG } from '@core/api/config';
import {
  getCurrentDocumentType,
  updateDocumentType,
} from '@core/api/apiConfigManager';
import { useDocumentTypes } from '@features/document/hooks/useDocumentTypes';
import { DocumentType } from '@features/document/services/documentTypeService';
import Toast from 'react-native-toast-message';
import { useSettingsRepository } from '@core/hooks/useDatabase';
import { documentStorage, DocumentTypeData } from '@core/storage';
import {
  AnimatedView,
  FadeInView,
  AnimatedButton,
  SkeletonView,
} from '@/shared/animations/components';
import {
  useFadeIn,
  useScale,
  useSlideY,
  useStaggeredAnimation,
} from '@/shared/animations';
import Header from '@/shared/components/molecules/Header';
import { useToast } from '@/shared/hooks/useToast';

const { width } = Dimensions.get('window');

// Interface cho loại tài liệu trong component (sẽ map từ API type)
interface LocalDocumentType {
  Id: string;
  TenLoai: string;
  MaLoai: string;
  MoTa?: string;
}

// Keys cho SQLite storage (chỉ còn lại cho pinned items)
const PINNED_DOC_TYPES_KEY = 'pinned_document_types';

// Màu cho các loại tài liệu theo chữ cái đầu
const DOC_TYPE_COLORS = [
  '#4361ee', // A
  '#4cc9f0', // B
  '#f72585', // C
  '#480ca8', // D
  '#f77f00', // E
  '#7209b7', // F
  '#4cc9f0', // G
  '#f72585', // H
  '#4895ef', // I
  '#4361ee', // J
  '#b5179e', // K
  '#4895ef', // L
  '#3f37c9', // M
  '#f72585', // N
  '#560bad', // O
  '#4cc9f0', // P
  '#fcbf49', // Q
  '#7209b7', // R
  '#4895ef', // S
  '#3a0ca3', // T
  '#b5179e', // U
  '#4895ef', // V
  '#f77f00', // W
  '#4361ee', // X
  '#7209b7', // Y
  '#3a0ca3', // Z
];

const MAX_RECENT_DOC_TYPES = 3;
const MAX_PINNED_DOC_TYPES = 5;

const DocumentTypeScreen = () => {
  const { showSuccess, showError, showToast } = useToast();
  const router = useRouter();
  const [selectedDocTypeId, setSelectedDocTypeId] = useState<string | null>(
    null,
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [recentDocTypes, setRecentDocTypes] = useState<LocalDocumentType[]>([]);
  const [showAllDocTypes, setShowAllDocTypes] = useState(false);
  const [pinnedDocTypes, setPinnedDocTypes] = useState<LocalDocumentType[]>([]);
  const settingsRepository = useSettingsRepository();

  // Animations
  const fadeAnim = useFadeIn(600);
  const scaleAnim = useScale(600, 0, 0.95);
  const searchAnim = useFadeIn(800, 300);
  const scrollYAnim = useState(new Animated.Value(0))[0];

  // Header animation
  const headerHeight = scrollYAnim.interpolate({
    inputRange: [0, 120],
    outputRange: [180, 70],
    extrapolate: 'clamp',
  });

  const headerOpacity = scrollYAnim.interpolate({
    inputRange: [0, 80, 120],
    outputRange: [1, 0.5, 0],
    extrapolate: 'clamp',
  });

  const headerTitleOpacity = scrollYAnim.interpolate({
    inputRange: [0, 80, 120],
    outputRange: [0, 0.5, 1],
    extrapolate: 'clamp',
  });

  // Sử dụng React Query hook để lấy loại tài liệu
  const {
    data: documentTypesResponse,
    isLoading,
    isError,
    error: apiError,
    refetch,
    isFetching,
  } = useDocumentTypes({
    SearchText: searchQuery,
    PageSize: 100,
  });

  // Sử dụng isFetching từ React Query để xác định trạng thái refreshing
  const refreshing = isFetching;

  // Khởi tạo các giá trị mặc định khi component mount
  React.useEffect(() => {
    const loadAllData = async () => {
      try {
        // Load default document type
        const defaultDocType = documentStorage.getDefaultDocumentType();
        if (defaultDocType) {
          setSelectedDocTypeId(defaultDocType);
        }

        // Load recent documents từ documentStorage
        const recentDocumentTypes = documentStorage.getRecentDocumentTypes();
        if (recentDocumentTypes.length > 0) {
          // Chuyển đổi từ DocumentTypeData sang LocalDocumentType
          const convertedRecent = recentDocumentTypes.map(item => ({
            Id: item.NghiepVuID,
            TenLoai: item.NghiepVu,
            MaLoai: item.NghiepVuID, // Sử dụng ID làm mã mặc định
            MoTa: '',
          }));
          setRecentDocTypes(convertedRecent);
        }

        // Load pinned documents (vẫn sử dụng SQLite)
        const pinnedData =
          await settingsRepository.getSetting(PINNED_DOC_TYPES_KEY);
        if (pinnedData) {
          setPinnedDocTypes(JSON.parse(pinnedData));
        }
      } catch (error) {
        console.error('Error loading data:', error);
        Toast.show({
          type: 'error',
          text1: 'Lỗi tải dữ liệu',
          text2: 'Không thể tải dữ liệu từ bộ nhớ',
          position: 'bottom',
          visibilityTime: 3000,
          autoHide: true,
          topOffset: 30,
          bottomOffset: 40,
        });
      }
    };

    loadAllData();
  }, [settingsRepository]);

  // Chuyển đổi từ API DocumentType sang LocalDocumentType
  const mapApiToLocalDocType = useCallback(
    (apiDocType: DocumentType): LocalDocumentType => ({
      Id: apiDocType.NghiepVuID,
      TenLoai: apiDocType.NghiepVu,
      MaLoai: apiDocType.NghiepVuID,
      MoTa: '',
    }),
    [],
  );

  // Xử lý refresh
  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  // Cập nhật danh sách loại tài liệu đã sử dụng gần đây
  const updateRecentDocTypes = useCallback(
    async (docType: LocalDocumentType) => {
      // Chuyển đổi từ LocalDocumentType sang DocumentTypeData
      const documentTypeData: DocumentTypeData = {
        NghiepVuID: docType.Id,
        NghiepVu: docType.TenLoai,
      };

      // Sử dụng documentStorage để lưu trữ
      const updatedList = documentStorage.addRecentDocumentType(
        documentTypeData,
        MAX_RECENT_DOC_TYPES,
      );

      // Chuyển đổi lại thành dạng LocalDocumentType và cập nhật state
      const convertedList = updatedList.map(item => ({
        Id: item.NghiepVuID,
        TenLoai: item.NghiepVu,
        MaLoai: item.NghiepVuID,
        MoTa: '',
      }));

      setRecentDocTypes(convertedList);
    },
    [],
  );

  // Cập nhật hàm togglePin để lưu vào SQLite
  const togglePin = useCallback(
    async (docType: LocalDocumentType) => {
      setPinnedDocTypes(prev => {
        const isPinned = prev.some(p => p.Id === docType.Id);

        if (!isPinned && prev.length >= MAX_PINNED_DOC_TYPES) {
          Toast.show({
            type: 'warning',
            text1: 'Đã đạt giới hạn ghim',
            text2: `Bạn chỉ có thể ghim tối đa ${MAX_PINNED_DOC_TYPES} tài liệu`,
            position: 'bottom',
            visibilityTime: 3000,
            autoHide: true,
            topOffset: 30,
            bottomOffset: 40,
          });
          return prev;
        }

        let updated;
        if (isPinned) {
          updated = prev.filter(p => p.Id !== docType.Id);
          Toast.show({
            type: 'success',
            text1: 'Đã bỏ ghim tài liệu',
            text2: docType.TenLoai,
            position: 'bottom',
            visibilityTime: 2000,
            autoHide: true,
            topOffset: 30,
            bottomOffset: 40,
          });
        } else {
          updated = [...prev, docType];
          Toast.show({
            type: 'success',
            text1: 'Đã ghim tài liệu',
            text2: docType.TenLoai,
            position: 'bottom',
            visibilityTime: 2000,
            autoHide: true,
            topOffset: 30,
            bottomOffset: 40,
          });
        }

        // Lưu vào SQLite
        settingsRepository
          .setSetting(PINNED_DOC_TYPES_KEY, JSON.stringify(updated))
          .catch(error => {
            console.error('Error saving pinned documents:', error);
            Toast.show({
              type: 'error',
              text1: 'Lỗi lưu dữ liệu',
              text2: 'Không thể lưu dữ liệu ghim',
              position: 'bottom',
              visibilityTime: 3000,
              autoHide: true,
              topOffset: 30,
              bottomOffset: 40,
            });
          });

        return updated;
      });
    },
    [settingsRepository],
  );

  // Lưu loại tài liệu khi người dùng chọn
  const handleSelectDocType = useCallback(
    async (docType: LocalDocumentType) => {
      try {
        // Lưu loại tài liệu mặc định bằng documentStorage
        documentStorage.setDefaultDocumentType(docType.Id);
        setSelectedDocTypeId(docType.Id);

        // ✅ Cập nhật vào API config để sử dụng cho upload
        updateDocumentType(docType.Id);

        // Cập nhật danh sách gần đây
        await updateRecentDocTypes(docType);

        showSuccess('Đã lưu loại tài liệu mặc định', docType.TenLoai);
      } catch (error) {
        console.error('Error saving document type:', error);
        Toast.show({
          type: 'error',
          text1: 'Lỗi lưu dữ liệu',
          text2: 'Không thể lưu loại tài liệu mặc định',
          position: 'bottom',
          visibilityTime: 3000,
          autoHide: true,
          topOffset: 30,
          bottomOffset: 40,
        });
      }
    },
    [updateRecentDocTypes],
  );

  // Lọc và tối ưu hoá danh sách tài liệu
  const { docTypes, filteredDocTypes, sectionedDocTypes } = useMemo(() => {
    const typesFromApi =
      documentTypesResponse?.Items?.map(mapApiToLocalDocType) || [];
    const filtered = typesFromApi.filter(
      docType =>
        (docType?.TenLoai?.toLowerCase() || '').includes(
          searchQuery?.toLowerCase() || '',
        ) ||
        (docType?.MaLoai?.toLowerCase() || '').includes(
          searchQuery?.toLowerCase() || '',
        ),
    );

    if (searchQuery) {
      return {
        docTypes: typesFromApi,
        filteredDocTypes: filtered,
        sectionedDocTypes: [{ title: '', data: filtered }],
      };
    }

    const recentDocTypeIds = recentDocTypes.map(p => p.Id);
    const pinnedDocTypeIds = pinnedDocTypes.map(p => p.Id);

    const recentDocTypesData = typesFromApi.filter(p =>
      recentDocTypeIds.includes(p.Id),
    );
    const pinnedDocTypesData = typesFromApi.filter(p =>
      pinnedDocTypeIds.includes(p.Id),
    );
    const otherDocTypes = typesFromApi.filter(
      p => !recentDocTypeIds.includes(p.Id) && !pinnedDocTypeIds.includes(p.Id),
    );

    const displayedOtherDocTypes = showAllDocTypes
      ? otherDocTypes
      : otherDocTypes.slice(0, 10);

    const sectioned = [];

    if (pinnedDocTypesData.length > 0) {
      sectioned.push({
        title: 'Đã ghim',
        data: pinnedDocTypesData,
      });
    }

    if (recentDocTypesData.length > 0) {
      sectioned.push({
        title: 'Đã sử dụng gần đây',
        data: recentDocTypesData,
      });
    }

    sectioned.push({
      title: 'Tất cả loại tài liệu',
      data: displayedOtherDocTypes,
    });

    return {
      docTypes: typesFromApi,
      filteredDocTypes: filtered,
      sectionedDocTypes: sectioned,
    };
  }, [
    documentTypesResponse,
    searchQuery,
    recentDocTypes,
    pinnedDocTypes,
    showAllDocTypes,
    mapApiToLocalDocType,
  ]);

  // Lấy màu dựa vào tên loại tài liệu
  const getDocTypeColor = useCallback((docTypeName: string) => {
    if (!docTypeName) return DOC_TYPE_COLORS[0];

    const firstChar = docTypeName.charAt(0).toUpperCase();
    const index = firstChar.charCodeAt(0) - 'A'.charCodeAt(0);

    if (index >= 0 && index < 26) {
      return DOC_TYPE_COLORS[index];
    }
    return DOC_TYPE_COLORS[0];
  }, []);

  // Lấy icon dựa vào mã loại tài liệu
  const getDocTypeIcon = useCallback(
    (docTypeCode: string, docTypeName: string) => {
      // Lấy số từ đầu tên tài liệu (vd: "1. Báo cáo..." -> "1")
      const numberMatch = docTypeName.match(/^\d+/);
      if (numberMatch) {
        return numberMatch[0];
      }

      // Fallback về icon cũ nếu không có số
      const code = docTypeCode?.toLowerCase() || '';
      if (code.includes('qd') || code.includes('qdpd'))
        return 'shield-checkmark-outline';
      if (code.includes('bb') || code.includes('bbbg'))
        return 'newspaper-outline';
      if (code.includes('bc')) return 'document-text-outline';
      if (code.includes('bv')) return 'image-outline';
      return 'document-outline';
    },
    [],
  );

  // Render mỗi item loại tài liệu
  const renderDocTypeItem = useCallback(
    ({ item, index }: { item: LocalDocumentType; index: number }) => {
      const isSelected = item.Id === selectedDocTypeId;
      const isPinned = pinnedDocTypes.some(p => p.Id === item.Id);
      const docTypeColor = getDocTypeColor(item.TenLoai);
      const docTypeIcon = getDocTypeIcon(item.MaLoai, item.TenLoai);

      return (
        <FadeInView
          style={styles.docTypeItemContainer}
          duration={500}
          delay={index * 100}
        >
          <TouchableOpacity
            style={[styles.docTypeItem, isSelected && styles.selectedItem]}
            onPress={() => handleSelectDocType(item)}
            onLongPress={() => {
              Alert.alert(
                'Tên loại tài liệu đầy đủ',
                item.TenLoai,
                [{ text: 'Đóng', style: 'cancel' }],
                { cancelable: true },
              );
            }}
            activeOpacity={0.8}
          >
            <View
              style={[styles.docTypeIcon, { backgroundColor: docTypeColor }]}
            >
              {typeof docTypeIcon === 'string' && docTypeIcon.match(/^\d+$/) ? (
                <Text style={styles.docTypeNumber}>{docTypeIcon}</Text>
              ) : (
                <Ionicons name={docTypeIcon as any} size={22} color='#fff' />
              )}
            </View>
            <View style={styles.docTypeContent}>
              <Text style={styles.docTypeTitle} numberOfLines={3}>
                {item.TenLoai}
              </Text>
              <Text style={styles.docTypeCode}>Mã: {item.MaLoai}</Text>
              {item.MoTa ? (
                <Text style={styles.docTypeDescription} numberOfLines={1}>
                  {item.MoTa}
                </Text>
              ) : null}
            </View>
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[styles.pinButton, isPinned && styles.pinnedButton]}
                onPress={() => togglePin(item)}
              >
                <MaterialCommunityIcons
                  name={isPinned ? 'pin' : 'pin-outline'}
                  size={18}
                  color={isPinned ? '#4361ee' : '#666'}
                />
              </TouchableOpacity>
              {isSelected && (
                <View
                  style={[
                    styles.checkmarkCircle,
                    { backgroundColor: docTypeColor },
                  ]}
                >
                  <Ionicons name='checkmark' size={16} color='#fff' />
                </View>
              )}
            </View>
          </TouchableOpacity>
        </FadeInView>
      );
    },
    [
      selectedDocTypeId,
      pinnedDocTypes,
      getDocTypeColor,
      getDocTypeIcon,
      handleSelectDocType,
      togglePin,
    ],
  );

  // Render header cho mỗi section
  const renderSectionHeader = useCallback(
    ({ section }: { section: { title: string } }) => {
      if (!section.title) return null;

      return (
        <Animated.View
          style={[
            styles.sectionHeader,
            {
              opacity: fadeAnim,
              transform: [
                {
                  translateY: fadeAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [20, 0],
                  }),
                },
              ],
            },
          ]}
        >
          <View style={styles.sectionHeaderContent}>
            <View style={styles.sectionTitleContainer}>
              <Text style={styles.sectionHeaderText}>{section.title}</Text>
              {section.title === 'Đã ghim' && (
                <View style={styles.pinCountContainer}>
                  <Text style={styles.pinCountText}>
                    {pinnedDocTypes.length}/{MAX_PINNED_DOC_TYPES}
                  </Text>
                </View>
              )}
            </View>

            {section.title === 'Đã ghim' && (
              <MaterialCommunityIcons name='pin' size={18} color='#4361ee' />
            )}

            {section.title === 'Đã sử dụng gần đây' && (
              <MaterialCommunityIcons
                name='clock-time-four-outline'
                size={18}
                color='#666'
              />
            )}

            {section.title === 'Tất cả loại tài liệu' && (
              <MaterialCommunityIcons
                name='file-document-multiple-outline'
                size={18}
                color='#666'
              />
            )}
          </View>
        </Animated.View>
      );
    },
    [fadeAnim, pinnedDocTypes.length],
  );

  // Render footer cho phần "Tất cả loại tài liệu"
  const renderSectionFooter = useCallback(
    ({ section }: { section: { title: string; data: any[] } }) => {
      const totalCount = docTypes.length;
      const displayedCount = section.data.length;

      if (
        section.title === 'Tất cả loại tài liệu' &&
        !searchQuery &&
        !showAllDocTypes &&
        totalCount > 10
      ) {
        return (
          <TouchableOpacity
            style={styles.showMoreButton}
            onPress={() => setShowAllDocTypes(true)}
          >
            <Text style={styles.showMoreText}>
              Xem thêm {totalCount - displayedCount} loại tài liệu
            </Text>
            <Ionicons name='chevron-down' size={16} color='#4361ee' />
          </TouchableOpacity>
        );
      }
      return null;
    },
    [docTypes, searchQuery, showAllDocTypes],
  );

  // Render skeleton loading
  const renderSkeletonLoading = useCallback(() => {
    const skeletons = Array(7).fill(0);

    return (
      <View style={styles.skeletonContainer}>
        {skeletons.map((_, index) => (
          <SkeletonView
            key={index}
            style={[
              styles.skeletonItem,
              {
                transform: [
                  {
                    translateY: fadeAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [index * 10 + 20, 0],
                    }),
                  },
                ],
              },
            ]}
          />
        ))}
      </View>
    );
  }, [fadeAnim]);

  // Hiển thị danh sách trống
  const renderEmptyComponent = useCallback(() => {
    return (
      <View style={styles.emptyContainer}>
        <View style={styles.emptyIconContainer}>
          <MaterialCommunityIcons
            name='file-document-multiple-outline'
            size={80}
            color='#ddd'
          />
        </View>
        <Text style={styles.emptyText}>
          {searchQuery
            ? 'Không tìm thấy loại tài liệu phù hợp'
            : 'Không có loại tài liệu nào'}
        </Text>
        <Text style={styles.emptySubtext}>
          {searchQuery
            ? 'Vui lòng thử từ khóa khác.'
            : 'Hãy liên hệ quản trị viên để thêm loại tài liệu.'}
        </Text>
        {searchQuery && (
          <TouchableOpacity
            style={styles.clearSearchButton}
            onPress={() => setSearchQuery('')}
          >
            <Text style={styles.clearSearchText}>Xóa tìm kiếm</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }, [searchQuery]);

  // Hiển thị header với animation khi scroll
  const renderAnimatedHeader = () => {
    return (
      <Animated.View
        style={[
          styles.animatedHeaderContainer,
          {
            height: headerHeight,
          },
        ]}
      >
        {/* Header background */}
        <Animated.View
          style={[
            styles.headerBackground,
            {
              opacity: headerOpacity,
            },
          ]}
        >
          <View style={styles.headerGradient}>
            <View style={styles.headerContent}>
              <Text style={styles.bannerTitle}>
                Chọn loại tài liệu mặc định
              </Text>
              <Text style={styles.bannerDescription}>
                loại tài liệu được chọn sẽ được sử dụng làm mặc định khi quét và
                xử lý tài liệu
              </Text>
            </View>
            <View style={styles.bannerImageContainer}>
              <MaterialCommunityIcons
                name='file-document-outline'
                size={48}
                color='#fff'
              />
            </View>
          </View>
        </Animated.View>

        {/* Header title khi scroll */}
        <Animated.View
          style={[
            styles.floatingHeader,
            {
              opacity: headerTitleOpacity,
            },
          ]}
        >
          <Text style={styles.floatingHeaderTitle}>
            Chọn loại tài liệu mặc định
          </Text>
          {selectedDocTypeId &&
            docTypes.find(d => d.Id === selectedDocTypeId)?.TenLoai && (
              <Text style={styles.selectedDocTypeName} numberOfLines={1}>
                Đã chọn:{' '}
                {docTypes.find(d => d.Id === selectedDocTypeId)?.TenLoai}
              </Text>
            )}
        </Animated.View>
      </Animated.View>
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar barStyle='dark-content' backgroundColor='#fff' />

      <Header
        title='Loại tài liệu'
        RightComponent={
          <TouchableOpacity
            style={[
              styles.refreshButton,
              refreshing && styles.refreshingButton,
            ]}
            onPress={() => handleRefresh()}
            disabled={refreshing}
          >
            <Ionicons
              name={refreshing ? 'sync' : 'refresh'}
              size={22}
              color={refreshing ? '#999' : '#4361ee'}
            />
          </TouchableOpacity>
        }
      />

      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        {/* Search Box */}
        <Animated.View
          style={[
            styles.searchContainer,
            {
              opacity: searchAnim,
              transform: [
                {
                  translateY: searchAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [10, 0],
                  }),
                },
              ],
            },
          ]}
        >
          <Ionicons
            name='search'
            size={20}
            color='#999'
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder='Tìm kiếm loại tài liệu...'
            placeholderTextColor='#999'
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => setSearchQuery('')}
            >
              <Ionicons name='close-circle' size={18} color='#999' />
            </TouchableOpacity>
          )}
        </Animated.View>

        {/* Error Banner if needed */}
        {apiError && (
          <AnimatedView style={styles.errorBanner} duration={600} delay={300}>
            <Ionicons
              name='information-circle-outline'
              size={20}
              color='#fff'
            />
            <Text style={styles.errorBannerText}>{apiError.message}</Text>
          </AnimatedView>
        )}

        {/* Content */}
        {isLoading && !refreshing ? (
          renderSkeletonLoading()
        ) : (
          <SectionList
            sections={sectionedDocTypes}
            keyExtractor={(item, index) => `${item.Id || ''}-${index}`}
            renderItem={renderDocTypeItem}
            renderSectionHeader={renderSectionHeader}
            renderSectionFooter={renderSectionFooter}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={['#4361ee']}
                tintColor='#4361ee'
              />
            }
            ListHeaderComponent={renderAnimatedHeader}
            ListEmptyComponent={renderEmptyComponent}
            stickySectionHeadersEnabled={false}
            removeClippedSubviews={Platform.OS === 'android'}
            initialNumToRender={8}
            maxToRenderPerBatch={10}
            windowSize={10}
            onScroll={Animated.event(
              [{ nativeEvent: { contentOffset: { y: scrollYAnim } } }],
              { useNativeDriver: false },
            )}
            scrollEventThrottle={16}
          />
        )}
      </Animated.View>
      <Toast />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eaedf2',
    zIndex: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f6fa',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f6fa',
  },
  refreshingButton: {
    backgroundColor: '#eaecf5',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  animatedHeaderContainer: {
    overflow: 'hidden',
    marginBottom: 16,
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  headerGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4361ee',
    borderRadius: 16,
    padding: 20,
    height: '100%',
  },
  headerContent: {
    flex: 1,
  },
  bannerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#fff',
    marginBottom: 8,
  },
  bannerDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.85)',
    lineHeight: 20,
  },
  bannerImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    marginLeft: 16,
  },
  floatingHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  floatingHeaderTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  selectedDocTypeName: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
    maxWidth: width * 0.7,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 16,
    marginTop: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: '#333',
  },
  clearButton: {
    padding: 4,
  },
  errorBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ff4d4f',
    padding: 12,
    borderRadius: 12,
    marginBottom: 16,
  },
  errorBannerText: {
    color: '#fff',
    marginLeft: 8,
    flex: 1,
  },
  sectionHeader: {
    paddingVertical: 10,
    paddingHorizontal: 4,
    marginTop: 8,
    marginBottom: 12,
  },
  sectionHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  sectionHeaderText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#555',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  skeletonContainer: {
    flex: 1,
    paddingTop: 200,
  },
  skeletonItem: {
    height: 100,
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
  },
  listContent: {
    paddingBottom: 24,
    flexGrow: 1,
  },
  docTypeItemContainer: {
    marginBottom: 12,
  },
  docTypeItem: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedItem: {
    borderWidth: 2,
    borderColor: '#4361ee',
    backgroundColor: '#F5F7FF',
  },
  docTypeIcon: {
    width: 50,
    height: 50,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  docTypeContent: {
    flex: 1,
  },
  docTypeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
    lineHeight: 20,
  },
  docTypeCode: {
    fontSize: 13,
    color: '#666',
    marginBottom: 2,
  },
  docTypeDescription: {
    fontSize: 12,
    color: '#888',
  },
  checkmarkContainer: {
    justifyContent: 'center',
    marginLeft: 8,
  },
  checkmarkCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    marginTop: 40,
  },
  emptyIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  clearSearchButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#f0f2f5',
    borderRadius: 24,
  },
  clearSearchText: {
    color: '#4361ee',
    fontWeight: '500',
  },
  showMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    marginBottom: 12,
  },
  showMoreText: {
    color: '#4361ee',
    fontWeight: '500',
    marginRight: 4,
  },
  projectContent: {
    flex: 1,
    marginRight: 12,
  },
  projectTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
    width: '95%',
  },
  docTypeNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  pinButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f6fa',
  },
  pinnedButton: {
    backgroundColor: '#EDF2FF',
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  pinCountContainer: {
    backgroundColor: '#EDF2FF',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  pinCountText: {
    fontSize: 12,
    color: '#4361ee',
    fontWeight: '600',
  },
});

export default DocumentTypeScreen;
