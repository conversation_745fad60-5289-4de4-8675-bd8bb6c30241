import * as Updates from 'expo-updates';
import { Alert, Platform } from 'react-native';

/**
 * Service quản lý cập nhật ứng dụng
 */
export const UpdateService = {
  /**
   * Kiểm tra và tải cập nhật từ máy chủ
   * @returns Promise<boolean> - true nếu có cập nhật mới
   */
  checkForUpdates: async (): Promise<boolean> => {
    try {
      if (!__DEV__) {
        const update = await Updates.checkForUpdateAsync();
        
        if (update.isAvailable) {
          console.log('Có bản cập nhật mới!');
          
          // Tải bản cập nhật mới về
          const result = await Updates.fetchUpdateAsync();
          
          if (result.isNew) {
            console.log('Cập nhật đã được tải xuống và sẵn sàng áp dụng');
            return true;
          }
        } else {
          console.log('Ứng dụng đã ở phiên bản mới nhất');
        }
      }
      return false;
    } catch (error) {
      console.error('Lỗi khi kiểm tra cập nhật:', error);
      return false;
    }
  },

  /**
   * Hiển thị thông báo và hỏi người dùng có muốn khởi động lại để áp dụng cập nhật không
   */
  promptForReload: () => {
    Alert.alert(
      'Cập nhật ứng dụng',
      'Đã có phiên bản mới của ứng dụng. Bạn có muốn khởi động lại để áp dụng không?',
      [
        {
          text: 'Để sau',
          style: 'cancel'
        },
        {
          text: 'Khởi động lại',
          onPress: () => UpdateService.reloadApp()
        }
      ],
      { cancelable: false }
    );
  },

  /**
   * Khởi động lại ứng dụng để áp dụng cập nhật
   */
  reloadApp: () => {
    try {
      Updates.reloadAsync();
    } catch (error) {
      console.error('Lỗi khi khởi động lại ứng dụng:', error);
    }
  },

  /**
   * Xử lý cập nhật tự động
   * @param showPrompt - Có hiển thị thông báo cho người dùng hay không
   */
  handleUpdates: async (showPrompt: boolean = true): Promise<void> => {
    try {
      const hasUpdate = await UpdateService.checkForUpdates();
      
      if (hasUpdate) {
        if (showPrompt) {
          UpdateService.promptForReload();
        } else {
          // Tự động khởi động lại mà không cần hỏi
          UpdateService.reloadApp();
        }
      }
    } catch (error) {
      console.error('Lỗi khi xử lý cập nhật:', error);
    }
  }
}; 