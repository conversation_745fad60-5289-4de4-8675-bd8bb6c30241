import * as React from 'react';
import { Path, Svg, SvgProps } from 'react-native-svg';

interface AddScanSvgProps extends SvgProps {
  color?: string;
  size?: number;
}

export const AddScanSvg: React.FC<AddScanSvgProps> = ({
  color = '#94A3B8',
  size = 64,
  ...props
}) => {
  return (
    <Svg width={size} height={size} viewBox='0 0 64 64' fill='none' {...props}>
      <Path
        d='M32 8V56M8 32H56'
        stroke={color}
        strokeWidth='4'
        strokeLinecap='round'
        strokeDasharray='8 8'
      />
      <Path
        d='M16 8H8V16M48 8H56V16M16 56H8V48M48 56H56V48'
        stroke={color}
        strokeWidth='4'
        strokeLinecap='round'
      />
    </Svg>
  );
};
