import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import Header from '@/shared/components/molecules/Header';

const parseGender = (gender: string) => {
  if (gender.toLowerCase() === 'male' || gender === 'Nam') return 'Nam';
  if (gender.toLowerCase() === 'female' || gender === 'Nữ') return 'Nữ';
  return gender;
};

const CitizenDetailScreen = () => {
  const params = useLocalSearchParams();
  const router = useRouter();

  let info: CitizenInfo | null = null;
  try {
    if (typeof params.data === 'string') {
      info = JSON.parse(params.data);
    }
  } catch (e) {
    // fallback: try split by | or show error
  }

  if (!info) {
    return (
      <View style={styles.center}>
        <Text style={{ color: 'red' }}>Không đọc được dữ liệu QR!</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.root}>
      <Header title='Thông tin Căn cước công dân' />
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.card}>
          <View style={styles.rowBetween}>
            <Text style={styles.label}>Số CCCD</Text>
            <Text style={styles.valueBold}>{info.cccd}</Text>
          </View>
          {info.cmnd && (
            <View style={styles.rowBetween}>
              <Text style={styles.label}>Số CMND</Text>
              <Text style={styles.value}>{info.cmnd}</Text>
            </View>
          )}
          <View style={styles.rowBetween}>
            <Text style={styles.label}>Họ và tên</Text>
            <Text style={styles.value}>{info.name}</Text>
          </View>
          <View style={styles.rowBetween}>
            <Text style={styles.label}>Giới tính</Text>
            <Text style={styles.value}>{parseGender(info.gender)}</Text>
          </View>
          <View style={styles.rowBetween}>
            <Text style={styles.label}>Ngày sinh</Text>
            <Text style={styles.value}>{info.dob}</Text>
          </View>
          <View style={styles.rowBetween}>
            <Text style={styles.label}>Nơi thường trú</Text>
            <Text style={styles.value}>{info.address}</Text>
          </View>
          <View style={styles.rowBetween}>
            <Text style={styles.label}>Ngày cấp CCCD</Text>
            <Text style={styles.value}>{info.issued}</Text>
          </View>
        </View>
        <Text style={styles.note}>
          Thông tin này được lấy từ mã QR in trên CCCD, ứng dụng không chịu
          trách nhiệm về tính xác thực
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1890ff',
    paddingTop: 48,
    paddingBottom: 16,
    paddingHorizontal: 16,
    borderBottomLeftRadius: 18,
    borderBottomRightRadius: 18,
    justifyContent: 'space-between',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  scrollContent: {
    padding: 20,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
  rowBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    flexWrap: 'wrap',
  },
  label: {
    color: '#888',
    fontSize: 14,
    minWidth: 90,
  },
  value: {
    color: '#222',
    fontSize: 13,
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  valueBold: {
    color: '#111',
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'right',
  },
  note: {
    color: '#888',
    fontSize: 13,
    textAlign: 'center',
    marginTop: 10,
  },
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
});

export default CitizenDetailScreen;
