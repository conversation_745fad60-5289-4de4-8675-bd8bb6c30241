import React, { ReactNode } from 'react';
import {
  Animated,
  ViewStyle,
  TextStyle,
  ImageStyle,
  StyleProp,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
} from 'react-native';
import {
  useFadeIn,
  useScale,
  useSlideY,
  useAppearAnimation,
  usePressAnimation,
  useSkeletonAnimation,
} from './index';

interface AnimatedViewProps {
  children: ReactNode;
  style?: StyleProp<ViewStyle>;
  duration?: number;
  delay?: number;
  slideValue?: number;
  initialOpacity?: number;
  initialScale?: number;
}

interface AnimatedPressableProps extends TouchableOpacityProps {
  children: ReactNode;
  onPress: () => void;
  activeOpacity?: number;
}

interface SkeletonProps {
  style: StyleProp<ViewStyle>;
  duration?: number;
}

interface StaggerProps {
  children: ReactNode[];
  staggerDelay?: number;
  duration?: number;
  initialDelay?: number;
}

/**
 * Component với hiệu ứng fade in
 */
export const FadeInView = ({
  children,
  style,
  duration = 500,
  delay = 0,
  initialOpacity = 0,
}: AnimatedViewProps) => {
  const opacity = useFadeIn(duration, delay, initialOpacity);

  return <Animated.View style={[style, { opacity }]}>{children}</Animated.View>;
};

/**
 * Component với hiệu ứng scale
 */
export const ScaleView = ({
  children,
  style,
  duration = 500,
  delay = 0,
  initialScale = 0.9,
}: AnimatedViewProps) => {
  const scale = useScale(duration, delay, initialScale);

  return (
    <Animated.View style={[style, { transform: [{ scale }] }]}>
      {children}
    </Animated.View>
  );
};

/**
 * Component với hiệu ứng slide từ dưới lên
 */
export const SlideInView = ({
  children,
  style,
  duration = 500,
  delay = 0,
  slideValue = 50,
}: AnimatedViewProps) => {
  const translateY = useSlideY(duration, delay, slideValue);

  return (
    <Animated.View style={[style, { transform: [{ translateY }] }]}>
      {children}
    </Animated.View>
  );
};

/**
 * Component với hiệu ứng kết hợp fade + scale + slide
 */
export const AnimatedView = ({
  children,
  style,
  duration = 600,
  delay = 0,
  slideValue = 30,
}: AnimatedViewProps) => {
  const { style: animatedStyle } = useAppearAnimation(
    duration,
    delay,
    slideValue,
  );

  return (
    <Animated.View style={[style, animatedStyle]}>{children}</Animated.View>
  );
};

/**
 * Button với hiệu ứng press animation
 */
export const AnimatedButton = ({
  children,
  style,
  onPress,
  activeOpacity = 0.7,
  ...props
}: AnimatedPressableProps) => {
  const { handlers, style: animatedStyle } = usePressAnimation();

  return (
    <Animated.View style={animatedStyle}>
      <TouchableOpacity
        style={style}
        onPress={onPress}
        activeOpacity={activeOpacity}
        onPressIn={handlers.onPressIn}
        onPressOut={handlers.onPressOut}
        {...props}
      >
        {children}
      </TouchableOpacity>
    </Animated.View>
  );
};

/**
 * Component tạo hiệu ứng skeleton loading
 */
export const SkeletonView = ({ style, duration = 1500 }: SkeletonProps) => {
  const opacity = useSkeletonAnimation(duration);

  return <Animated.View style={[style, { opacity }]} />;
};

/**
 * Component để tạo các item xuất hiện lần lượt
 */
export const StaggeredList = ({
  children,
  staggerDelay = 50,
  duration = 400,
  initialDelay = 0,
}: StaggerProps) => {
  return (
    <>
      {children.map((child, index) => (
        <FadeInView
          key={index}
          duration={duration}
          delay={initialDelay + index * staggerDelay}
        >
          <SlideInView
            duration={duration}
            delay={initialDelay + index * staggerDelay}
            slideValue={20}
          >
            {child}
          </SlideInView>
        </FadeInView>
      ))}
    </>
  );
};

/**
 * Component tạo animated text với fade in
 */
export const AnimatedText = ({
  children,
  style,
  duration = 500,
  delay = 0,
}: AnimatedViewProps) => {
  const opacity = useFadeIn(duration, delay);

  return <Animated.Text style={[style, { opacity }]}>{children}</Animated.Text>;
};

/**
 * Component tạo animated image với fade in
 */
export const AnimatedImage = ({
  style,
  duration = 800,
  delay = 0,
  ...props
}: any) => {
  const opacity = useFadeIn(duration, delay);

  return <Animated.Image {...props} style={[style, { opacity }]} />;
};

/**
 * Component với hiệu ứng reveal
 */
export const RevealView = ({
  children,
  style,
  duration = 600,
  delay = 0,
}: AnimatedViewProps) => {
  const width = useFadeIn(duration, delay, 0);

  return (
    <View style={[style, { overflow: 'hidden' }]}>
      <Animated.View
        style={{
          width: width.interpolate({
            inputRange: [0, 1],
            outputRange: ['0%', '100%'],
          }),
        }}
      >
        {children}
      </Animated.View>
    </View>
  );
};
