# Tối ưu Splash Screen & Loading States

## Tổng quan

Tài liệu này mô tả việc cải tiến splash screen và loading states để thay thế màn hình trắng khi khởi động ứng dụng.

## Vấn đề đã giải quyết

- ❌ **Trước:** Màn hình trắng khi app khởi động
- ✅ **Sau:** Splash screen đẹp với gradient và animation mượt mà

## Các component đã tạo/cải tiến

### 1. SplashScreen Component ⭐ **CẢI TIẾN**

**File:** `src/features/splash/components/SplashScreen.tsx`

**Tính năng mới:**
- 🎨 **Gradient background** với màu xanh dương chuyển tiếp
- ✨ **Animation mượt mà** cho logo và text
- 🔄 **Logo rotation** animation tinh tế
- 📊 **Progress bar** với animation
- 🌟 **Background pattern** trang trí
- 💫 **Loading dots** animation
- 📱 **Logo thực tế** từ assets
- 📝 **Dynamic loading text** thay đổi theo thời gian

### 2. LoadingOverlay Component ⭐ **MỚI**

**File:** `src/shared/components/molecules/LoadingOverlay.tsx`

**Các loại loading:**
- `default`: Loading cơ bản với modal
- `splash`: Full screen loading với gradient
- `processing`: Loading với blur effect và progress
- `uploading`: Loading cho upload file

**Props:**
- `visible`: Hiển thị/ẩn loading
- `message`: Thông điệp loading
- `type`: Loại loading
- `progress`: Tiến độ (0-100)
- `onComplete`: Callback khi hoàn thành

### 3. useAppLoading Hook ⭐ **MỚI**

**File:** `src/shared/hooks/useAppLoading.ts`

**Chức năng:**
- Quản lý trạng thái loading
- Cập nhật progress và message
- Predefined loading states
- Type-safe loading management

## Cấu hình đã cập nhật

### app.config.ts

```typescript
splash: {
  image: './src/assets/images/appstore.png',
  resizeMode: 'contain',
  backgroundColor: '#1E3A8A', // Màu xanh dương
},
```

### _layout.tsx

- Tăng thời gian hiển thị splash từ 2s lên 3s
- Đủ thời gian cho animation hoàn thành

## Hướng dẫn sử dụng

### 1. Sử dụng LoadingOverlay

```tsx
import { LoadingOverlay } from '@/shared/components/molecules/LoadingOverlay';

function MyComponent() {
  const [loading, setLoading] = useState(false);
  
  return (
    <>
      {/* Your content */}
      
      <LoadingOverlay
        visible={loading}
        message="Đang xử lý..."
        type="processing"
        progress={50}
      />
    </>
  );
}
```

### 2. Sử dụng useAppLoading Hook

```tsx
import { useAppLoading } from '@/shared/hooks/useAppLoading';

function MyComponent() {
  const {
    loadingState,
    showLoading,
    hideLoading,
    showProcessingLoading,
    updateProgress,
  } = useAppLoading();

  const handleProcess = async () => {
    showProcessingLoading('Đang xử lý tài liệu...');
    
    // Simulate progress
    for (let i = 0; i <= 100; i += 10) {
      updateProgress(i);
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    hideLoading();
  };

  return (
    <>
      <Button onPress={handleProcess} label="Xử lý" />
      
      <LoadingOverlay
        visible={loadingState.visible}
        message={loadingState.message}
        type={loadingState.type}
        progress={loadingState.progress}
      />
    </>
  );
}
```

### 3. Demo Component

```tsx
import { SplashDemo } from '@/features/demo/screens/SplashDemo';

// Sử dụng để test tất cả loading states
<SplashDemo />
```

## Animation Details

### Splash Screen Animations

1. **Logo Entrance:**
   - Scale từ 0 → 1 với easing back
   - Opacity từ 0 → 1
   - Duration: 800ms

2. **Logo Rotation:**
   - Rotate 360° với easing sin
   - Duration: 1000ms

3. **Progress Animation:**
   - Width từ 0 → 60% screen width
   - Duration: 2000ms

4. **Loading Dots:**
   - Opacity và scale animation
   - Staggered animation cho 3 dots

### LoadingOverlay Animations

1. **Entrance:**
   - Fade in với scale spring
   - Duration: 300ms

2. **Exit:**
   - Fade out với scale down
   - Duration: 200ms

## Best Practices

1. **Chọn loại loading phù hợp:**
   - `default`: Cho loading ngắn (< 2s)
   - `processing`: Cho xử lý có progress
   - `splash`: Cho app initialization
   - `uploading`: Cho upload file

2. **Cập nhật progress và message:**
   - Luôn cập nhật progress cho user feedback
   - Thay đổi message theo giai đoạn xử lý

3. **Timing:**
   - Splash screen: 3s (đủ cho animation)
   - Loading overlay: Tùy theo tác vụ
   - Minimum 500ms để user nhận biết

4. **Accessibility:**
   - Có message mô tả rõ ràng
   - Progress indicator cho screen reader

## Lợi ích

- **UX tốt hơn:** Không còn màn hình trắng
- **Professional:** Splash screen đẹp, chuyên nghiệp
- **Feedback:** User biết app đang làm gì
- **Smooth:** Animation mượt mà, không giật lag
- **Flexible:** Nhiều loại loading cho các tình huống khác nhau
- **Maintainable:** Code dễ maintain và extend
