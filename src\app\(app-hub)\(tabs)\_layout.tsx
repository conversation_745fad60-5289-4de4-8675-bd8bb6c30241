import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import {
  CurvedBottomBar,
  CurvedBottomBarExpo,
} from 'react-native-curved-bottom-bar';
import { Ionicons, AntDesign, MaterialIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import LottieView from 'lottie-react-native';
import Home from './home';
import Profile from './profile';
import { Colors } from 'react-native-ui-lib';
import { NavigationContainer } from '@react-navigation/native';

export default function AppHubTabLayout() {
  const router = useRouter();

  const renderTabBar = ({ routeName, selectedTab, navigate }: any) => {
    const color = routeName === selectedTab ? Colors.buttonPrimary : '#999';
    let icon;
    if (routeName === 'home') {
      icon = <Ionicons name='apps' size={24} color={color} />;
    } else if (routeName === 'profile') {
      icon = <AntDesign name='user' size={24} color={color} />;
    }
    return (
      <TouchableOpacity
        onPress={() => navigate(routeName)}
        style={{ alignItems: 'center', justifyContent: 'center' }}
      >
        {icon}
      </TouchableOpacity>
    );
  };

  const goToAppSelection = () => {
    // Chức năng chọn ứng dụng
    router.push('/feature-selection');
  };

  return (
    <CurvedBottomBarExpo.Navigator
      id='app-hub-tabs'
      screenListeners={{}}
      defaultScreenOptions={{}}
      shadowStyle={{
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }}
      type='DOWN'
      style={{ backgroundColor: 'transparent' }}
      height={70}
      circleWidth={60}
      width={undefined}
      borderColor='transparent'
      borderWidth={0}
      circlePosition='CENTER'
      bgColor='white'
      initialRouteName='home'
      screenOptions={{ headerShown: false }}
      borderTopLeftRight
      backBehavior='none'
      renderCircle={() => (
        <TouchableOpacity
          onPress={goToAppSelection}
          style={{
            width: 70,
            height: 70,
            borderRadius: 40,
            backgroundColor: Colors.buttonPrimary,
            justifyContent: 'center',
            alignItems: 'center',
            bottom: 28,
          }}
        >
          <MaterialIcons name='add-circle-outline' size={32} color='white' />
        </TouchableOpacity>
      )}
      tabBar={renderTabBar}
    >
      <CurvedBottomBar.Screen
        name='home'
        position='LEFT'
        component={() => <Home />}
      />
      <CurvedBottomBar.Screen
        name='profile'
        position='RIGHT'
        component={() => <Profile />}
      />
    </CurvedBottomBarExpo.Navigator>
  );
}
