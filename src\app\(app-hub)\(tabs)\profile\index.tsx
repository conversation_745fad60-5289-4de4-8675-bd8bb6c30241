import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Image,
} from 'react-native';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Ionicons, MaterialIcons, FontAwesome } from '@expo/vector-icons';
import { useSession } from '@features/auth/context/ctx';
import { Colors } from 'react-native-ui-lib';
import { SafeAreaView } from 'react-native-safe-area-context';

const MENU_ITEMS = [
  {
    id: 'account',
    title: 'Tài khoản',
    icon: 'person-outline',
    iconType: 'ionicons',
    route: '/settings',
  },
  {
    id: 'notifications',
    title: 'Thông báo',
    icon: 'notifications-outline',
    iconType: 'ionicons',
    route: '/settings',
  },
  {
    id: 'appearance',
    title: 'Giao diện',
    icon: 'color-palette-outline',
    iconType: 'ionicons',
    route: '/settings',
  },
  {
    id: 'security',
    title: '<PERSON><PERSON>o mật',
    icon: 'shield-outline',
    iconType: 'ionicons',
    route: '/settings',
  },
  {
    id: 'help',
    title: 'Trợ giúp & Hỗ trợ',
    icon: 'help-circle-outline',
    iconType: 'ionicons',
    route: '/settings',
  },
  {
    id: 'about',
    title: 'Về ứng dụng',
    icon: 'information-circle-outline',
    iconType: 'ionicons',
    route: '/(dashboard)/about',
  },
];

interface UserData {
  TenDangNhap?: string;
  TenDonVi?: string;
}
export default function AppHubProfile() {
  const router = useRouter();
  const { session, signOut } = useSession();

  const userData: UserData = session ?? {};
  const handleMenuItemPress = (item: (typeof MENU_ITEMS)[0]) => {
    router.push(item.route);
  };

  const renderIcon = (item: (typeof MENU_ITEMS)[0]) => {
    if (item.iconType === 'ionicons') {
      return (
        <Ionicons
          name={item.icon as any}
          size={22}
          color={Colors.buttonPrimary}
        />
      );
    }
    return (
      <FontAwesome
        name={item.icon as any}
        size={22}
        color={Colors.buttonPrimary}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style='dark' />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Hồ sơ</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* User Profile Card */}
        <View style={styles.profileCard}>
          <View style={styles.profileHeader}>
            <Image
              source={{ uri: 'https://randomuser.me/api/portraits/men/32.jpg' }}
              style={styles.avatar}
            />
            <View style={styles.profileInfo}>
              <Text style={styles.userName}>{userData.TenDangNhap}</Text>
              <Text style={styles.userEmail}>{userData.TenDonVi}</Text>
              <TouchableOpacity style={styles.editButton}>
                <Text style={styles.editButtonText}>Chỉnh sửa</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>4</Text>
              <Text style={styles.statLabel}>Ứng dụng</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>12</Text>
              <Text style={styles.statLabel}>Hoạt động</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>85%</Text>
              <Text style={styles.statLabel}>Hoàn thành</Text>
            </View>
          </View>
        </View>

        {/* Menu Items */}
        <View style={styles.menuContainer}>
          {MENU_ITEMS.map(item => (
            <TouchableOpacity
              key={item.id}
              style={styles.menuItem}
              onPress={() => handleMenuItemPress(item)}
            >
              <View style={styles.menuIconContainer}>{renderIcon(item)}</View>
              <Text style={styles.menuTitle}>{item.title}</Text>
              <MaterialIcons name='chevron-right' size={24} color='#888' />
            </TouchableOpacity>
          ))}
        </View>

        {/* Logout Button */}
        <TouchableOpacity style={styles.logoutButton} onPress={signOut}>
          <Ionicons name='log-out-outline' size={22} color='#FF5A5A' />
          <Text style={styles.logoutText}>Đăng xuất</Text>
        </TouchableOpacity>

        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>Phiên bản 1.0.0</Text>
        </View>

        <View style={{ height: 100 }} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#222',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  profileCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 2,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#888',
    marginBottom: 8,
  },
  editButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.buttonPrimary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  editButtonText: {
    color: Colors.buttonPrimary,
    fontSize: 12,
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 15,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statDivider: {
    width: 1,
    backgroundColor: '#eee',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#888',
  },
  menuContainer: {
    backgroundColor: '#fff',
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  menuIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 8,
    backgroundColor: '#f0f2f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  menuTitle: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    borderRadius: 16,
    paddingVertical: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FF5A5A',
    marginLeft: 8,
  },
  versionContainer: {
    alignItems: 'center',
    padding: 10,
  },
  versionText: {
    fontSize: 12,
    color: '#888',
  },
});
