import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import NetInfo from '@react-native-community/netinfo';
import { API_CONFIG } from '@core/api/config';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Create main API instance
export const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
  },
});

// Create image API instance with different configuration
export const imageApi = axios.create({
  baseURL: API_CONFIG.IMAGE_API_URL,
  timeout: 120000, // 2 minutes timeout for image uploads
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'multipart/form-data',
  },
  // Add these configurations to handle large files
  maxContentLength: Infinity,
  maxBodyLength: Infinity,
  // Add these configurations to handle CORS and SSL
  withCredentials: true,
  validateStatus: function (status) {
    return status >= 200 && status < 500;
  },
});

// Request interceptor for main API
api.interceptors.request.use(
  async (config) => {
    // Check network connectivity
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      throw new Error('No internet connection');
    }

    // Log full URL and request details
    const fullUrl = `${config.baseURL}${config.url}`;
    console.log('Request URL:', fullUrl);
    console.log('Request Config:', {
      method: config.method,
      headers: config.headers,
      params: config.params,
      data: config.data
    });

    // Add auth token if available
    const token = await getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// Request interceptor for image API
imageApi.interceptors.request.use(
  async (config) => {
    try {
      // Check network connectivity
      const netInfo = await NetInfo.fetch();
      if (!netInfo.isConnected) {
        throw new Error('No internet connection');
      }

      // Add auth token if available
      const token = await getToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // Log full URL and request details
      const fullUrl = `${config.baseURL}${config.url}`;
      console.log('Full Request URL:', fullUrl);

      // Log detailed request information
      console.log('Request Details:', {
        method: config.method,
        headers: config.headers,
        params: config.params,
      });

      // Log FormData details
      if (config.data instanceof FormData) {
        console.log('FormData Contents:');
        const formData = config.data as FormData;
        for (const [key, value] of formData.entries()) {
          if (value instanceof File) {
            console.log(`- ${key}:`, {
              name: value.name,
              type: value.type,
              size: value.size,
              lastModified: value.lastModified,
            });
          } else {
            console.log(`- ${key}:`, value);
          }
        }
      } else {
        console.log('Request Data:', config.data);
      }

      return config;
    } catch (error) {
      console.error('Request preparation error:', error);
      return Promise.reject(error);
    }
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for main API
api.interceptors.response.use(
  (response) => {
    console.log('Response:', {
      status: response.status,
      data: response.data,
      headers: response.headers
    });
    return response;
  },
  async (error: AxiosError) => {
    console.error('Response Error:', {
      url: error.config?.url,
      baseURL: error.config?.baseURL,
      fullUrl: `${error.config?.baseURL}${error.config?.url}`,
      method: error.config?.method,
      params: error.config?.params,
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });

    if (error.response?.status === 401) {
      // Handle token expiration
      const newToken = await refreshToken();
      if (newToken) {
        const config = error.config as AxiosRequestConfig;
        config.headers = {
          ...config.headers,
          Authorization: `Bearer ${newToken}`,
        };
        return api(config);
      }
    }
    return Promise.reject(error);
  }
);

// Response interceptor for image API
imageApi.interceptors.response.use(
  (response) => {
    console.log('Image Upload Success:', {
      status: response.status,
      data: response.data,
    });
    return response;
  },
  async (error: AxiosError) => {
    // Log error details
    console.error('Image Upload Error:', {
      url: error.config?.url,
      baseURL: error.config?.baseURL,
      fullUrl: `${error.config?.baseURL}${error.config?.url}`,
      method: error.config?.method,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      error: {
        message: error.message,
        code: error.code,
        name: error.name,
      },

      // Response information if available
      response: error.response ? {
        status: error.response.status,
        statusText: error.response.statusText,
        headers: error.response.headers,
        data: error.response.data,
      } : null,

      // Request object if available
      request: error.request ? {
        method: error.request.method,
        url: error.request.url,
        headers: error.request.headers,
        readyState: error.request.readyState,
      } : null,

      // Additional error information
      isAxiosError: error.isAxiosError,
      toJSON: error.toJSON(),
    });

    if (error.response?.status === 401) {
      // Handle token expiration
      const newToken = await refreshToken();
      if (newToken) {
        const config = error.config as AxiosRequestConfig;
        config.headers = {
          ...config.headers,
          Authorization: `Bearer ${newToken}`,
        };
        return imageApi(config);
      }
    }
    return Promise.reject(error);
  }
);

// Helper function to get error message
const getErrorMessage = (error: AxiosError): string => {
  if (error.response) {
    switch (error.response.status) {
      case 400:
        return 'Bad request';
      case 401:
        return 'Unauthorized access';
      case 403:
        return 'Access forbidden';
      case 404:
        return 'Resource not found';
      case 408:
        return 'Request timeout';
      case 500:
        return 'Server error';
      default:
        return 'An error occurred';
    }
  }
  if (error.request) {
    return 'No response from server';
  }
  return error.message || 'Unknown error occurred';
};

// Token management functions
const getToken = async (): Promise<string | null> => {
  return await AsyncStorage.getItem('accessToken');
};

const refreshToken = async (): Promise<string | null> => {
  // Implement token refresh logic
  return null;
};
