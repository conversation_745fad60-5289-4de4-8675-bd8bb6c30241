import * as SQLite from 'expo-sqlite';
import { databasePromise } from '../index';

export interface ScanSession {
    id: string;
    title: string;
    images: string; // JSON string of image paths
    createdAt: number;
    updatedAt: number;
    siteName?: string;
    isSent: boolean;
    size?: number;
    userId?: string;
}

export class ScanSessionRepository {
    private db: SQLite.SQLiteDatabase | null = null;

    constructor() {
        this.initialize();
    }

    private async initialize() {
        this.db = await databasePromise;
        await this.createTable();
    }

    private async createTable() {
        if (!this.db) await this.initialize();

        const createTableQuery = `
            CREATE TABLE IF NOT EXISTS scan_sessions (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                images TEXT NOT NULL,
                created_at INTEGER NOT NULL,
                updated_at INTEGER NOT NULL,
                site_name TEXT,
                is_sent BOOLEAN DEFAULT FALSE,
                size INTEGER,
                user_id TEXT
            );
        `;

        try {
            await this.db!.execAsync(createTableQuery);
        } catch (error) {
            console.error('Error creating scan_sessions table:', error);
        }
    }

    async getAllSessions(userId?: string): Promise<ScanSession[]> {
        if (!this.db) await this.initialize();

        let query = 'SELECT * FROM scan_sessions';
        const params: any[] = [];

        if (userId) {
            query += ' WHERE user_id = ?';
            params.push(userId);
        }

        query += ' ORDER BY created_at DESC';

        const results = await this.db!.getAllAsync<any>(query, params);
        return results.map(row => this.mapRowToSession(row));
    }

    async getById(id: string): Promise<ScanSession | null> {
        if (!this.db) await this.initialize();

        const query = 'SELECT * FROM scan_sessions WHERE id = ?';
        const result = await this.db!.getFirstAsync<any>(query, [id]);

        if (!result) return null;

        return this.mapRowToSession(result);
    }

    async add(session: Omit<ScanSession, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
        if (!this.db) await this.initialize();

        const id = this.generateUUID();
        const now = Date.now();

        const query = `
            INSERT INTO scan_sessions (
                id, title, images, created_at, updated_at, 
                site_name, is_sent, size, user_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        await this.db!.runAsync(query, [
            id,
            session.title,
            session.images,
            now,
            now,
            session.siteName || null,
            session.isSent ? 1 : 0,
            session.size || null,
            session.userId || null
        ]);

        return id;
    }

    async update(id: string, session: Partial<Omit<ScanSession, 'id' | 'createdAt' | 'updatedAt'>>): Promise<void> {
        if (!this.db) await this.initialize();

        const now = Date.now();
        const updateFields: string[] = [];
        const params: any[] = [];

        // Build update fields and parameters
        Object.entries(session).forEach(([key, value]) => {
            if (value !== undefined) {
                let fieldName = key;

                // Convert camelCase to snake_case for DB column names
                fieldName = fieldName.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);

                updateFields.push(`${fieldName} = ?`);

                // Handle boolean values
                if (typeof value === 'boolean') {
                    params.push(value ? 1 : 0);
                } else {
                    params.push(value);
                }
            }
        });

        // Always update the updated_at field
        updateFields.push('updated_at = ?');
        params.push(now);

        // Add the ID for the WHERE clause
        params.push(id);

        const query = `
            UPDATE scan_sessions 
            SET ${updateFields.join(', ')} 
            WHERE id = ?
        `;

        await this.db!.runAsync(query, params);
    }

    async delete(id: string): Promise<void> {
        if (!this.db) await this.initialize();

        const query = 'DELETE FROM scan_sessions WHERE id = ?';
        await this.db!.runAsync(query, [id]);
    }

    async deleteByUserId(userId: string): Promise<void> {
        if (!this.db) await this.initialize();

        const query = 'DELETE FROM scan_sessions WHERE user_id = ?';
        await this.db!.runAsync(query, [userId]);
    }

    async deleteOldSessions(daysOld: number): Promise<number> {
        if (!this.db) await this.initialize();

        const cutoffTime = Date.now() - (daysOld * 24 * 60 * 60 * 1000);
        const query = 'DELETE FROM scan_sessions WHERE created_at < ?';

        const result = await this.db!.runAsync(query, [cutoffTime]);
        return result.changes || 0;
    }

    private mapRowToSession(row: any): ScanSession {
        return {
            id: row.id,
            title: row.title,
            images: row.images,
            createdAt: row.created_at,
            updatedAt: row.updated_at,
            siteName: row.site_name,
            isSent: Boolean(row.is_sent),
            size: row.size,
            userId: row.user_id
        };
    }

    // Helper methods to convert between string and array
    static imagesToString(images: string[]): string {
        return JSON.stringify(images);
    }

    static stringToImages(imagesString: string): string[] {
        try {
            return JSON.parse(imagesString);
        } catch (error) {
            console.error('Error parsing images string:', error);
            return [];
        }
    }

    // Simple UUID generator
    private generateUUID(): string {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
} 