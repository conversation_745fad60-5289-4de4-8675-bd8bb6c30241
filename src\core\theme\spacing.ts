const baseUnit = 4;

export const spacing = {
    // Base spacing units
    xs: baseUnit, // 4
    sm: baseUnit * 2, // 8
    md: baseUnit * 3, // 12
    lg: baseUnit * 4, // 16
    xl: baseUnit * 6, // 24
    xxl: baseUnit * 8, // 32
    xxxl: baseUnit * 12, // 48

    // Common spacing values
    page: baseUnit * 4, // 16
    section: baseUnit * 6, // 24
    container: baseUnit * 8, // 32

    // Component specific spacing
    button: {
        padding: baseUnit * 2, // 8
        margin: baseUnit, // 4
    },
    input: {
        padding: baseUnit * 2, // 8
        margin: baseUnit, // 4
    },
    card: {
        padding: baseUnit * 3, // 12
        margin: baseUnit * 2, // 8
    },
    list: {
        itemSpacing: baseUnit * 2, // 8
        sectionSpacing: baseUnit * 4, // 16
    },
} as const;

export type Spacing = typeof spacing; 