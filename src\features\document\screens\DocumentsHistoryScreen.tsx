import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  TextInput,
  Animated,
  FlatList,
  Image,
  Alert,
} from 'react-native';
import { Ionicons, MaterialIcons, AntDesign } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Colors } from 'react-native-ui-lib';
import DocumentList from '@/features/document/components/DocumentList';
import * as FileSystem from 'expo-file-system';
import { Button } from 'react-native-ui-lib';
import { ActionSheet } from 'react-native-ui-lib';
import Share from 'react-native-share';
import ScanService from '@/features/scan/services/scanService';
import { SafeAreaView } from 'react-native-safe-area-context';
interface ScanFile {
  id: string;
  name: string;
  path: string;
  date: Date;
  timestamp: string;
}

export default function DocumentsListScreen() {
  const router = useRouter();
  const [searchVisible, setSearchVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [showSortOptions, setShowSortOptions] = useState(false);
  const [sortBy, setSortBy] = useState('date');
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'recent', 'favorite', 'shared'
  const [scanFiles, setScanFiles] = useState<ScanFile[]>([]);
  const [filteredFiles, setFilteredFiles] = useState<ScanFile[]>([]);
  const [selectedFile, setSelectedFile] = useState<ScanFile | null>(null);
  const [showActionSheet, setShowActionSheet] = useState(false);

  // Animation value for search bar
  const searchBarAnim = React.useRef(new Animated.Value(0)).current;
  // Animation values for SelectedProjectDocInfo component
  const projectInfoOpacity = React.useRef(new Animated.Value(0)).current;
  const projectInfoSlide = React.useRef(new Animated.Value(20)).current;

  // Parse scan file name to extract date and time
  const parseScanFileName = (fileName: string): ScanFile | null => {
    // Format: DOCUMENT_SCAN_0_20250613_155856.jpg
    const match = fileName.match(/DOCUMENT_SCAN_\d+_(\d{8})_(\d{6})\.jpg/);
    if (!match) return null;

    const dateStr = match[1];
    const timeStr = match[2];

    const year = parseInt(dateStr.substring(0, 4));
    const month = parseInt(dateStr.substring(4, 6)) - 1; // Month is 0-indexed
    const day = parseInt(dateStr.substring(6, 8));

    const hour = parseInt(timeStr.substring(0, 2));
    const minute = parseInt(timeStr.substring(2, 4));
    const second = parseInt(timeStr.substring(4, 6));

    const date = new Date(year, month, day, hour, minute, second);

    return {
      id: fileName,
      name: fileName,
      path: `${FileSystem.documentDirectory}${fileName}`,
      date,
      timestamp: `${dateStr}_${timeStr}`,
    };
  };

  const loadScanFiles = async () => {
    try {
      const docDir = FileSystem.documentDirectory || '';
      const files = await FileSystem.readDirectoryAsync(docDir);

      const scanFileList: ScanFile[] = [];

      for (const file of files) {
        if (file.startsWith('DOCUMENT_SCAN_') && file.endsWith('.jpg')) {
          const scanFile = parseScanFileName(file);
          if (scanFile) {
            scanFileList.push(scanFile);
          }
        }
      }

      // Sort by date (newest first)
      scanFileList.sort((a, b) => b.date.getTime() - a.date.getTime());

      setScanFiles(scanFileList);
      setFilteredFiles(scanFileList);
    } catch (error) {
      console.error('Error loading scan files:', error);
    }
  };

  const filterFilesByTab = (tab: string) => {
    switch (tab) {
      case 'recent':
        // Show files from last 7 days
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        return scanFiles.filter(file => file.date >= oneWeekAgo);
      case 'favorite':
        // For now, return empty array (can be implemented later with favorites storage)
        return [];
      case 'shared':
        // For now, return empty array (can be implemented later with shared storage)
        return [];
      default:
        return scanFiles;
    }
  };

  const filterFilesBySearch = (files: ScanFile[], search: string) => {
    if (!search.trim()) return files;
    return files.filter(file =>
      file.name.toLowerCase().includes(search.toLowerCase()),
    );
  };

  const sortFiles = (files: ScanFile[], sortType: string) => {
    const sortedFiles = [...files];
    switch (sortType) {
      case 'date':
        return sortedFiles.sort((a, b) => b.date.getTime() - a.date.getTime());
      case 'name':
        return sortedFiles.sort((a, b) => a.name.localeCompare(b.name));
      case 'type':
        return sortedFiles; // Already sorted by type (all are jpg)
      default:
        return sortedFiles;
    }
  };

  const scanService = ScanService.getInstance();

  useEffect(() => {
    loadScanFiles();
  }, []);

  const handleTest = async () => {
    const documents = await scanService.getAllDocuments();
    console.log('documents', documents);
  };

  useEffect(() => {
    let filtered = filterFilesByTab(activeTab);
    filtered = filterFilesBySearch(filtered, searchText);
    filtered = sortFiles(filtered, sortBy);
    setFilteredFiles(filtered);
  }, [activeTab, searchText, sortBy, scanFiles]);

  React.useEffect(() => {
    Animated.timing(searchBarAnim, {
      toValue: searchVisible ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();

    // Animate the project info component on mount
    Animated.parallel([
      Animated.timing(projectInfoOpacity, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(projectInfoSlide, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  }, [searchVisible, projectInfoOpacity, projectInfoSlide]);

  const searchBarHeight = searchBarAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 50],
  });

  const searchBarOpacity = searchBarAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  const toggleSearch = () => {
    setSearchVisible(!searchVisible);
    if (searchVisible) {
      setSearchText('');
    }
  };

  const handleSort = (sortType: 'date' | 'name' | 'type') => {
    setSortBy(sortType);
    setShowSortOptions(false);
  };

  const onTest = async () => {
    await handleTest();
  };

  const handleFileAction = (file: ScanFile) => {
    setSelectedFile(file);
    setShowActionSheet(true);
  };

  const handleShare = async () => {
    if (!selectedFile) return;

    try {
      await Share.open({
        url: selectedFile.path,
        type: 'image/jpeg',
        failOnCancel: false,
      });
    } catch (error) {
      console.error('Error sharing file:', error);
    }
    setShowActionSheet(false);
  };

  const handleSend = async () => {
    if (!selectedFile) return;

    try {
      // Here you can implement sending to specific apps or services
      // For now, we'll use the share functionality
      await Share.open({
        url: selectedFile.path,
        type: 'image/jpeg',
        title: 'Gửi tài liệu',
        message: `Tài liệu scan từ ${selectedFile.date.toLocaleDateString('vi-VN')}`,
        failOnCancel: false,
      });
    } catch (error) {
      console.error('Error sending file:', error);
    }
    setShowActionSheet(false);
  };

  const handleDelete = async () => {
    if (!selectedFile) return;

    Alert.alert('Xác nhận xóa', 'Bạn có chắc chắn muốn xóa tài liệu này?', [
      {
        text: 'Hủy',
        style: 'cancel',
      },
      {
        text: 'Xóa',
        style: 'destructive',
        onPress: async () => {
          try {
            await FileSystem.deleteAsync(selectedFile.path);
            // Remove from state
            setScanFiles(prev =>
              prev.filter(file => file.id !== selectedFile.id),
            );
            setFilteredFiles(prev =>
              prev.filter(file => file.id !== selectedFile.id),
            );
          } catch (error) {
            console.error('Error deleting file:', error);
            Alert.alert('Lỗi', 'Không thể xóa tài liệu');
          }
        },
      },
    ]);
    setShowActionSheet(false);
  };

  const renderScanFile = ({ item }: { item: ScanFile }) => (
    <TouchableOpacity
      style={styles.scanFileCard}
      onPress={() => {
        // Navigate to scan detail or open image
        console.log('Open scan file:', item.path);
      }}
      onLongPress={() => handleFileAction(item)}
      activeOpacity={0.8}
    >
      <Image
        source={{ uri: item.path }}
        style={styles.scanFileThumbnail}
        resizeMode='cover'
      />
      <View style={styles.scanFileOverlay}>
        <View style={styles.scanFileInfo}>
          <Text style={styles.scanFileDate} numberOfLines={1}>
            {item.date.toLocaleDateString('vi-VN')}
          </Text>
          <Text style={styles.scanFileTime} numberOfLines={1}>
            {item.date.toLocaleTimeString('vi-VN', {
              hour: '2-digit',
              minute: '2-digit',
            })}
          </Text>
        </View>
      </View>

      {/* Action indicator */}
      <View style={styles.actionIndicator}>
        <Ionicons name='ellipsis-horizontal' size={16} color='#fff' />
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name='document-outline' size={64} color='#ccc' />
      <Text style={styles.emptyStateText}>
        {activeTab === 'recent'
          ? 'Không có tài liệu gần đây'
          : activeTab === 'favorite'
            ? 'Không có tài liệu yêu thích'
            : activeTab === 'shared'
              ? 'Không có tài liệu đã chia sẻ'
              : 'Không có tài liệu nào'}
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle='dark-content' />
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Tài liệu</Text>
        <View style={styles.headerRight}>
          <TouchableOpacity style={styles.iconButton} onPress={toggleSearch}>
            <Ionicons
              name={searchVisible ? 'close-outline' : 'search-outline'}
              size={22}
              color='#333'
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => setShowSortOptions(!showSortOptions)}
          >
            <MaterialIcons name='sort' size={22} color='#333' />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Bar */}
      <Animated.View
        style={[
          styles.searchBarContainer,
          {
            height: searchBarHeight,
            opacity: searchBarOpacity,
            overflow: 'hidden',
          },
        ]}
      >
        <View style={styles.searchBar}>
          <Ionicons
            name='search'
            size={20}
            color='#999'
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder='Tìm kiếm tài liệu...'
            value={searchText}
            onChangeText={setSearchText}
            autoFocus={searchVisible}
            returnKeyType='search'
            autoCapitalize='none'
            autoCorrect={false}
            clearButtonMode='while-editing'
          />
          {searchText ? (
            <TouchableOpacity onPress={() => setSearchText('')}>
              <Ionicons name='close-circle' size={20} color='#999' />
            </TouchableOpacity>
          ) : null}
        </View>
      </Animated.View>

      {/* Sort Options */}
      {showSortOptions && (
        <View style={styles.sortOptionsContainer}>
          <TouchableOpacity
            style={styles.sortOption}
            onPress={() => handleSort('date')}
          >
            <Ionicons
              name='time-outline'
              size={20}
              color={sortBy === 'date' ? Colors.buttonPrimary : '#666'}
            />
            <Text
              style={[
                styles.sortOptionText,
                sortBy === 'date' && {
                  color: Colors.buttonPrimary,
                  fontWeight: '600',
                },
              ]}
            >
              Ngày tạo
            </Text>
            {sortBy === 'date' && (
              <Ionicons
                name='checkmark'
                size={20}
                color={Colors.buttonPrimary}
              />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.sortOption}
            onPress={() => handleSort('name')}
          >
            <Ionicons
              name='text-outline'
              size={20}
              color={sortBy === 'name' ? Colors.buttonPrimary : '#666'}
            />
            <Text
              style={[
                styles.sortOptionText,
                sortBy === 'name' && {
                  color: Colors.buttonPrimary,
                  fontWeight: '600',
                },
              ]}
            >
              Tên A-Z
            </Text>
            {sortBy === 'name' && (
              <Ionicons
                name='checkmark'
                size={20}
                color={Colors.buttonPrimary}
              />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.sortOption}
            onPress={() => handleSort('type')}
          >
            <AntDesign
              name='appstore-o'
              size={20}
              color={sortBy === 'type' ? Colors.buttonPrimary : '#666'}
            />
            <Text
              style={[
                styles.sortOptionText,
                sortBy === 'type' && {
                  color: Colors.buttonPrimary,
                  fontWeight: '600',
                },
              ]}
            >
              Loại tài liệu
            </Text>
            {sortBy === 'type' && (
              <Ionicons
                name='checkmark'
                size={20}
                color={Colors.buttonPrimary}
              />
            )}
          </TouchableOpacity>
        </View>
      )}

      {/* Document Categories */}
      <View style={styles.categories}>
        <TouchableOpacity
          style={[styles.categoryTab, activeTab === 'all' && styles.activeTab]}
          onPress={() => setActiveTab('all')}
        >
          <Text
            style={[
              styles.categoryText,
              activeTab === 'all' && styles.activeText,
            ]}
          >
            Tất cả
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.categoryTab,
            activeTab === 'recent' && styles.activeTab,
          ]}
          onPress={() => setActiveTab('recent')}
        >
          <Text
            style={[
              styles.categoryText,
              activeTab === 'recent' && styles.activeText,
            ]}
          >
            Gần đây
          </Text>
        </TouchableOpacity>
        {/* <TouchableOpacity
          style={[
            styles.categoryTab,
            activeTab === 'favorite' && styles.activeTab,
          ]}
          onPress={() => setActiveTab('favorite')}
        >
          <Text
            style={[
              styles.categoryText,
              activeTab === 'favorite' && styles.activeText,
            ]}
          >
            Yêu thích
          </Text>
        </TouchableOpacity> */}
        {/* <TouchableOpacity
          style={[
            styles.categoryTab,
            activeTab === 'shared' && styles.activeTab,
          ]}
          onPress={() => setActiveTab('shared')}
        >
          <Text
            style={[
              styles.categoryText,
              activeTab === 'shared' && styles.activeText,
            ]}
          >
            Đã chia sẻ
          </Text>
        </TouchableOpacity> */}
      </View>

      {/* Document List */}
      <View style={styles.documentListContainer}>
        {activeTab === 'all' ? (
          <DocumentList />
        ) : (
          <FlatList
            data={filteredFiles}
            keyExtractor={item => item.id}
            renderItem={renderScanFile}
            numColumns={2}
            columnWrapperStyle={styles.row}
            contentContainerStyle={
              filteredFiles.length === 0
                ? styles.emptyStateContainer
                : styles.gridContainer
            }
            ListEmptyComponent={renderEmptyState}
            showsVerticalScrollIndicator={false}
            ItemSeparatorComponent={() => <View style={styles.gridSeparator} />}
          />
        )}
      </View>

      {/* Action Sheet for file actions */}
      <ActionSheet
        title='Tùy chọn tài liệu'
        message={selectedFile?.name || ''}
        cancelButtonIndex={3}
        destructiveButtonIndex={2}
        useNativeIOS={true}
        options={[
          {
            label: 'Gửi đi',
            onPress: handleSend,
          },
          {
            label: 'Chia sẻ',
            onPress: handleShare,
          },
          {
            label: 'Xóa',
            onPress: handleDelete,
            destructive: true,
          },
          {
            label: 'Hủy',
          },
        ]}
        visible={showActionSheet}
        onDismiss={() => setShowActionSheet(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f2f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  searchBarContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f2f5',
    borderRadius: 10,
    paddingHorizontal: 10,
    height: 40,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 15,
    color: '#333',
  },
  sortOptionsContainer: {
    backgroundColor: '#fff',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  sortOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f5f5f5',
  },
  sortOptionText: {
    fontSize: 15,
    color: '#555',
    marginLeft: 10,
    flex: 1,
  },
  categories: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    backgroundColor: '#fff',
  },
  categoryTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#f0f2f5',
  },
  activeTab: {
    backgroundColor: Colors.buttonPrimary,
  },
  categoryText: {
    fontSize: 14,
    color: '#555',
  },
  activeText: {
    color: '#fff',
    fontWeight: '600',
  },
  documentListContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  scanFileCard: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginHorizontal: 4,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    overflow: 'hidden',
  },
  scanFileThumbnail: {
    width: '100%',
    height: 160,
    borderRadius: 12,
  },
  scanFileOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: 8,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  scanFileInfo: {
    alignItems: 'center',
  },
  scanFileDate: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  scanFileTime: {
    fontSize: 11,
    color: '#fff',
    opacity: 0.8,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#999',
    marginTop: 16,
    textAlign: 'center',
  },
  gridContainer: {
    padding: 16,
  },
  row: {
    justifyContent: 'space-between',
  },
  gridSeparator: {
    height: 16,
  },
  actionIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    padding: 4,
  },
});
