import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
    documentTypeService,
    DocumentTypeResponse,
    DocumentType,
    DocumentTypeListParams
} from '../services/documentTypeService';
import { documentKeys } from '@core/api/keys/documentKeys';

/**
 * Hook để lấy danh sách loại tài liệu
 */
export const useDocumentTypes = (params?: DocumentTypeListParams) => {
    return useQuery<DocumentTypeResponse>({
        queryKey: documentKeys.types(),
        queryFn: () => documentTypeService.getList(params),
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 30 * 60 * 1000, // 30 minutes
        retry: 3, // Retry 3 times on failure
        refetchOnWindowFocus: false, // Don't refetch when window regains focus
    });
};

/**
 * Hook để lấy chi tiết một loại tài liệu theo ID
 */
export const useDocumentType = (id: string) => {
    const queryClient = useQueryClient();

    return useQuery<DocumentType | null>({
        queryKey: documentKeys.type(id),
        queryFn: () => documentTypeService.getById(id),
        staleTime: 10 * 60 * 1000, // 10 minutes
        gcTime: 60 * 60 * 1000, // 60 minutes
        initialData: () => {
            // Attempt to get document type from cache
            const documentsData = queryClient.getQueryData<DocumentTypeResponse>(documentKeys.types());
            if (documentsData?.Items) {
                const cachedType = documentTypeService.findById(id, documentsData.Items);
                return cachedType || null;
            }
            return null;
        }
    });
};   