import React from 'react';
import { Text as R<PERSON>IText } from 'react-native-ui-lib';
import type { TextProps as RNUITextProps } from 'react-native-ui-lib';

export interface TextProps extends Omit<RNUITextProps, 'style'> {
  variant?:
    | 'h1'
    | 'h2'
    | 'h3'
    | 'h4'
    | 'h5'
    | 'h6'
    | 'body'
    | 'bodySmall'
    | 'button'
    | 'caption'
    | 'overline'
    | 'input'
    | 'helper';
  color?: string;
  align?: 'auto' | 'left' | 'right' | 'center' | 'justify';
  style?: RNUITextProps['style'];
}

export const Text: React.FC<TextProps> = ({
  variant = 'body',
  color,
  align = 'left',
  style,
  ...props
}) => {
  return (
    <RNUIText
      text70={variant === 'body'}
      text65={variant === 'bodySmall'}
      text60={variant === 'h1'}
      text50={variant === 'h2'}
      text40={variant === 'h3'}
      text30={variant === 'h4'}
      text20={variant === 'h5'}
      text10={variant === 'h6'}
      color={color}
      textAlign={align}
      style={style}
      {...props}
    />
  );
};
