# Thư viện Animations

Thư viện animation cho App Scan giúp tạo ra các hiệu ứng chuyển động đẹp mắt, mượt mà và dễ sử dụng.

## Nội dung

- [Cài đặt](#cài-đặt)
- [Các animation hooks](#các-animation-hooks)
- [Các animation components](#các-animation-components)
- [Ví dụ sử dụng](#ví-dụ-sử-dụng)

## Cài đặt

Thư viện animation đã được cài đặt sẵn trong dự án và không cần thêm dependencies.

## Các animation hooks

### Hooks cơ bản

```typescript
import { 
  useFadeIn, 
  useScale, 
  useSlideY, 
  useSlideX, 
  useAppearAnimation,
  usePressAnimation,
  useSkeletonAnimation,
  useStaggeredAnimation 
} from '@core/animations';
```

| Hook | Mô tả |
|------|-------|
| `useFadeIn(duration, delay, initialValue)` | Tạo hiệu ứng opacity từ 0 đến 1 |
| `useScale(duration, delay, initialValue)` | Tạo hiệu ứng tỷ lệ từ 0.9 đến 1 |
| `useSlideY(duration, delay, fromValue)` | Tạo hiệu ứng trượt dọc |
| `useSlideX(duration, delay, fromValue)` | Tạo hiệu ứng trượt ngang |
| `useAppearAnimation(duration, delay, slideValue)` | Kết hợp fade + scale + slide |
| `usePressAnimation()` | Tạo hiệu ứng khi nhấn (scale) |
| `useSkeletonAnimation(duration)` | Tạo hiệu ứng loading skeleton |
| `useStaggeredAnimation(itemCount, delay, duration)` | Tạo hiệu ứng xuất hiện lần lượt |

### Sử dụng hooks

```jsx
const MyComponent = () => {
  // Sử dụng fade in animation
  const opacity = useFadeIn(500, 0, 0);
  
  // Sử dụng scale animation
  const scale = useScale(500, 0, 0.9);
  
  // Sử dụng slide animation (dọc)
  const translateY = useSlideY(500, 0, 50);
  
  // Sử dụng combined animation (opacity + scale + slide)
  const { style: animStyle } = useAppearAnimation(600, 0, 30);
  
  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity,
          transform: [
            { scale },
            { translateY }
          ]
        }
      ]}
    >
      <Text>Animated Content</Text>
    </Animated.View>
  );
};
```

## Các animation components

### Components cơ bản

```typescript
import { 
  FadeInView, 
  ScaleView, 
  SlideInView, 
  AnimatedView,
  AnimatedButton,
  SkeletonView,
  StaggeredList,
  AnimatedText,
  AnimatedImage,
  RevealView
} from '@core/animations/components';
```

| Component | Mô tả |
|-----------|-------|
| `<FadeInView>` | Component với hiệu ứng fade in |
| `<ScaleView>` | Component với hiệu ứng scale |
| `<SlideInView>` | Component với hiệu ứng slide |
| `<AnimatedView>` | Component với hiệu ứng kết hợp fade + scale + slide |
| `<AnimatedButton>` | Button với hiệu ứng press |
| `<SkeletonView>` | Component tạo skeleton loading |
| `<StaggeredList>` | Component tạo các items xuất hiện lần lượt |
| `<AnimatedText>` | Text với hiệu ứng fade in |
| `<AnimatedImage>` | Image với hiệu ứng fade in |
| `<RevealView>` | Component với hiệu ứng reveal (width từ 0 đến 100%) |

### Sử dụng components

```jsx
import { 
  AnimatedView, 
  AnimatedButton, 
  StaggeredList 
} from '@core/animations/components';

const MyScreen = () => {
  return (
    <View style={styles.container}>
      {/* Animated View với hiệu ứng fade + scale + slide */}
      <AnimatedView style={styles.card} duration={600} delay={300}>
        <Text>Animated Card</Text>
      </AnimatedView>
      
      {/* Button với hiệu ứng press animation */}
      <AnimatedButton 
        style={styles.button} 
        onPress={() => console.log('Pressed!')}
      >
        <Text>Animated Button</Text>
      </AnimatedButton>
      
      {/* Danh sách với hiệu ứng xuất hiện lần lượt */}
      <StaggeredList 
        staggerDelay={50} 
        duration={400} 
        initialDelay={100}
      >
        {items.map(item => (
          <View key={item.id} style={styles.item}>
            <Text>{item.name}</Text>
          </View>
        ))}
      </StaggeredList>
    </View>
  );
};
```

## Ví dụ sử dụng

### 1. Hiệu ứng hiện lần lượt cho danh sách

```jsx
import { StaggeredList } from '@core/animations/components';

const ProjectList = ({ projects }) => {
  return (
    <StaggeredList staggerDelay={50}>
      {projects.map(project => (
        <View key={project.id} style={styles.projectItem}>
          <Text>{project.name}</Text>
        </View>
      ))}
    </StaggeredList>
  );
};
```

### 2. Hiệu ứng loading skeleton

```jsx
import { SkeletonView } from '@core/animations/components';

const LoadingScreen = () => {
  return (
    <View style={styles.container}>
      {Array(5).fill(0).map((_, index) => (
        <SkeletonView key={index} style={styles.skeletonItem} />
      ))}
    </View>
  );
};
```

### 3. Button với hiệu ứng press

```jsx
import { AnimatedButton } from '@core/animations/components';

const MyButton = ({ onPress, title }) => {
  return (
    <AnimatedButton 
      style={styles.button} 
      onPress={onPress}
    >
      <Text style={styles.buttonText}>{title}</Text>
    </AnimatedButton>
  );
};
```

### 4. Hiệu ứng header khi scroll

```jsx
import { useScrollAnimation } from '@core/animations';
import { Animated } from 'react-native';

const ScrollHeader = () => {
  const scrollY = useRef(new Animated.Value(0)).current;
  
  const headerHeight = useScrollAnimation(
    scrollY,
    [0, 100],
    [200, 80]
  );
  
  const headerOpacity = useScrollAnimation(
    scrollY,
    [0, 80, 120], 
    [1, 0.5, 0]
  );
  
  return (
    <>
      <Animated.View 
        style={[
          styles.header,
          { 
            height: headerHeight,
            opacity: headerOpacity 
          }
        ]}
      >
        <Text style={styles.headerTitle}>Header Title</Text>
      </Animated.View>
      
      <Animated.ScrollView
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false }
        )}
        scrollEventThrottle={16}
      >
        {/* Content */}
      </Animated.ScrollView>
    </>
  );
};
```

## Các màn hình đã áp dụng animations

Thư viện animation đã được ứng dụng cho các màn hình chính của ứng dụng:

### 1. Màn hình chọn dự án (Project Selection)
- Hiệu ứng fade in và scale cho header
- Hiệu ứng slide cho search box
- Hiệu ứng staggered (xuất hiện lần lượt) cho danh sách dự án
- Hiệu ứng skeleton loading khi đang tải
- Hiệu ứng header co giãn khi scroll

### 2. Màn hình chọn loại tài liệu (Document Type Selection)
- Thiết kế tương tự màn hình chọn dự án
- Hiệu ứng fade in + scale cho mỗi loại tài liệu
- Hiệu ứng header linh hoạt khi scroll

### 3. Màn hình Dashboard (Home)
- Hiệu ứng fade in và scale cho header
- Hiệu ứng slide lên và fade in cho card thống kê
- Hiệu ứng staggered và fade in cho tab content
- Hiệu ứng fade in cho các thành phần thống kê

### 4. Màn hình chi tiết tài liệu (Document Detail)
- Hiệu ứng fade in cho header
- Hiệu ứng header thay đổi khi scroll
- Hiệu ứng staggered cho danh sách các trang tài liệu
- Hiệu ứng scale khi xem trang tài liệu

### 5. Danh sách tài liệu (Document List)
- Hiệu ứng staggered xuất hiện cho danh sách tài liệu
- Hiệu ứng animação cho thao tác swipe
- Hiệu ứng scale cho nút action
- Hiệu ứng fade in cho empty state

## Hướng dẫn áp dụng cho màn hình mới

Để áp dụng animations cho một màn hình mới, bạn có thể làm theo các bước sau:

1. Import các hooks animation cần thiết:
```jsx
import { 
  useFadeIn, 
  useScale, 
  useSlideY, 
  useAppearAnimation 
} from '@core/animations';
```

2. Import các components animation:
```jsx
import { 
  AnimatedView, 
  FadeInView, 
  ScaleView 
} from '@core/animations/components';
```

3. Khởi tạo các animations trong component:
```jsx
const opacity = useFadeIn(500);
const scale = useScale(500, 0, 0.95);
const slideY = useSlideY(500, 200, 30);
```

4. Áp dụng animations cho các phần tử UI:
```jsx
<Animated.View 
  style={[
    styles.container,
    { 
      opacity: opacity,
      transform: [
        { scale: scale },
        { translateY: slideY }
      ]
    }
  ]}
>
  {/* Content */}
</Animated.View>
```

5. Hoặc sử dụng animation components nhanh chóng:
```jsx
<FadeInView delay={200} duration={500}>
  <Text>Content với fade in animation</Text>
</FadeInView>
```

## Tối ưu hiệu năng

Khi áp dụng animations, hãy lưu ý:

1. Sử dụng `useNativeDriver: true` khi có thể cho hiệu năng tốt hơn
2. Tránh animate quá nhiều thành phần cùng lúc
3. Tránh animations phức tạp cho những thiết bị cấu hình thấp
4. Trong trường hợp list dài, sử dụng memo/useMemo để tránh re-render không cần thiết

## Liên hệ

Nếu có bất kỳ câu hỏi hoặc đề xuất nào về thư viện animation, vui lòng liên hệ team phát triển. 