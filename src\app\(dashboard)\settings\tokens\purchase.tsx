import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Ionicons,
  FontAwesome5,
  MaterialCommunityIcons,
} from '@expo/vector-icons';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { AnimatedView } from '@/shared/animations/components';
import { useTokens } from '@features/tokens/tokenContext';
import { ROUTES } from '@/core/constants/routes';
import Header from '@/shared/components/molecules/Header';

type IconName = 'arrow-back' | 'checkmark-circle';

// Phương thức thanh toán hỗ trợ
const PAYMENT_METHODS = [
  {
    id: 'momo',
    name: 'V<PERSON> MoMo',
    color: '#A50064',
  },
  {
    id: 'zalopay',
    name: '<PERSON><PERSON> Zalo<PERSON>ay',
    color: '#0068FF',
  },
  {
    id: 'bank',
    name: '<PERSON><PERSON><PERSON><PERSON> khoản ngân hàng',
    color: '#005BAA',
  },
  {
    id: 'visa',
    name: 'Thẻ Visa/Mastercard',
    color: '#1A1F71',
  },
];

const TokenPurchaseScreen = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { tokenPackages, addTokens } = useTokens();

  // Lấy packageId từ params hoặc mặc định là package đầu tiên
  const packageId = (params.packageId as string) || tokenPackages[0]?.id;

  // Tìm package được chọn
  const selectedPackage =
    tokenPackages.find(pkg => pkg.id === packageId) || tokenPackages[0];

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('momo');
  const [isProcessing, setIsProcessing] = useState(false);

  const handlePurchase = () => {
    // Hiển thị loading
    setIsProcessing(true);

    // Giả lập quá trình thanh toán
    setTimeout(() => {
      setIsProcessing(false);

      // Thêm token vào tài khoản người dùng
      const paymentMethod =
        PAYMENT_METHODS.find(method => method.id === selectedPaymentMethod)
          ?.name || 'Ví điện tử';
      addTokens(selectedPackage.amount, paymentMethod);

      // Hiển thị thông báo thành công
      Alert.alert(
        'Thanh toán thành công',
        `Bạn đã mua thành công ${selectedPackage.amount} token.`,
        [
          {
            text: 'Xem lịch sử',
            onPress: () => router.push(ROUTES.TOKENS_HISTORY),
          },
          {
            text: 'OK',
            onPress: () => router.push(ROUTES.TOKENS),
          },
        ],
      );
    }, 2000);
  };

  // Định dạng giá tiền
  const formatPrice = (price: string) => {
    return price.replace(/\.\d+/, ''); // Bỏ phần thập phân nếu có
  };

  // Get payment method icon
  const getPaymentIcon = (methodId: string) => {
    switch (methodId) {
      case 'momo':
        return <FontAwesome5 name='wallet' size={24} color='#A50064' />;
      case 'zalopay':
        return <FontAwesome5 name='wallet' size={24} color='#0068FF' />;
      case 'bank':
        return <FontAwesome5 name='university' size={24} color='#005BAA' />;
      case 'visa':
        return <FontAwesome5 name='credit-card' size={24} color='#1A1F71' />;
      default:
        return <FontAwesome5 name='money-bill-alt' size={24} color='#4CAF50' />;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header title='Mua token' />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Package Information */}
        <AnimatedView style={styles.packageCard} duration={600}>
          <View style={styles.packageIconContainer}>
            <FontAwesome5 name='coins' size={32} color='#FBBC05' />
          </View>

          <Text style={styles.packageTitle}>
            {selectedPackage.amount} Token
          </Text>
          <Text style={styles.packagePrice}>
            {formatPrice(selectedPackage.price)}
          </Text>

          {selectedPackage.discount !== '0%' && (
            <View style={styles.discountBadge}>
              <Text style={styles.discountText}>
                Tiết kiệm {selectedPackage.discount}
              </Text>
            </View>
          )}

          <Text style={styles.packageDescription}>
            Gói token này có thể sử dụng cho tất cả các tính năng trong ứng
            dụng: quét tài liệu, nhận dạng văn bản (OCR) và lưu trữ đám mây.
          </Text>
        </AnimatedView>

        {/* Payment Methods */}
        <AnimatedView
          style={styles.paymentMethodsCard}
          duration={600}
          delay={200}
        >
          <Text style={styles.sectionTitle}>Phương thức thanh toán</Text>

          {PAYMENT_METHODS.map(method => (
            <TouchableOpacity
              key={method.id}
              style={[
                styles.paymentMethodItem,
                selectedPaymentMethod === method.id &&
                  styles.paymentMethodItemSelected,
              ]}
              onPress={() => setSelectedPaymentMethod(method.id)}
              disabled={isProcessing}
            >
              <View style={styles.paymentMethodLeft}>
                <View
                  style={[
                    styles.iconContainer,
                    { backgroundColor: `${method.color}20` },
                  ]}
                >
                  {getPaymentIcon(method.id)}
                </View>
                <Text style={styles.paymentMethodName}>{method.name}</Text>
              </View>

              {selectedPaymentMethod === method.id ? (
                <Ionicons
                  name={'checkmark-circle' as IconName}
                  size={24}
                  color={method.color}
                />
              ) : (
                <View style={styles.radioOuter}>
                  <View style={styles.radioInner} />
                </View>
              )}
            </TouchableOpacity>
          ))}
        </AnimatedView>

        {/* Order Summary */}
        <AnimatedView style={styles.summaryCard} duration={600} delay={400}>
          <Text style={styles.sectionTitle}>Thông tin đơn hàng</Text>

          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Sản phẩm</Text>
            <Text style={styles.summaryValue}>
              {selectedPackage.amount} Token
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Đơn giá</Text>
            <Text style={styles.summaryValue}>
              {formatPrice(selectedPackage.price)}
            </Text>
          </View>

          {selectedPackage.discount !== '0%' && (
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Giảm giá</Text>
              <Text style={[styles.summaryValue, styles.discountValue]}>
                -{selectedPackage.discount}
              </Text>
            </View>
          )}

          <View style={[styles.summaryRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Tổng cộng</Text>
            <Text style={styles.totalValue}>
              {formatPrice(selectedPackage.price)}
            </Text>
          </View>
        </AnimatedView>
      </ScrollView>

      {/* Bottom Action Bar */}
      <View style={styles.bottomBar}>
        <View style={styles.priceContainer}>
          <Text style={styles.priceLabel}>Tổng thanh toán:</Text>
          <Text style={styles.priceValue}>
            {formatPrice(selectedPackage.price)}
          </Text>
        </View>

        <TouchableOpacity
          style={[
            styles.purchaseButton,
            isProcessing && styles.purchaseButtonDisabled,
          ]}
          onPress={handlePurchase}
          disabled={isProcessing}
        >
          {isProcessing ? (
            <View style={styles.processingContainer}>
              <MaterialCommunityIcons name='loading' size={20} color='#fff' />
              <Text style={styles.purchaseButtonText}>Đang xử lý...</Text>
            </View>
          ) : (
            <Text style={styles.purchaseButtonText}>Xác nhận & Thanh toán</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f6fa',
  },
  placeholderView: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100, // Space for bottom bar
  },
  packageCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  packageIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(251, 188, 5, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  packageTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#333',
    marginBottom: 8,
  },
  packagePrice: {
    fontSize: 28,
    fontWeight: '700',
    color: '#2F4FCD',
    marginBottom: 12,
  },
  discountBadge: {
    backgroundColor: '#E3F2FD',
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 16,
    marginBottom: 16,
  },
  discountText: {
    fontSize: 14,
    color: '#1976D2',
    fontWeight: '500',
  },
  packageDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  paymentMethodsCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  paymentMethodItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    marginBottom: 12,
    backgroundColor: '#fff',
  },
  paymentMethodItemSelected: {
    borderColor: '#2F4FCD',
    backgroundColor: '#F5F8FF',
  },
  paymentMethodLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  paymentMethodName: {
    fontSize: 15,
    color: '#333',
    fontWeight: '500',
  },
  radioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#999',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: 'transparent',
  },
  summaryCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  discountValue: {
    color: '#4CAF50',
  },
  totalRow: {
    marginTop: 8,
    borderBottomWidth: 0,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#2F4FCD',
  },
  bottomBar: {
    position: 'absolute',
    bottom: 10,
    left: 5,
    right: 5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  priceContainer: {
    flex: 1,
  },
  priceLabel: {
    fontSize: 12,
    color: '#666',
  },
  priceValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#2F4FCD',
  },
  purchaseButton: {
    backgroundColor: '#2F4FCD',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  purchaseButtonDisabled: {
    backgroundColor: '#A5B4FC',
  },
  purchaseButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  processingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default TokenPurchaseScreen;
