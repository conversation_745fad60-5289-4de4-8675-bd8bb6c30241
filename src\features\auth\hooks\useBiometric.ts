import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import biometricAuthService, { BiometricType } from '@core/services/biometricAuthService';

const BIOMETRIC_ENABLED_KEY = '@auth:biometric_enabled';

export interface UseBiometricReturnType {
    isBiometricAvailable: boolean;
    isBiometricEnabled: boolean;
    biometricType: BiometricType;
    isLoading: boolean;
    authenticate: (promptMessage: string) => Promise<boolean>;
    toggleBiometric: () => Promise<void>;
}

export const useBiometric = (): UseBiometricReturnType => {
    const [isBiometricAvailable, setIsBiometricAvailable] = useState<boolean>(false);
    const [isBiometricEnabled, setIsBiometricEnabled] = useState<boolean>(false);
    const [biometricType, setBiometricType] = useState<BiometricType>('none');
    const [isLoading, setIsLoading] = useState<boolean>(true);

    // Kiểm tra thiết bị và đọc trạng thái từ storage khi component mount
    useEffect(() => {
        const checkBiometric = async () => {
            try {
                // Kiểm tra thiết bị có hỗ trợ không
                const isAvailable = await biometricAuthService.isBiometricAvailable();
                setIsBiometricAvailable(isAvailable);

                if (isAvailable) {
                    // Kiểm tra người dùng đã đăng ký dữ liệu sinh trắc học chưa
                    const isEnrolled = await biometricAuthService.isEnrolled();

                    if (isEnrolled) {
                        // Lấy loại sinh trắc học được hỗ trợ
                        const type = await biometricAuthService.getBiometricType();
                        setBiometricType(type);

                        // Đọc trạng thái từ storage
                        const enabledString = await AsyncStorage.getItem(BIOMETRIC_ENABLED_KEY);
                        setIsBiometricEnabled(enabledString === 'true');
                    }
                }
            } catch (error) {
                console.error('Error checking biometric:', error);
            } finally {
                setIsLoading(false);
            }
        };

        checkBiometric();
    }, []);

    // Xác thực sinh trắc học
    const authenticate = useCallback(async (promptMessage: string): Promise<boolean> => {
        try {
            // Nếu không hỗ trợ hoặc không bật thì không làm gì
            if (!isBiometricAvailable || !isBiometricEnabled) {
                return false;
            }

            // Thực hiện xác thực
            const success = await biometricAuthService.authenticate({
                promptMessage,
            });

            return success;
        } catch (error) {
            console.error('Authentication error:', error);
            return false;
        }
    }, [isBiometricAvailable, isBiometricEnabled]);

    // Bật/tắt xác thực sinh trắc học
    const toggleBiometric = useCallback(async (): Promise<void> => {
        try {
            const newState = !isBiometricEnabled;
            await AsyncStorage.setItem(BIOMETRIC_ENABLED_KEY, newState.toString());
            setIsBiometricEnabled(newState);
        } catch (error) {
            console.error('Error toggling biometric:', error);
        }
    }, [isBiometricEnabled]);

    return {
        isBiometricAvailable,
        isBiometricEnabled,
        biometricType,
        isLoading,
        authenticate,
        toggleBiometric,
    };
};

export default useBiometric; 