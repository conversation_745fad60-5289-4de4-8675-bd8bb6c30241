import React, { useState } from 'react';
import {
  FlatList,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  View,
  Text,
} from 'react-native';
import { View as UILibView, Image, ActionSheet } from 'react-native-ui-lib';
import { Swipeable } from 'react-native-gesture-handler';
import { Ionicons, Entypo } from '@expo/vector-icons';
import LottieView from 'lottie-react-native';
import Share from 'react-native-share';
import Dialog from 'react-native-dialog';
import { useRouter } from 'expo-router';
import { useScan } from '@features/scan/context/ScanContext';
import { ROUTES, getRoute } from '@core/constants/routes';

export default function DocumentList() {
  const { scanSessions, deleteScanSession, updateScanSessionTitle, isLoading } =
    useScan();
  const router = useRouter();
  const [editingItem, setEditingItem] = useState<
    null | (typeof scanSessions)[0]
  >(null);
  const [newTitle, setNewTitle] = useState('');
  const [showActionSheetId, setShowActionSheetId] = useState<string | null>(
    null,
  );

  const handleShare = async (item: (typeof scanSessions)[0]) => {
    if (!item.images.length) return;
    try {
      await Share.open({
        urls: item.images,
        failOnCancel: false,
      });
    } catch (err) {
      console.error('Lỗi chia sẻ:', err);
    }
  };

  const handleDelete = (item: (typeof scanSessions)[0]) => {
    deleteScanSession(item.id);
  };

  const renderItem = ({ item }: { item: (typeof scanSessions)[0] }) => {
    const rightActions = () => (
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => handleDelete(item)}
      >
        <Ionicons name='trash-outline' size={24} color='#fff' />
        <Text style={styles.deleteText}>Xóa</Text>
      </TouchableOpacity>
    );

    return (
      <>
        <Swipeable renderRightActions={rightActions}>
          <TouchableOpacity
            activeOpacity={0.9}
            style={styles.cardWrapperList}
            onPress={() =>
              router.push({
                pathname: '/scan/[id]',
                params: { id: item.id },
              })
            }
          >
            <UILibView style={styles.cardList}>
              <Image
                source={{ uri: item.images[0] }}
                style={styles.thumbnailList}
              />
              <UILibView style={styles.infoList}>
                <Text style={styles.title}>{item.title}</Text>
                {/* <UILibView row centerV>
                  <Text>Phần mềm: </Text>
                  <Text style={styles.appName}>
                    {item.siteName || 'Chưa có'}
                  </Text>
                </UILibView> */}
                <UILibView row centerV>
                  <Text>Ngày: </Text>
                  <Text style={styles.date}>
                    {new Date(item.createdAt).toLocaleString()}
                  </Text>
                </UILibView>
              </UILibView>

              {/* Nút 3 chấm */}
              <TouchableOpacity
                style={styles.moreButton}
                onPress={() => setShowActionSheetId(item.id)}
              >
                <Entypo name='dots-three-vertical' size={18} color='#777' />
              </TouchableOpacity>
            </UILibView>
          </TouchableOpacity>
        </Swipeable>

        {/* ActionSheet cho từng item */}
        <ActionSheet
          title={'Tùy chọn'}
          message={item.title}
          cancelButtonIndex={2}
          destructiveButtonIndex={1}
          useNativeIOS={true}
          options={[
            {
              label: 'Đổi tên',
              onPress: () => {
                setEditingItem(item);
                setNewTitle(item.title);
              },
            },
            {
              label: 'Xóa',
              onPress: () => handleDelete(item),
              destructive: true,
            },
            {
              label: 'Đóng',
            },
          ]}
          visible={showActionSheetId === item.id}
          onDismiss={() => setShowActionSheetId(null)}
        />
      </>
    );
  };

  // Loading state
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size='large' color='#4361ee' />
        <Text style={styles.loadingText}>Đang tải dữ liệu...</Text>
      </View>
    );
  }

  // Empty state
  if (scanSessions.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <LottieView
          autoPlay
          style={{ width: 150, height: 150 }}
          source={require('@assets/lottie/document_2.json')}
        />
        <Text style={styles.emptyText}>Chưa có phiên quét nào</Text>
        <Text style={styles.emptySubText}>
          Bắt đầu quét tài liệu để tạo phiên mới
        </Text>
      </View>
    );
  }

  return (
    <>
      <FlatList
        data={scanSessions}
        keyExtractor={item => item.id}
        renderItem={renderItem}
        contentContainerStyle={{ padding: 0 }}
      />
      <Dialog.Container useNativeDriver visible={!!editingItem}>
        <Dialog.Title> Đổi tên</Dialog.Title>
        <Dialog.Input
          value={newTitle}
          onChangeText={setNewTitle}
          placeholder='Tên tài liệu mới'
        />
        <Dialog.Button label='Hủy bỏ' onPress={() => setEditingItem(null)} />
        <Dialog.Button
          label='Lưu'
          onPress={() => {
            if (editingItem) {
              updateScanSessionTitle(
                editingItem.id,
                newTitle.trim() || editingItem.title,
              );
              setEditingItem(null);
            }
          }}
        />
      </Dialog.Container>
    </>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
  },
  emptySubText: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
  cardWrapperList: {
    flex: 1,
    marginBottom: 12,
    borderRadius: 12,
    backgroundColor: '#f9f9f9',
    overflow: 'hidden',
  },
  cardList: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    position: 'relative',
  },
  thumbnailList: {
    width: 60,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
  },
  infoList: {
    flex: 1,
    gap: 2,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  appName: {
    fontSize: 14,
    color: '#666',
  },
  date: {
    fontSize: 14,
    color: '#666',
  },
  moreButton: {
    padding: 8,
    marginLeft: 8,
  },
  deleteButton: {
    backgroundColor: '#ff4757',
    justifyContent: 'center',
    alignItems: 'center',
    width: 80,
    height: '100%',
  },
  deleteText: {
    color: '#fff',
    fontSize: 12,
    marginTop: 4,
  },
});
