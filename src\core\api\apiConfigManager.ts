import { MMKV } from 'react-native-mmkv';
import { API_CONFIG } from './config';
import { documentStorage, projectStorage } from '@core/storage';

// Local storage to store/retrieve configuration values (chỉ còn giữ lại cho tương thích)



/**
 * Updates the project ID in both storage and API config
 */
export const updateProjectId = (projectId: string): void => {
  if (!projectId) return;

  // Lưu trữ qua projectStorage
  projectStorage.setDefaultProject(projectId);

  // Update API config
  API_CONFIG.IMAGE_UPLOAD.PARAMS.duAnID = projectId;

  console.log('[API Config] Updated project ID:', projectId);
};

/**
 * Updates the document type in both storage and API config
 */
export const updateDocumentType = (documentType: string): void => {
  if (!documentType) return;

  // Lưu trữ qua documentStorage
  documentStorage.setDefaultDocumentType(documentType);

  // Update API config
  API_CONFIG.IMAGE_UPLOAD.PARAMS.loaiVanBan = documentType;

  console.log('[API Config] Updated document type:', documentType);
};

/**
 * Lấy projectId hiện tại từ storage
 */
export const getCurrentProjectId = (): string | null => {
  return projectStorage.getDefaultProject() || null;
};

/**
 * Lấy loại văn bản hiện tại từ storage
 */
export const getCurrentDocumentType = (): string | null => {
  return documentStorage.getDefaultDocumentType() || null;
};