const baseUnit = 4;

export const borderRadius = {
    // Base border radius values
    none: 0,
    xs: baseUnit / 2, // 2
    sm: baseUnit, // 4
    md: baseUnit * 1.5, // 6
    lg: baseUnit * 2, // 8
    xl: baseUnit * 3, // 12
    xxl: baseUnit * 4, // 16
    round: 9999,

    // Component specific border radius
    button: {
        sm: baseUnit / 2, // 2
        md: baseUnit, // 4
        lg: baseUnit * 1.5, // 6
    },
    card: {
        sm: baseUnit, // 4
        md: baseUnit * 1.5, // 6
        lg: baseUnit * 2, // 8
    },
    input: {
        sm: baseUnit / 2, // 2
        md: baseUnit, // 4
        lg: baseUnit * 1.5, // 6
    },
    avatar: {
        sm: baseUnit * 2, // 8
        md: baseUnit * 3, // 12
        lg: baseUnit * 4, // 16
    },
} as const;

export type BorderRadius = typeof borderRadius; 