import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Platform,
  Dimensions,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useQRScannerDimensions } from '@/shared/hooks/useResponsiveDimensions';

/**
 * Test component để kiểm tra layout của QR Scanner trên Android
 * Hiển thị các thông số và vị trí để debug
 */
const QRScannerTestLayout: React.FC = () => {
  const responsive = useQRScannerDimensions();
  const { width, height } = Dimensions.get('window');

  const debugInfo = {
    platform: Platform.OS,
    screenWidth: width,
    screenHeight: height,
    scanFrameSize: responsive.scanFrameSize,
    scanAreaOffsetY: responsive.scanAreaOffsetY,
    topOverlayHeight: Platform.OS === 'android'
      ? (height - responsive.scanFrameSize) / 2
      : (height - responsive.scanFrameSize) / 2 + responsive.scanAreaOffsetY,
    bottomOverlayHeight: Platform.OS === 'android'
      ? (height - responsive.scanFrameSize) / 2
      : (height - responsive.scanFrameSize) / 2 - responsive.scanAreaOffsetY,
    middleRowTop: Platform.OS === 'android'
      ? (height - responsive.scanFrameSize) / 2
      : (height - responsive.scanFrameSize) / 2 + responsive.scanAreaOffsetY,
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar 
        barStyle="light-content" 
        backgroundColor="transparent" 
        translucent={true}
      />
      
      {/* Debug overlay */}
      <View style={styles.debugOverlay}>
        <Text style={styles.debugTitle}>QR Scanner Layout Debug</Text>
        {Object.entries(debugInfo).map(([key, value]) => (
          <Text key={key} style={styles.debugText}>
            {key}: {typeof value === 'number' ? value.toFixed(1) : value}
          </Text>
        ))}
      </View>

      {/* Spotlight overlay simulation */}
      <View style={[styles.spotlightOverlay, { width, height }]}>
        {/* Top overlay */}
        <View style={[
          styles.topOverlay,
          {
            height: debugInfo.topOverlayHeight,
          }
        ]} />
        
        {/* Middle row */}
        <View style={[
          styles.middleRow,
          {
            width,
            height: responsive.scanFrameSize,
            top: debugInfo.middleRowTop,
          }
        ]}>
          <View style={[
            styles.leftOverlay,
            {
              width: (width - responsive.scanFrameSize) / 2,
              height: responsive.scanFrameSize,
            }
          ]} />
          
          {/* Scan hole with frame */}
          <View style={[
            styles.scanHole,
            {
              width: responsive.scanFrameSize,
              height: responsive.scanFrameSize,
            }
          ]}>
            <View style={[
              styles.scanFrame,
              {
                width: responsive.scanFrameSize,
                height: responsive.scanFrameSize,
              }
            ]}>
              <Text style={styles.scanFrameText}>SCAN AREA</Text>
              <Text style={styles.scanFrameSize}>
                {responsive.scanFrameSize.toFixed(0)}x{responsive.scanFrameSize.toFixed(0)}
              </Text>
            </View>
          </View>
          
          <View style={[
            styles.rightOverlay,
            {
              width: (width - responsive.scanFrameSize) / 2,
              height: responsive.scanFrameSize,
            }
          ]} />
        </View>
        
        {/* Bottom overlay */}
        <View style={[
          styles.bottomOverlay,
          {
            height: debugInfo.bottomOverlayHeight,
          }
        ]} />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  debugOverlay: {
    position: 'absolute',
    top: 50,
    left: 10,
    right: 10,
    backgroundColor: 'rgba(255,255,255,0.9)',
    padding: 10,
    borderRadius: 8,
    zIndex: 1000,
  },
  debugTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#000',
  },
  debugText: {
    fontSize: 12,
    color: '#333',
    marginBottom: 2,
  },
  spotlightOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  topOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255,0,0,0.3)', // Red tint for debugging
  },
  bottomOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,255,0,0.3)', // Green tint for debugging
  },
  middleRow: {
    flexDirection: 'row',
    position: 'absolute',
  },
  leftOverlay: {
    backgroundColor: 'rgba(0,0,255,0.3)', // Blue tint for debugging
  },
  rightOverlay: {
    backgroundColor: 'rgba(0,0,255,0.3)', // Blue tint for debugging
  },
  scanHole: {
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanFrame: {
    borderWidth: 2,
    borderColor: '#00FF88',
    backgroundColor: 'rgba(0,255,136,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
  },
  scanFrameText: {
    color: '#00FF88',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  scanFrameSize: {
    color: '#00FF88',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 4,
  },
});

export default QRScannerTestLayout;
