import { useState, useCallback } from 'react';
import { ConfigService, ProjectConfig, DocumentTypeConfig } from '../services/ConfigService';

export const useConfigService = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);
    const configService = new ConfigService();

    const saveProject = useCallback(async (project: ProjectConfig) => {
        setLoading(true);
        setError(null);
        try {
            await configService.saveProjectConfig(project);
            setLoading(false);
        } catch (err) {
            const error = err instanceof Error ? err : new Error('Lỗi lưu dự án');
            setError(error);
            setLoading(false);
            throw error;
        }
    }, []);

    const getProject = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const project = await configService.getProjectConfig();
            setLoading(false);
            return project;
        } catch (err) {
            const error = err instanceof Error ? err : new Error('Lỗi lấy dự án');
            setError(error);
            setLoading(false);
            throw error;
        }
    }, []);

    const saveDocumentType = useCallback(async (typeId: string, typeName?: string) => {
        setLoading(true);
        setError(null);
        try {
            await configService.saveDocumentType(typeId, typeName);
            setLoading(false);
        } catch (err) {
            const error = err instanceof Error ? err : new Error('Lỗi lưu loại tài liệu');
            setError(error);
            setLoading(false);
            throw error;
        }
    }, []);

    const getDocumentType = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const documentType = await configService.getDocumentTypeDetail();
            setLoading(false);
            return documentType;
        } catch (err) {
            const error = err instanceof Error ? err : new Error('Lỗi lấy loại tài liệu');
            setError(error);
            setLoading(false);
            throw error;
        }
    }, []);

    const getAllConfig = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const config = await configService.getAllConfig();
            setLoading(false);
            return config;
        } catch (err) {
            const error = err instanceof Error ? err : new Error('Lỗi lấy cấu hình');
            setError(error);
            setLoading(false);
            throw error;
        }
    }, []);

    return {
        loading,
        error,
        saveProject,
        getProject,
        saveDocumentType,
        getDocumentType,
        getAllConfig
    };
}; 