// Base path constants
const AUTH_BASE = '/(auth)';
const DASHBOARD_BASE = '/(dashboard)';
const APP_HUB_BASE = '/(app-hub)';

// Common path segments
const TABS = '(tabs)';
const SETTINGS = 'settings';

export const ROUTES = {
    // Auth routes
    AUTH: {
        SIGN_IN: `${AUTH_BASE}/sign-in`,
        SIGN_UP: `${AUTH_BASE}/sign-up`,
        ONBOARDING: `${AUTH_BASE}/onboarding`,
    },

    // Main routes
    HOME: `${DASHBOARD_BASE}/${TABS}/home`,
    PROFILE: `${DASHBOARD_BASE}/${TABS}/profile`,
    SCAN: `${DASHBOARD_BASE}/scan`,
    SETTINGS: `${DASHBOARD_BASE}/${SETTINGS}`,
    DOCUMENTS_HISTORY: `${DASHBOARD_BASE}/${TABS}/documents`,

    // <PERSON>an routes
    SCAN_RESULT: `${DASH<PERSON>ARD_BASE}/scan/index`,
    SCAN_DETAIL: (id: string) => `${DASHBOARD_BASE}/scan/${id}`,

    // Settings routes
    STORAGE_MANAGER: `${DASHBOARD_BASE}/${SETTINGS}/storage_manager`,
    ABOUT: `${DASHBOARD_BASE}/${SETTINGS}/about`,

    // Tokens routes
    TOKENS: `${DASHBOARD_BASE}/${SETTINGS}/tokens`,
    TOKENS_HISTORY: `${DASHBOARD_BASE}/${SETTINGS}/tokens/history`,
    TOKENS_PURCHASE: `${DASHBOARD_BASE}/${SETTINGS}/tokens/purchase`,

    // Project manager routes
    PROJECT_MANAGER: `${DASHBOARD_BASE}/${SETTINGS}/project-manager`,
    AI_MODELS: `${DASHBOARD_BASE}/${SETTINGS}/ai-models`,
    DOCUMENT_TYPE: `${DASHBOARD_BASE}/${SETTINGS}/document-type`,
    PROJECT_DOC_SETTINGS: `${DASHBOARD_BASE}/${SETTINGS}/project-document-settings`,
    NOTIFICATIONS: `${DASHBOARD_BASE}/${SETTINGS}/notifications`,
    APPEARANCE: `${DASHBOARD_BASE}/${SETTINGS}/appearance`,
    QUICK_ACTIONS: `${DASHBOARD_BASE}/${SETTINGS}/quick-actions`,
    LANGUAGE: `${DASHBOARD_BASE}/${SETTINGS}/language`,
    USER_PROFILE: `${DASHBOARD_BASE}/${SETTINGS}/user-profile`,

    // App hub routes
    APP_HUB: `${APP_HUB_BASE}/${TABS}`,
    FEATURE_SELECTION: `${APP_HUB_BASE}/feature-selection`,


} as const;

// Type for route names
export type RouteName = keyof typeof ROUTES;

// Helper function to get route with params
export const getRoute = (name: RouteName, params?: Record<string, string>) => {
    const route = ROUTES[name];
    if (typeof route === 'function' && params) {
        return route(params.id);
    }
    return route;
};

// Helper function for navigation
export const createRoute = (basePath: string, subPath?: string) => {
    return subPath ? `${basePath}/${subPath}` : basePath;
};

// Common navigation helpers
export const NavigationHelpers = {
    // Auth navigation
    goToSignIn: () => ROUTES.AUTH.SIGN_IN,
    goToSignUp: () => ROUTES.AUTH.SIGN_UP,
    goToOnboarding: () => ROUTES.AUTH.ONBOARDING,

    // Main navigation
    goToHome: () => ROUTES.HOME,
    goToProfile: () => ROUTES.PROFILE,
    goToScan: () => ROUTES.SCAN,
    goToSettings: () => ROUTES.SETTINGS,

    // Scan navigation
    goToScanDetail: (id: string) => ROUTES.SCAN_DETAIL(id),

    // Settings navigation
    goToStorageManager: () => ROUTES.STORAGE_MANAGER,
    goToAbout: () => ROUTES.ABOUT,
    goToTokens: () => ROUTES.TOKENS,
    goToTokensHistory: () => ROUTES.TOKENS_HISTORY,
    goToTokensPurchase: () => ROUTES.TOKENS_PURCHASE,
    goToProjectManager: () => ROUTES.PROJECT_MANAGER,
    goToAiModels: () => ROUTES.AI_MODELS,
    goToDocumentType: () => ROUTES.DOCUMENT_TYPE,
    goToNotifications: () => ROUTES.NOTIFICATIONS,
    goToAppearance: () => ROUTES.APPEARANCE,
    goToLanguage: () => ROUTES.LANGUAGE,
    goToUserProfile: () => ROUTES.USER_PROFILE,
} as const; 