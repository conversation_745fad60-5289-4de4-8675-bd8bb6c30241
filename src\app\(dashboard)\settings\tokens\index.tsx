import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons, FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { AnimatedView } from '@/shared/animations/components';
import { PieChart } from 'react-native-gifted-charts';
import { ROUTES } from '@/core/constants/routes';
import Header from '@/shared/components/molecules/Header';

// Mock data for token balance
const userTokens = {
  balance: 148,
  tokenPackages: [
    { id: '1', amount: 50, price: '49.000 ₫', discount: '0%' },
    { id: '2', amount: 100, price: '89.000 ₫', discount: '10%' },
    { id: '3', amount: 300, price: '249.000 ₫', discount: '15%' },
    { id: '4', amount: 1000, price: '799.000 ₫', discount: '20%' },
  ],
  tokenCost: {
    scanDocument: 2,
    ocrProcessing: 5,
    cloudStorage: 1,
  },
};

type IconName = 'arrow-back';

const TokenSettingsScreen = () => {
  const router = useRouter();

  return (
    <SafeAreaView style={styles.container}>
      <Header title='Token của tôi' />
      <ScrollView style={styles.scrollView}>
        {/* Token Balance */}
        <AnimatedView style={styles.balanceCard} duration={600}>
          <View style={styles.balanceTopRow}>
            <Text style={styles.balanceLabel}>Số token hiện có</Text>
            <TouchableOpacity
              style={styles.historyButton}
              onPress={() => router.push(ROUTES.TOKENS_HISTORY)}
            >
              <Text style={styles.historyButtonText}>Lịch sử</Text>
              <Ionicons name='chevron-forward' size={16} color='#2F4FCD' />
            </TouchableOpacity>
          </View>

          <View style={styles.balanceContent}>
            <View style={styles.balanceCircle}>
              <PieChart
                data={[
                  { value: userTokens.balance, color: '#2F4FCD' },
                  { value: 500 - userTokens.balance, color: '#E3E8FF' },
                ]}
                radius={60}
                innerRadius={48}
                innerCircleColor={'#fff'}
                centerLabelComponent={() => (
                  <Text
                    style={{ fontSize: 26, fontWeight: '700', color: '#333' }}
                  >
                    {userTokens.balance}
                  </Text>
                )}
              />
            </View>
            <View style={styles.balanceInfo}>
              <Text style={styles.balanceTitle}>
                {userTokens.balance} token
              </Text>
              <Text style={styles.balanceSubtitle}>
                Bạn có thể quét khoảng{' '}
                {Math.floor(
                  userTokens.balance / userTokens.tokenCost.scanDocument,
                )}{' '}
                tài liệu
              </Text>
              <TouchableOpacity
                style={styles.addTokenButton}
                onPress={() => router.push(ROUTES.TOKENS_PURCHASE)}
              >
                <Text style={styles.addTokenButtonText}>Mua thêm token</Text>
              </TouchableOpacity>
            </View>
          </View>
        </AnimatedView>

        {/* Token Pricing */}
        <AnimatedView style={styles.pricingCard} duration={600} delay={200}>
          <Text style={styles.sectionTitle}>Chi phí theo tính năng</Text>

          <View style={styles.pricingItem}>
            <View style={styles.pricingItemLeft}>
              <View
                style={[
                  styles.pricingIconContainer,
                  { backgroundColor: 'rgba(66, 133, 244, 0.1)' },
                ]}
              >
                <Ionicons name='scan-outline' size={20} color='#4285F4' />
              </View>
              <Text style={styles.pricingText}>Quét tài liệu</Text>
            </View>
            <View style={styles.tokenChip}>
              <FontAwesome5 name='coins' size={12} color='#2F4FCD' />
              <Text style={styles.tokenChipText}>
                {userTokens.tokenCost.scanDocument}
              </Text>
            </View>
          </View>

          <View style={styles.pricingItem}>
            <View style={styles.pricingItemLeft}>
              <View
                style={[
                  styles.pricingIconContainer,
                  { backgroundColor: 'rgba(52, 168, 83, 0.1)' },
                ]}
              >
                <Ionicons name='text-outline' size={20} color='#34A853' />
              </View>
              <Text style={styles.pricingText}>Nhận dạng văn bản (OCR)</Text>
            </View>
            <View style={styles.tokenChip}>
              <FontAwesome5 name='coins' size={12} color='#2F4FCD' />
              <Text style={styles.tokenChipText}>
                {userTokens.tokenCost.ocrProcessing}
              </Text>
            </View>
          </View>

          <View style={styles.pricingItem}>
            <View style={styles.pricingItemLeft}>
              <View
                style={[
                  styles.pricingIconContainer,
                  { backgroundColor: 'rgba(251, 188, 5, 0.1)' },
                ]}
              >
                <Ionicons
                  name='cloud-upload-outline'
                  size={20}
                  color='#FBBC05'
                />
              </View>
              <Text style={styles.pricingText}>
                Lưu trữ đám mây (mỗi tài liệu/tháng)
              </Text>
            </View>
            <View style={styles.tokenChip}>
              <FontAwesome5 name='coins' size={12} color='#2F4FCD' />
              <Text style={styles.tokenChipText}>
                {userTokens.tokenCost.cloudStorage}
              </Text>
            </View>
          </View>
        </AnimatedView>

        {/* Token Packages */}
        <AnimatedView style={styles.packagesCard} duration={600} delay={400}>
          <Text style={styles.sectionTitle}>Mua token</Text>

          {userTokens.tokenPackages.map(pkg => (
            <View key={pkg.id} style={styles.packageItem}>
              <View style={styles.packageInfo}>
                <Text style={styles.packageTokenAmount}>
                  {pkg.amount} token
                </Text>
                <Text style={styles.packagePrice}>{pkg.price}</Text>
              </View>
              {pkg.discount !== '0%' && (
                <View style={styles.discountBadge}>
                  <Text style={styles.discountText}>
                    Tiết kiệm {pkg.discount}
                  </Text>
                </View>
              )}
              <TouchableOpacity
                style={styles.purchaseButton}
                onPress={() =>
                  router.push({
                    pathname: '/settings/tokens/purchase',
                    params: { packageId: pkg.id },
                  })
                }
              >
                <Text style={styles.purchaseButtonText}>Mua</Text>
              </TouchableOpacity>
            </View>
          ))}
        </AnimatedView>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  balanceCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  balanceTopRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  balanceLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  historyButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  historyButtonText: {
    fontSize: 14,
    color: '#2F4FCD',
    marginRight: 4,
  },
  balanceContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  balanceCircle: {
    marginRight: 20,
  },
  balanceInfo: {
    flex: 1,
  },
  balanceTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2F4FCD',
    marginBottom: 4,
  },
  balanceSubtitle: {
    fontSize: 13,
    color: '#666',
    marginBottom: 16,
  },
  addTokenButton: {
    backgroundColor: '#2F4FCD',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  addTokenButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  pricingCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  pricingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  pricingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pricingIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  pricingText: {
    fontSize: 15,
    color: '#333',
  },
  tokenChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E3E8FF',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 20,
  },
  tokenChipText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2F4FCD',
    marginLeft: 6,
  },
  packagesCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  packageItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 14,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  packageInfo: {
    flex: 1,
  },
  packageTokenAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  packagePrice: {
    fontSize: 14,
    color: '#666',
  },
  discountBadge: {
    backgroundColor: '#E3F2FD',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    marginRight: 12,
  },
  discountText: {
    fontSize: 12,
    color: '#1976D2',
    fontWeight: '500',
  },
  purchaseButton: {
    backgroundColor: '#E3E8FF',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  purchaseButtonText: {
    color: '#2F4FCD',
    fontWeight: '600',
    fontSize: 14,
  },
});

export default TokenSettingsScreen;
