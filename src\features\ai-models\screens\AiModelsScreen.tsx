import React, { useState, useCallback, useMemo, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  SectionList,
  Animated,
  Platform,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import Toast from 'react-native-toast-message';
import { useAiModels } from '../hooks/useAiModels';
import { AiModel } from '../types';
// import { aiModelStorage } from '@core/storage/aiModelStorage';
import { aiModelStorage } from '@/core/storage/aiModelStorage';
import Header from '@/shared/components/molecules/Header';
import { useTheme } from '@/core/theme/theme';
import { useToast } from '@/shared/hooks/useToast';

const { width } = Dimensions.get('window');

// <PERSON><PERSON>u cho các mô hình AI
const AI_MODEL_COLORS = [
  '#4361ee', // A
  '#4cc9f0', // B
  '#f72585', // C
  '#480ca8', // D
  '#f77f00', // E
  '#7209b7', // F
];

// Thêm interface cho filter
interface FilterOptions {
  showActiveOnly: boolean;
  minAccuracy: number;
}

export default function AiModelsScreen() {
  const router = useRouter();
  const { colors } = useTheme();
  const { showToast } = useToast();
  const { data: aiModels = [], isLoading } = useAiModels();
  const [selectedModelName, setSelectedModelName] = useState<string | null>(
    null,
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [recentModels, setRecentModels] = useState<AiModel[]>([]);
  const [showAllModels, setShowAllModels] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    showActiveOnly: false,
    minAccuracy: 0,
  });

  // Animations
  const fadeAnim = useState(new Animated.Value(0))[0];
  const scaleAnim = useState(new Animated.Value(0.95))[0];
  const searchAnim = useState(new Animated.Value(0))[0];
  const scrollYAnim = useState(new Animated.Value(0))[0];

  // Thêm hàm xử lý filter
  const handleFilterChange = useCallback(
    (newOptions: Partial<FilterOptions>) => {
      setFilterOptions(prev => ({ ...prev, ...newOptions }));
    },
    [],
  );

  // Load saved model only when needed
  const loadSavedModel = useCallback(() => {
    const savedDefaultModel = aiModelStorage.getDefaultModel();
    if (savedDefaultModel) {
      setSelectedModelName(savedDefaultModel.Name);
    }
  }, []);

  // Load recent models only when needed
  const loadRecentModels = useCallback(() => {
    const savedRecentModels = aiModelStorage.getRecentModels();
    if (savedRecentModels.length > 0) {
      setRecentModels(savedRecentModels);
    }
  }, []);

  // Load data when component mounts
  useEffect(() => {
    loadSavedModel();
    loadRecentModels();
  }, [loadSavedModel, loadRecentModels]);

  // Memoize filtered models to avoid recalculation
  const filteredModels = useMemo(() => {
    if (!searchQuery) return aiModels;
    return aiModels.filter(model =>
      model.Name.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }, [aiModels, searchQuery]);

  // Memoize recent models to avoid recalculation
  const recentFilteredModels = useMemo(() => {
    return filteredModels.filter(model =>
      recentModels.some(recent => recent.Model === model.Model),
    );
  }, [filteredModels, recentModels]);

  // Memoize other models to avoid recalculation
  const otherFilteredModels = useMemo(() => {
    return filteredModels.filter(
      model => !recentModels.some(recent => recent.Model === model.Model),
    );
  }, [filteredModels, recentModels]);

  // Lọc và tối ưu hoá danh sách mô hình
  const { sectionedModels } = useMemo(() => {
    const recentModelNames = recentModels.map(p => p.Name);
    const recentModelsData = aiModels.filter((p: AiModel) =>
      recentModelNames.includes(p.Name),
    );
    const otherModels = aiModels.filter(
      (p: AiModel) => !recentModelNames.includes(p.Name),
    );

    const displayedOtherModels = showAllModels
      ? otherModels
      : otherModels.slice(0, 10);

    const sectioned = [];

    if (recentModelsData.length > 0) {
      sectioned.push({
        title: 'Đã sử dụng gần đây',
        data: recentModelsData,
      });
    }

    sectioned.push({
      title: 'Tất cả mô hình',
      data: displayedOtherModels,
    });

    return {
      sectionedModels: sectioned,
    };
  }, [aiModels, recentModels, showAllModels]);

  // Lấy màu dựa vào tên mô hình
  const getModelColor = useCallback((modelName: string) => {
    if (!modelName) return AI_MODEL_COLORS[0];
    const firstChar = modelName.charAt(0).toUpperCase();
    const index = firstChar.charCodeAt(0) - 'A'.charCodeAt(0);
    return AI_MODEL_COLORS[index % AI_MODEL_COLORS.length];
  }, []);

  // Thêm hàm xử lý select model
  const handleSelectModel = async (model: AiModel) => {
    try {
      // Set selected model name
      setSelectedModelName(model.Name);

      // Update recent models list
      const updatedRecentModels = aiModelStorage.addRecentModel(model);
      setRecentModels(updatedRecentModels);

      // Save selected model to storage
      const saved = aiModelStorage.setDefaultModel(model);

      // Verify storage
      const savedModel = aiModelStorage.getDefaultModel();
      console.log('Saved model:', savedModel);
      console.log('Recent models:', aiModelStorage.getRecentModels());

      if (!saved || !savedModel) {
        throw new Error('Failed to save model');
      }

      showToast('success', `Mô hình đã chọn: ${model.Name}`);
    } catch (error) {
      console.error('Error selecting model:', error);
      showToast('error', 'Lỗi khi chọn mô hình');
    }
  };

  // Cập nhật renderModelItem để sử dụng handleSelectModel
  const renderModelItem = useCallback(
    ({ item }: { item: AiModel }) => {
      const isSelected = item.Name === selectedModelName;
      const modelColor = getModelColor(item.Name);

      return (
        <Animated.View style={styles.modelItemContainer}>
          <TouchableOpacity
            style={[styles.modelItem, isSelected && styles.selectedItem]}
            onPress={() => handleSelectModel(item)}
            activeOpacity={0.8}
          >
            <View style={[styles.modelIcon, { backgroundColor: modelColor }]}>
              <MaterialCommunityIcons name='brain' size={24} color='#fff' />
            </View>
            <View style={styles.modelContent}>
              <Text style={styles.modelTitle} numberOfLines={2}>
                {item.Model}
              </Text>
              <Text style={styles.modelCode}>Mã: {item.Name}</Text>
              {/* <Text style={styles.modelDescription} numberOfLines={1}>
                {item.Url}
              </Text> */}
            </View>
            {isSelected && (
              <View
                style={[
                  styles.checkmarkCircle,
                  { backgroundColor: modelColor },
                ]}
              >
                <Ionicons name='checkmark' size={16} color='#fff' />
              </View>
            )}
          </TouchableOpacity>
        </Animated.View>
      );
    },
    [selectedModelName, getModelColor, handleSelectModel],
  );

  // Render section header
  const renderSectionHeader = useCallback(
    ({ section: { title } }: { section: { title: string } }) => (
      <View style={styles.sectionHeader}>
        <View style={styles.sectionHeaderContent}>
          <Text style={styles.sectionHeaderText}>{title}</Text>
        </View>
      </View>
    ),
    [],
  );

  // Render empty component
  const renderEmptyComponent = useCallback(
    () => (
      <View style={styles.emptyContainer}>
        <View style={styles.emptyIconContainer}>
          <MaterialCommunityIcons name='brain' size={80} color='#ddd' />
        </View>
        <Text style={styles.emptyText}>
          {searchQuery
            ? 'Không tìm thấy mô hình phù hợp'
            : 'Không có mô hình nào'}
        </Text>
        <Text style={styles.emptySubtext}>
          {searchQuery
            ? 'Vui lòng thử từ khóa khác.'
            : 'Hãy liên hệ quản trị viên để thêm mô hình.'}
        </Text>
      </View>
    ),
    [searchQuery],
  );

  // Add animation on mount
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(searchAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Add refresh control
  const [refreshing, setRefreshing] = useState(false);
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    // Add your refresh logic here
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  // Thêm component Filter Modal
  const renderFilterModal = useCallback(() => {
    if (!showFilter) return null;

    return (
      <View style={styles.filterModal}>
        <View style={styles.filterHeader}>
          <Text style={styles.filterTitle}>Bộ lọc</Text>
          <TouchableOpacity onPress={() => setShowFilter(false)}>
            <Ionicons name='close' size={24} color='#333' />
          </TouchableOpacity>
        </View>

        <View style={styles.filterContent}>
          <View style={styles.filterSection}>
            <Text style={styles.filterSectionTitle}>
              Độ chính xác tối thiểu
            </Text>
            <View style={styles.accuracySlider}>
              <TouchableOpacity
                style={[
                  styles.accuracyButton,
                  filterOptions.minAccuracy === 0 &&
                    styles.accuracyButtonActive,
                ]}
                onPress={() => handleFilterChange({ minAccuracy: 0 })}
              >
                <Text style={styles.accuracyButtonText}>Tất cả</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.accuracyButton,
                  filterOptions.minAccuracy === 80 &&
                    styles.accuracyButtonActive,
                ]}
                onPress={() => handleFilterChange({ minAccuracy: 80 })}
              >
                <Text style={styles.accuracyButtonText}>80%+</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.accuracyButton,
                  filterOptions.minAccuracy === 90 &&
                    styles.accuracyButtonActive,
                ]}
                onPress={() => handleFilterChange({ minAccuracy: 90 })}
              >
                <Text style={styles.accuracyButtonText}>90%+</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.filterSection}>
            <Text style={styles.filterSectionTitle}>Trạng thái</Text>
            <TouchableOpacity
              style={styles.statusToggle}
              onPress={() =>
                handleFilterChange({
                  showActiveOnly: !filterOptions.showActiveOnly,
                })
              }
            >
              <Text style={styles.statusToggleText}>
                {filterOptions.showActiveOnly
                  ? 'Chỉ hiện mô hình đang hoạt động'
                  : 'Hiện tất cả mô hình'}
              </Text>
              <View
                style={[
                  styles.toggleSwitch,
                  filterOptions.showActiveOnly && styles.toggleSwitchActive,
                ]}
              >
                <View
                  style={[
                    styles.toggleKnob,
                    filterOptions.showActiveOnly && styles.toggleKnobActive,
                  ]}
                />
              </View>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.filterFooter}>
          <TouchableOpacity
            style={styles.resetButton}
            onPress={() => {
              setFilterOptions({ minAccuracy: 0, showActiveOnly: false });
            }}
          >
            <Text style={styles.resetButtonText}>Đặt lại</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.applyButton}
            onPress={() => setShowFilter(false)}
          >
            <Text style={styles.applyButtonText}>Áp dụng</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }, [showFilter, filterOptions, handleFilterChange]);

  // Lấy model mặc định
  const savedDefaultModel = aiModelStorage.getDefaultModel();
  console.log('Default model:', savedDefaultModel);

  // Lấy danh sách model gần đây
  const savedRecentModels = aiModelStorage.getRecentModels();
  console.log('Recent models:', savedRecentModels);

  // Kiểm tra xem có model nào đã lưu không
  if (savedDefaultModel) {
    console.log('Đã có model mặc định:', savedDefaultModel.Name);
  } else {
    console.log('Chưa có model mặc định');
  }

  if (savedRecentModels.length > 0) {
    console.log('Có', savedRecentModels.length, 'model gần đây');
  } else {
    console.log('Chưa có model gần đây nào');
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      {/* Header */}
      <Header
        title='Mô hình AI'
        RightComponent={
          <TouchableOpacity onPress={() => setShowFilter(true)}>
            <Ionicons name='filter' size={24} color='#333' />
          </TouchableOpacity>
        }
      />

      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        {/* Search Box */}
        <Animated.View
          style={[
            styles.searchContainer,
            {
              opacity: searchAnim,
              transform: [
                {
                  translateY: searchAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [20, 0],
                  }),
                },
              ],
            },
          ]}
        >
          <View style={styles.searchInputContainer}>
            <Ionicons
              name='search'
              size={20}
              color='#999'
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder='Tìm kiếm mô hình AI...'
              placeholderTextColor='#999'
              value={searchQuery}
              onChangeText={setSearchQuery}
              returnKeyType='search'
              autoCapitalize='none'
              autoCorrect={false}
              clearButtonMode='while-editing'
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={() => setSearchQuery('')}
              >
                <Ionicons name='close-circle' size={18} color='#999' />
              </TouchableOpacity>
            )}
          </View>
        </Animated.View>

        {/* Content */}
        <SectionList
          sections={sectionedModels}
          keyExtractor={item => item.Model}
          renderItem={renderModelItem}
          renderSectionHeader={renderSectionHeader}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyComponent}
          stickySectionHeadersEnabled={false}
          removeClippedSubviews={Platform.OS === 'android'}
          initialNumToRender={8}
          maxToRenderPerBatch={10}
          windowSize={10}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#4361ee']}
              tintColor='#4361ee'
            />
          }
        />
      </Animated.View>

      {/* Add Filter Modal */}
      {renderFilterModal()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  searchContainer: {
    marginBottom: 16,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: '#333',
  },
  clearButton: {
    padding: 4,
  },
  listContent: {
    paddingBottom: 24,
    flexGrow: 1,
  },
  modelItemContainer: {
    marginBottom: 12,
  },
  modelItem: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedItem: {
    borderWidth: 2,
    borderColor: '#4361ee',
    backgroundColor: '#F5F7FF',
  },
  modelIcon: {
    width: 50,
    height: 50,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  modelContent: {
    flex: 1,
  },
  modelTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
    lineHeight: 20,
  },
  modelCode: {
    fontSize: 13,
    color: '#666',
    marginBottom: 2,
  },
  modelDescription: {
    fontSize: 12,
    color: '#888',
    marginBottom: 4,
  },
  modelAccuracy: {
    fontSize: 12,
    color: '#4361ee',
    fontWeight: '500',
  },
  checkmarkCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sectionHeader: {
    paddingVertical: 10,
    paddingHorizontal: 4,
    marginTop: 8,
    marginBottom: 12,
  },
  sectionHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  sectionHeaderText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#555',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    marginTop: 40,
  },
  emptyIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  filterButton: {
    marginLeft: 'auto',
    padding: 8,
  },
  filterModal: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    zIndex: 1000,
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  filterTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  filterContent: {
    marginBottom: 20,
  },
  filterSection: {
    marginBottom: 20,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
    marginBottom: 12,
  },
  accuracySlider: {
    flexDirection: 'row',
    gap: 8,
  },
  accuracyButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f2f5',
  },
  accuracyButtonActive: {
    backgroundColor: '#4361ee',
  },
  accuracyButtonText: {
    fontSize: 14,
    color: '#666',
  },
  statusToggle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  statusToggleText: {
    fontSize: 14,
    color: '#333',
  },
  toggleSwitch: {
    width: 50,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#e0e0e0',
    padding: 2,
  },
  toggleSwitchActive: {
    backgroundColor: '#4361ee',
  },
  toggleKnob: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
  toggleKnobActive: {
    transform: [{ translateX: 22 }],
  },
  filterFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  resetButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#f0f2f5',
    alignItems: 'center',
  },
  resetButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  applyButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#4361ee',
    alignItems: 'center',
  },
  applyButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#fff',
  },
});
