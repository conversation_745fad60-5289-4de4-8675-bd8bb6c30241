import React, {
  useState,
  useCallback,
  useMemo,
  useEffect,
  useRef,
} from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
  Dimensions,
  Platform,
  Alert,
  ScrollView,
} from 'react-native';
import DraggableFlatList, {
  RenderItemParams,
  ScaleDecorator,
} from 'react-native-draggable-flatlist';
import { Ionicons } from '@expo/vector-icons';
import { useScan } from '@features/scan/context/ScanContext';
import { StorageManager } from '@features/scan/services/memoryManager';
import { ROUTES } from '@core/constants/routes';
import { useRouter } from 'expo-router';
import {
  COLORS,
  FONT_SIZES,
  FONT_WEIGHTS,
  SPACING,
  BORDER_RADIUS,
} from '@core/constants/theme';
import { useImageUpload } from '@features/scan/hooks/useImageUpload';
import DocumentScanner, {
  ScanDocumentResponse,
} from 'react-native-document-scanner-plugin';
import { Safe<PERSON>reaView } from 'react-native-safe-area-context';
import {
  Spacings,
  Picker,
  PickerValue,
  PickerItemProps,
  FloatingButton,
  Toast,
} from 'react-native-ui-lib';
import { useDocumentTypes } from '@features/document/hooks/useDocumentTypes';
import { useDuAnQuery } from '@features/duan/hooks/useDuAnQuery';
import { useProfile } from '@features/auth/hooks/useAuth';
import { documentStorage } from '@core/storage/documentStorage';
import { projectStorage } from '@core/storage/projectStorage';
import FloatingActionBar from '@/shared/components/molecules/FloatingActionBar/FloatingActionBar';
import ReusableBottomSheet, {
  ReusableBottomSheetRef,
} from '@/shared/components/molecules/ReusableBottomSheet';
import { aiModelStorage } from '@/core/storage/aiModelStorage';
import { useScanActions } from '@/features/scan/hooks/useScanActions';
import { useToast } from '@/shared/hooks/useToast';
import Header from '@/shared/components/molecules/Header';

const { width } = Dimensions.get('window');
const ITEM_WIDTH = width / 3 - 12;

type QualityOption = string;
type FormatOption = string;
type LanguageOption = string;

export default function ScanResultScreen() {
  const [selected, setSelected] = useState<Set<number>>(new Set());
  const [images, setImages] = useState<Array<{ key: string; uri: string }>>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showCustomize, setShowCustomize] = useState(false);
  const [autoScanTriggered, setAutoScanTriggered] = useState(false);
  const bottomSheetRef = useRef<ReusableBottomSheetRef>(null);
  const memoryManager = StorageManager.getInstance();
  const defaultModel = aiModelStorage.getDefaultModel();

  // Picker states
  const [selectedQuality, setSelectedQuality] = useState<QualityOption>('high');
  const [selectedFormat, setSelectedFormat] = useState<FormatOption>('pdf');
  const [selectedLanguage, setSelectedLanguage] =
    useState<LanguageOption>('vi');
  const {
    currentSession,
    addScanSession,
    updateCurrentSessionImages,
    removeImagesFromCurrentSession,
  } = useScan();
  const {
    handleShareMultiple,
    handleSave,
    handleDelete,
    startScan,
    pickImagesFromLibrary,
  } = useScanActions();
  const router = useRouter();
  const { mutate: uploadImages, isPending: isUploading } = useImageUpload();
  const { showSuccess, showError, showInfo } = useToast();

  // Get user profile for API queries
  const { data: userProfile } = useProfile();
  const donViId = userProfile?.DonViID;

  // Fetch projects
  const { data: projectsData, isLoading: isLoadingProjects } = useDuAnQuery({
    donViId,
    enabled: !!donViId,
  });

  // Fetch document types
  const { data: docTypesData, isLoading: isLoadingDocTypes } = useDocumentTypes(
    {
      SearchText: '',
      PageSize: 100,
    },
  );

  // Get AI models from storage
  const aiModels = aiModelStorage.getRecentModels();

  // Convert API data to picker items
  const projectOptions = useMemo(() => {
    if (!projectsData?.Items) return [];
    return projectsData.Items.map(project => ({
      label: project.TenDuAn,
      value: project.DuAnId,
    }));
  }, [projectsData]);

  const documentTypeOptions = useMemo(() => {
    if (!docTypesData?.Items) return [];
    return docTypesData.Items.map(docType => ({
      label: docType.NghiepVu,
      value: docType.NghiepVuID,
    }));
  }, [docTypesData]);

  const aiModelOptions = useMemo(() => {
    return aiModels.map(model => ({
      label: model.Name,
      value: model.Model,
    }));
  }, [aiModels]);

  // Get default values from storage
  useEffect(() => {
    const defaultProjectId = projectStorage.getDefaultProject();
    const defaultDocType = documentStorage.getDefaultDocumentType();
    const defaultModel = aiModelStorage.getDefaultModel();

    if (defaultProjectId) {
      setSelectedQuality(defaultProjectId);
    }
    if (defaultDocType) {
      setSelectedFormat(defaultDocType);
    }
    if (defaultModel) {
      setSelectedLanguage(defaultModel.Model);
    }
  }, []);

  useEffect(() => {
    if (currentSession) {
      console.log(
        'currentSession changed, images count:',
        currentSession.images.length,
      );
      const mappedImages = currentSession.images.map((uri, index) => ({
        key: `${index}`,
        uri,
      }));
      console.log('Setting images array:', mappedImages.length);
      setImages(mappedImages);
    } else {
      setImages([]);
    }
  }, [currentSession]);

  // Tự động quét khi vào màn hình nếu chưa có ảnh nào và chưa có currentSession
  useEffect(() => {
    if (!autoScanTriggered && images.length === 0 && !currentSession) {
      setAutoScanTriggered(true);
      // Delay một chút để đảm bảo navigation đã hoàn thành
      setTimeout(() => {
        handleScanMore();
      }, 300);
    }
  }, [images, autoScanTriggered, currentSession]);

  const handleSendToAPI = useCallback(
    async (selectedUris: string[]) => {
      if (!selectedUris.length) {
        return showInfo('Bạn chưa chọn ảnh nào để gửi.');
      }

      try {
        uploadImages(selectedUris);
      } catch (error: any) {
        // Hiển thị thông báo lỗi tùy theo nội dung
        showInfo(error.message || 'Đã xảy ra lỗi khi gửi ảnh.', 'error');
      }
    },
    [uploadImages, showInfo],
  );

  const getFileName = useCallback((uri: string) => {
    const parts = uri.split('/');
    return parts[parts.length - 1];
  }, []);

  const toggleSelect = useCallback((index: number) => {
    setSelected(prev => {
      const newSet = new Set(prev);
      newSet.has(index) ? newSet.delete(index) : newSet.add(index);
      return newSet;
    });
  }, []);

  const handleScanMore = useCallback(async () => {
    setIsProcessing(true);
    try {
      const result: ScanDocumentResponse | undefined =
        await DocumentScanner.scanDocument({ maxNumDocuments: 5 });
      if (
        result?.status === 'success' &&
        Array.isArray(result.scannedImages) &&
        result.scannedImages.length > 0
      ) {
        if (currentSession) {
          // Nếu đã có session, thêm ảnh vào session hiện tại
          await updateCurrentSessionImages(result.scannedImages);
        } else {
          // Nếu chưa có session, tạo session mới
          await addScanSession(result.scannedImages);
        }
        // Không cần router.push vì đã ở màn hình /scan rồi
      }
    } catch (error) {
      console.error('Error starting new scan:', error);
      showError('Không thể bắt đầu quét mới', 'error');
    } finally {
      setIsProcessing(false);
    }
  }, [currentSession, addScanSession, updateCurrentSessionImages, showError]);

  const handlePickImages = useCallback(async () => {
    try {
      await pickImagesFromLibrary();
    } catch (error) {
      console.error('Error picking images:', error);
      showInfo('Không thể chọn ảnh từ thư viện', 'error');
    }
  }, [pickImagesFromLibrary, showInfo]);

  const handleAddAction = useCallback(() => {
    Alert.alert('Quét thêm', 'Chọn cách thêm ảnh vào tài liệu hiện tại', [
      {
        text: 'Quét tài liệu',
        onPress: handleScanMore,
      },
      {
        text: 'Chọn từ thư viện',
        onPress: handlePickImages,
      },
      {
        text: 'Hủy',
        style: 'cancel',
      },
    ]);
  }, [handleScanMore, handlePickImages]);

  const handleDeleteAndUpdateUI = useCallback(
    async (indexesToDelete: number[]) => {
      try {
        await handleDelete(indexesToDelete);
        setSelected(new Set());
        setImages(prevImages =>
          prevImages.filter((_, index) => !indexesToDelete.includes(index)),
        );
        showSuccess(`Đã xóa ${indexesToDelete.length} ảnh.`, 'success');
      } catch (error) {
        console.error('Lỗi khi xóa:', error);
        showError('Xóa ảnh thất bại!', 'error');
      }
    },
    [handleDelete, showSuccess, showError],
  );

  const selectedUris = useMemo(
    () => images.filter((_, idx) => selected.has(idx)).map(item => item.uri),
    [images, selected],
  );

  const renderCardItem = useCallback(
    ({
      item,
      drag,
      isActive,
      getIndex,
    }: RenderItemParams<{ key: string; uri: string }>) => {
      const index = getIndex() ?? 0;
      const isSelected = selected.has(index);

      return (
        <ScaleDecorator>
          <TouchableOpacity
            onPress={() => toggleSelect(index)}
            onLongPress={drag}
            style={[
              styles.imageCard,
              isSelected && styles.selectedCard,
              isActive && styles.activeCard,
            ]}
            activeOpacity={0.8}
          >
            <View style={styles.indexBadge}>
              <Text style={styles.indexText}>{index + 1}</Text>
            </View>

            <Image
              source={{ uri: item.uri }}
              resizeMode='cover'
              style={styles.image}
            />

            <View style={styles.fileInfoContainer}>
              <Text
                numberOfLines={1}
                ellipsizeMode='middle'
                style={styles.fileName}
              >
                {getFileName(item.uri)}
              </Text>
            </View>

            {isSelected && (
              <View style={styles.selectedBadge}>
                <Ionicons name='checkmark' size={14} color='#FFF' />
              </View>
            )}
          </TouchableOpacity>
        </ScaleDecorator>
      );
    },
    [selected, toggleSelect, getFileName],
  );

  const handleSettings = useCallback(() => {
    setShowCustomize(false);
    bottomSheetRef.current?.present();
  }, []);

  const handleQualityChange = useCallback((value: PickerValue) => {
    if (typeof value === 'string') {
      setSelectedQuality(value as QualityOption);
    }
  }, []);

  const handleFormatChange = useCallback((value: PickerValue) => {
    if (typeof value === 'string') {
      setSelectedFormat(value as FormatOption);
    }
  }, []);

  const handleLanguageChange = useCallback((value: PickerValue) => {
    if (typeof value === 'string') {
      setSelectedLanguage(value as LanguageOption);
    }
  }, []);

  // Helper functions to get display labels
  const getProjectLabel = useCallback(() => {
    const project = projectOptions.find(p => p.value === selectedQuality);
    return project?.label || 'Chưa chọn dự án';
  }, [projectOptions, selectedQuality]);

  const getDocumentTypeLabel = useCallback(() => {
    const docType = documentTypeOptions.find(d => d.value === selectedFormat);
    return docType?.label || 'Chưa chọn loại văn bản';
  }, [documentTypeOptions, selectedFormat]);

  const getAiModelLabel = useCallback(() => {
    const model = aiModelOptions.find(m => m.value === selectedLanguage);
    return model?.label || 'Chưa chọn mô hình';
  }, [aiModelOptions, selectedLanguage]);

  const handleCustomize = useCallback(() => {
    setShowCustomize(true);
  }, []);

  const handleCloseCustomize = useCallback(() => {
    setShowCustomize(false);
  }, []);

  if (!images.length) {
    return (
      <SafeAreaView style={styles.emptyContainer}>
        <Header
          title=''
          style={{ backgroundColor: 'transparent', borderBottomWidth: 0 }}
        />
        <View style={styles.emptyContent}>
          {/* Decorative Background Elements */}
          <View style={styles.decorativeCircle1} />
          <View style={styles.decorativeCircle2} />
          <View style={styles.decorativeCircle3} />

          {/* Main Icon with Enhanced Background */}
          <View style={styles.iconContainer}>
            <View style={styles.iconBackground}>
              <View style={styles.iconInnerCircle}>
                <Ionicons name='images' size={40} color={COLORS.primary} />
              </View>
            </View>
            <View style={styles.iconGlow} />
          </View>

          {/* Enhanced Title with Gradient */}
          <View style={styles.titleContainer}>
            <Text style={styles.emptyTitle}>Chưa có ảnh nào</Text>
            <View style={styles.titleUnderline} />
          </View>

          {/* Improved Description */}
          <Text style={styles.emptyDescription}>
            Bắt đầu quét tài liệu hoặc chọn ảnh từ thư viện để xử lý với AI
          </Text>

          {/* Enhanced Action Buttons */}
          <View style={styles.actionsContainer}>
            <TouchableOpacity
              style={styles.primaryButton}
              onPress={handleScanMore}
            >
              <View style={styles.buttonContent}>
                <View style={styles.buttonIconContainer}>
                  <Ionicons name='camera' size={22} color='#fff' />
                </View>
                <Text style={styles.primaryButtonText}>Quét ngay</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.secondaryButton}
              onPress={handlePickImages}
            >
              <View style={styles.buttonContent}>
                <View style={styles.secondaryButtonIconContainer}>
                  <Ionicons name='images' size={22} color={COLORS.primary} />
                </View>
                <Text style={styles.secondaryButtonText}>Chọn từ thư viện</Text>
              </View>
            </TouchableOpacity>
          </View>

          {/* Enhanced Tips Section */}
          <View style={styles.tipsContainer}>
            <View style={styles.tipsHeader}>
              <View style={styles.tipsIconContainer}>
                <Text style={styles.tipsIcon}>💡</Text>
              </View>
              <Text style={styles.tipsTitle}>Mẹo để quét tốt hơn</Text>
            </View>
            <View style={styles.tipsContent}>
              <View style={styles.tipItem}>
                <View style={styles.tipDot} />
                <Text style={styles.tipText}>
                  Đảm bảo ánh sáng đủ sáng và đều
                </Text>
              </View>
              <View style={styles.tipItem}>
                <View style={styles.tipDot} />
                <Text style={styles.tipText}>
                  Giữ camera ổn định và vuông góc
                </Text>
              </View>
              <View style={styles.tipItem}>
                <View style={styles.tipDot} />
                <Text style={styles.tipText}>Tránh bóng mờ và phản quang</Text>
              </View>
            </View>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => {
            if (selected.size === images.length) {
              setSelected(new Set());
            } else {
              const allIndexes = new Set<number>(images.map((_, i) => i));
              setSelected(allIndexes);
            }
          }}
          style={styles.headerButton}
        >
          <Text style={styles.headerButtonText}>
            {selected.size === images.length ? 'Bỏ chọn tất cả' : 'Chọn tất cả'}
          </Text>
        </TouchableOpacity>

        <View style={styles.selectedCount}>
          <Text style={styles.selectedCountText}>
            {selected.size > 0
              ? `${selected.size} / ${images.length}`
              : 'Chưa chọn ảnh'}
          </Text>
        </View>

        {selected.size > 0 && (
          <TouchableOpacity
            onPress={async () => {
              const indexesToDelete = Array.from(selected);
              await handleDeleteAndUpdateUI(indexesToDelete);
            }}
            style={[styles.headerButton, styles.deleteButton]}
          >
            <Text style={styles.deleteButtonText}>Xóa</Text>
          </TouchableOpacity>
        )}
      </View>

      {isProcessing || isUploading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={COLORS.primaryLight} />
          <Text style={styles.loadingText}>
            {isUploading ? 'Đang gửi ảnh...' : 'Đang xử lý...'}
          </Text>
        </View>
      ) : (
        <DraggableFlatList
          data={images}
          onDragEnd={({ data }) => setImages(data)}
          numColumns={3}
          keyExtractor={item => item.key}
          renderItem={renderCardItem}
          contentContainerStyle={styles.listContent}
          initialNumToRender={12}
          maxToRenderPerBatch={9}
          windowSize={5}
          removeClippedSubviews={true}
          updateCellsBatchingPeriod={50}
          getItemLayout={(data, index) => ({
            length: ITEM_WIDTH + 8,
            offset: (ITEM_WIDTH + 8) * Math.floor(index / 3),
            index,
          })}
        />
      )}

      <FloatingActionBar
        showEdit={false}
        onEdit={() => {}}
        visible={selected.size > 0}
        onAdd={handleAddAction}
        onShare={() => handleShareMultiple(selectedUris)}
        onSettings={handleSettings}
        onSave={() => handleSave(selectedUris)}
        onDelete={() => {
          const indexesToDelete = Array.from(selected);
          handleDeleteAndUpdateUI(indexesToDelete);
        }}
        onSend={
          selectedUris.length > 0 && !isUploading
            ? () => handleSendToAPI(selectedUris)
            : undefined
        }
      />

      <ReusableBottomSheet
        ref={bottomSheetRef}
        title='Số hóa hồ sơ'
        snapPoints={['75%']}
        defaultIndex={0}
        // enablePanDownToClose={true}
        // style={styles.bottomSheet}
      >
        <View style={styles.bottomSheetContent}>
          <View style={styles.formHeader}>
            <View style={styles.formHeaderRow}>
              <Ionicons
                name='information-circle-outline'
                size={24}
                color={COLORS.primary}
                style={styles.formHeaderIcon}
              />
              <View style={styles.formHeaderText}>
                <Text style={styles.formTitle}>Thông tin tài liệu</Text>
                <Text style={styles.formSubtitle}>
                  Thông tin mặc định cho việc số hóa hồ sơ
                </Text>
              </View>
            </View>
          </View>

          <ScrollView
            style={styles.formScrollView}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.formScrollContent}
          >
            <View style={styles.formContainer}>
              <View style={styles.infoCard}>
                <View style={styles.infoSection}>
                  <View style={styles.infoLabelRow}>
                    <Ionicons
                      name='folder-outline'
                      size={18}
                      color={COLORS.primary}
                      style={styles.infoLabelIcon}
                    />
                    <Text style={styles.infoLabel}>Dự án</Text>
                  </View>
                  <View style={styles.infoValue}>
                    <Text style={styles.infoText}>{getProjectLabel()}</Text>
                  </View>
                </View>

                <View style={styles.infoDivider} />

                <View style={styles.infoSection}>
                  <View style={styles.infoLabelRow}>
                    <Ionicons
                      name='document-text-outline'
                      size={18}
                      color={COLORS.primary}
                      style={styles.infoLabelIcon}
                    />
                    <Text style={styles.infoLabel}>Loại văn bản</Text>
                  </View>
                  <View style={styles.infoValue}>
                    <Text style={styles.infoText}>
                      {getDocumentTypeLabel()}
                    </Text>
                  </View>
                </View>

                <View style={styles.infoDivider} />

                <View style={styles.infoSection}>
                  <View style={styles.infoLabelRow}>
                    <Ionicons
                      name='hardware-chip-outline'
                      size={18}
                      color={COLORS.primary}
                      style={styles.infoLabelIcon}
                    />
                    <Text style={styles.infoLabel}>Mô hình</Text>
                  </View>
                  <View style={styles.infoValue}>
                    <Text style={styles.infoText}>{getAiModelLabel()}</Text>
                  </View>
                </View>
              </View>

              <View style={styles.selectedInfoCard}>
                <View style={styles.selectedInfoRow}>
                  <Ionicons
                    name='images-outline'
                    size={20}
                    color={COLORS.primary}
                    style={styles.selectedInfoIcon}
                  />
                  <Text style={styles.selectedInfoText}>
                    Đã chọn {selected.size} ảnh
                  </Text>
                </View>
              </View>
            </View>
          </ScrollView>

          <View style={styles.bottomSheetActions}>
            <TouchableOpacity
              style={[styles.actionButton, styles.sendButton]}
              onPress={() => {
                handleSendToAPI(selectedUris);
                bottomSheetRef.current?.close();
              }}
            >
              <Ionicons
                name='cloud-upload-outline'
                size={20}
                color='#fff'
                style={styles.buttonIcon}
              />
              <Text style={styles.sendButtonText}>Số hóa hồ sơ</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ReusableBottomSheet>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eef2f7',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  headerButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: '#f0f4f8',
  },
  headerButtonText: {
    color: COLORS.primary,
    fontSize: 14,
    fontWeight: '600',
  },
  deleteButton: {
    backgroundColor: '#fff1f0',
  },
  deleteButtonText: {
    color: '#ff4d4f',
    fontWeight: '600',
  },
  selectedCount: {
    backgroundColor: '#e6f7ff',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  selectedCountText: {
    fontSize: 13,
    color: '#1890ff',
    fontWeight: '500',
  },
  listContent: {
    padding: 12,
  },
  imageCard: {
    width: ITEM_WIDTH - 8,
    height: ITEM_WIDTH * 1.2,
    margin: 6,
    borderRadius: 18,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    backgroundColor: '#fff',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.12,
        shadowRadius: 6,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  selectedCard: {
    borderWidth: 2,
    borderColor: COLORS.primary,
    ...Platform.select({
      ios: {
        shadowColor: COLORS.primary,
        shadowOpacity: 0.3,
      },
      android: {
        elevation: 6,
      },
    }),
  },
  activeCard: {
    opacity: 0.8,
    transform: [{ scale: 1.05 }],
  },
  image: {
    width: '100%',
    height: '80%',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  indexBadge: {
    position: 'absolute',
    top: 10,
    left: 10,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 14,
    zIndex: 1,
  },
  indexText: {
    color: '#fff',
    fontSize: 13,
    fontWeight: '600',
  },
  fileInfoContainer: {
    height: '20%',
    backgroundColor: '#fafbfc',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 10,
    borderTopWidth: 1,
    borderTopColor: '#f0f2f5',
  },
  fileName: {
    fontSize: 13,
    color: '#4a5568',
    width: '100%',
    textAlign: 'center',
  },
  selectedBadge: {
    position: 'absolute',
    top: 10,
    right: 10,
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    borderWidth: 2,
    borderColor: '#fff',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 3,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#4a5568',
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  emptyContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.1,
        shadowRadius: 16,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  iconBackground: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#f0f9ff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconInnerCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconGlow: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    borderRadius: 60,
    backgroundColor: 'rgba(255,255,255,0.2)',
    transform: [{ scale: 1.2 }],
  },
  titleContainer: {
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    color: '#1a1a1a',
    fontWeight: '700',
    marginBottom: 12,
    textAlign: 'center',
  },
  titleUnderline: {
    height: 2,
    backgroundColor: COLORS.primary,
    width: 40,
    borderRadius: 1,
    marginHorizontal: 'auto',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  actionsContainer: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    gap: 16,
    marginBottom: 32,
  },
  primaryButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 32,
    width: '100%',
    maxWidth: 280,
    ...Platform.select({
      ios: {
        shadowColor: COLORS.primary,
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.3,
        shadowRadius: 16,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  secondaryButton: {
    backgroundColor: '#fff',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 32,
    width: '100%',
    maxWidth: 280,
    borderWidth: 2,
    borderColor: COLORS.primary,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIconContainer: {
    marginRight: 12,
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  secondaryButtonIconContainer: {
    marginRight: 12,
  },
  secondaryButtonText: {
    color: COLORS.primary,
    fontSize: 18,
    fontWeight: '600',
  },
  tipsContainer: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 16,
    width: '100%',
    maxWidth: 320,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.08,
        shadowRadius: 12,
      },
      android: {
        elevation: 6,
      },
    }),
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tipsIconContainer: {
    marginRight: 8,
  },
  tipsIcon: {
    fontSize: 16,
    color: '#1a1a1a',
    fontWeight: '600',
  },
  tipsTitle: {
    fontSize: 16,
    color: '#1a1a1a',
    fontWeight: '600',
    marginBottom: 8,
  },
  tipsContent: {
    flexDirection: 'column',
    gap: 8,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tipDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.primary,
    marginRight: 8,
  },
  tipText: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
  },
  bottomSheet: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  bottomSheetContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  formScrollView: {
    flex: 1,
  },
  formHeader: {
    backgroundColor: '#fff',
    paddingHorizontal: 0,
    paddingTop: 8,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  formHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  formHeaderIcon: {
    marginRight: 8,
  },
  formHeaderText: {
    flexDirection: 'column',
  },
  formTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  formSubtitle: {
    fontSize: 13,
    color: '#64748b',
    lineHeight: 18,
  },
  formContainer: {
    paddingHorizontal: 0,
    paddingTop: 12,
  },
  infoCard: {
    backgroundColor: '#fff',
    padding: 0,
    borderRadius: 10,
    marginBottom: 16,
  },
  infoSection: {
    marginBottom: 16,
  },
  infoLabelRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoLabelIcon: {
    marginRight: 8,
  },
  infoLabel: {
    fontSize: 14,
    color: '#1a1a1a',
    fontWeight: '600',
  },
  infoValue: {
    backgroundColor: '#f8fafc',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  infoText: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
  infoDivider: {
    height: 1,
    backgroundColor: '#e2e8f0',
    marginVertical: 12,
  },
  selectedInfoCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 10,
    marginTop: 0,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  selectedInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedInfoIcon: {
    marginRight: 8,
  },
  selectedInfoText: {
    fontSize: 14,
    color: '#64748b',
    fontWeight: '600',
  },
  pickerCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 10,
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  pickerSection: {
    marginBottom: 16,
  },
  pickerLabelRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  pickerLabelIcon: {
    marginRight: 8,
  },
  pickerLabel: {
    fontSize: 14,
    color: '#1a1a1a',
    fontWeight: '600',
  },
  pickerDivider: {
    height: 1,
    backgroundColor: '#e2e8f0',
    marginVertical: 12,
  },
  pickerWrapper: {
    borderRadius: 8,
    backgroundColor: '#fff',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
      },
      android: {
        elevation: 1,
      },
    }),
  },
  pickerInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 14,
    backgroundColor: '#fff',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  pickerText: {
    color: '#2D2D2DFF',
    fontSize: 14,
    fontWeight: '500',
  },
  pickerPlaceholder: {
    color: '#94a3b8',
  },
  searchStyle: {
    color: '#1a1a1a',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    marginHorizontal: 12,
    marginTop: 6,
  },
  pickerTopBarTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  pickerTopBarContainer: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    paddingVertical: 14,
  },
  formScrollContent: {
    paddingBottom: 20,
  },
  bottomSheetActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  actionButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  sendButton: {
    backgroundColor: COLORS.primary,
    flex: 2,
    ...Platform.select({
      ios: {
        shadowColor: COLORS.primary,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  closeButton: {
    backgroundColor: '#f8fafc',
    flex: 1,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  customizeButton: {
    backgroundColor: '#f0f4f8',
    flex: 1,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  buttonIcon: {
    marginRight: 8,
  },
  sendButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  closeButtonText: {
    color: '#64748b',
    fontSize: 14,
    fontWeight: '500',
  },
  customizeButtonText: {
    color: COLORS.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  decorativeCircle1: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    borderRadius: 1000,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  decorativeCircle2: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    borderRadius: 1000,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  decorativeCircle3: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    borderRadius: 1000,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
});
