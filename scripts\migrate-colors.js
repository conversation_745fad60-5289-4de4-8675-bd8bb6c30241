#!/usr/bin/env node

/**
 * Script để tự động chuyển đổi colors từ cách cũ sang React Native UI Lib
 * 
 * Usage: node scripts/migrate-colors.js
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Mapping từ cách cũ sang mới
const colorMappings = {
  // Background mappings
  'Colors.light.background': 'Colors.$backgroundDefault',
  'Colors.dark.background': 'Colors.$backgroundDefault',
  'Colors.light.primary': 'Colors.$backgroundPrimary',
  'Colors.dark.primary': 'Colors.$backgroundPrimary',
  
  // Text mappings
  'Colors.light.text': 'Colors.$textDefault',
  'Colors.dark.text': 'Colors.$textDefault',
  
  // Legacy colors
  'Colors.light.black': 'Colors.black',
  'Colors.light.white': 'Colors.white',
  'Colors.light.gray100': 'Colors.gray100',
  'Colors.light.gray500': 'Colors.gray500',
  'Colors.light.gray900': 'Colors.gray900',
  
  // Button colors
  'Colors.button.primary': 'Colors.buttonPrimary',
  'Colors.button.secondary': 'Colors.buttonSecondary',
  'Colors.button.border': 'Colors.buttonBorder',
};

// Style mappings cho modifiers
const styleMappings = {
  'backgroundColor: Colors.light.background': 'bg-$backgroundDefault',
  'backgroundColor: Colors.dark.background': 'bg-$backgroundDefault',
  'backgroundColor: Colors.light.primary': 'bg-$backgroundPrimary',
  'backgroundColor: Colors.dark.primary': 'bg-$backgroundPrimary',
  'backgroundColor: Colors.button.primary': 'bg-$backgroundPrimary',
  'backgroundColor: Colors.button.secondary': 'bg-$backgroundSuccess',
  
  'color: Colors.light.text': '$textDefault',
  'color: Colors.dark.text': '$textDefault',
  'color: Colors.light.primary': '$textPrimary',
  'color: Colors.dark.primary': '$textPrimary',
};

function migrateFile(filePath) {
  console.log(`Migrating: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  
  // Replace color references
  Object.entries(colorMappings).forEach(([oldPattern, newPattern]) => {
    if (content.includes(oldPattern)) {
      content = content.replace(new RegExp(oldPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newPattern);
      hasChanges = true;
      console.log(`  - ${oldPattern} -> ${newPattern}`);
    }
  });
  
  // Replace style patterns
  Object.entries(styleMappings).forEach(([oldPattern, newPattern]) => {
    if (content.includes(oldPattern)) {
      content = content.replace(new RegExp(oldPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newPattern);
      hasChanges = true;
      console.log(`  - ${oldPattern} -> ${newPattern}`);
    }
  });
  
  // Update imports
  if (content.includes("import Colors from '@core/constants/Colors'")) {
    content = content.replace(
      "import Colors from '@core/constants/Colors'",
      "import { Colors } from 'react-native-ui-lib'"
    );
    hasChanges = true;
    console.log(`  - Updated import to react-native-ui-lib`);
  }
  
  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  ✓ Updated ${filePath}`);
  } else {
    console.log(`  - No changes needed`);
  }
  
  return hasChanges;
}

function main() {
  console.log('🚀 Starting color migration...\n');
  
  // Find all TypeScript/JavaScript files
  const files = glob.sync('src/**/*.{ts,tsx,js,jsx}', {
    ignore: [
      'src/core/constants/Colors.ts',
      'src/core/constants/theme.ts',
      'src/core/constants/ColorsUsageExample.tsx',
      'src/core/constants/README.md',
      'node_modules/**',
      '**/*.d.ts'
    ]
  });
  
  let totalFiles = 0;
  let updatedFiles = 0;
  
  files.forEach(file => {
    totalFiles++;
    if (migrateFile(file)) {
      updatedFiles++;
    }
  });
  
  console.log(`\n✅ Migration completed!`);
  console.log(`📊 Files processed: ${totalFiles}`);
  console.log(`📝 Files updated: ${updatedFiles}`);
  
  console.log(`\n📋 Next steps:`);
  console.log(`1. Review the changes in updated files`);
  console.log(`2. Test the app to ensure colors work correctly`);
  console.log(`3. Update any remaining manual color references`);
  console.log(`4. Consider using modifiers (bg-$backgroundPrimary) instead of style props`);
}

if (require.main === module) {
  main();
}

module.exports = { migrateFile, colorMappings, styleMappings }; 