import { useCallback, useEffect, useState } from 'react';
import { MMKV } from 'react-native-mmkv';
import { Language, DEFAULT_LANGUAGE, getTranslation } from '../translations';

const storage = new MMKV();
const LANGUAGE_KEY = 'app_language';

export const useLanguage = () => {
    const [currentLanguage, setCurrentLanguage] = useState<Language>(() => {
        const savedLanguage = storage.getString(LANGUAGE_KEY);
        return (savedLanguage as Language) || DEFAULT_LANGUAGE;
    });

    useEffect(() => {
        storage.set(LANGUAGE_KEY, currentLanguage);
    }, [currentLanguage]);

    const t = useCallback(
        (key: string, language?: Language) => {
            return getTranslation(key as any, language || currentLanguage);
        },
        [currentLanguage],
    );

    const changeLanguage = useCallback((language: Language) => {
        setCurrentLanguage(language);
    }, []);

    return {
        currentLanguage,
        changeLanguage,
        t,
    };
}; 