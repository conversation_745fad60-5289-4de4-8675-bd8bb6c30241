# React Native UI Lib Colors - Hướng dẫn sử dụng

## Tổng quan

Dự án đã được chuyển đổi từ cách viết colors thủ công sang sử dụng **React Native UI Lib Colors** với **Design Tokens** và **Dark Mode** support.

## Lợi ích của việc chuyển đổi

1. **Design Tokens**: Hệ thống màu sắc có ý nghĩa ngữ nghĩa
2. **Dark Mode**: Tự động hỗ trợ chế độ tối
3. **Modifiers**: Cú pháp ngắn gọn và tiện lợi
4. **Type Safety**: Hỗ trợ TypeScript tốt hơn
5. **Consistency**: Đảm bảo tính nhất quán trong toàn bộ app

## Cách sử dụng

### 1. Import Colors

```typescript
import { Colors } from 'react-native-ui-lib';
// hoặc
import Colors from '@core/constants/Colors';
```

### 2. Sử dụng Design Tokens

#### Background Tokens
```typescript
// Trực tiếp
<View style={{ backgroundColor: Colors.$backgroundPrimary }}>

// Sử dụng modifiers (khuyến nghị)
<View bg-$backgroundPrimary>
<View bg-$backgroundSuccess>
<View bg-$backgroundWarning>
<View bg-$backgroundDanger>
```

#### Text Tokens
```typescript
// Trực tiếp
<Text style={{ color: Colors.$textDefault }}>

// Sử dụng modifiers
<Text $textDefault>
<Text $textPrimary>
<Text $textSuccess>
<Text $textWarning>
<Text $textDanger>
<Text $textInverted>
```

#### Icon Tokens
```typescript
// Trực tiếp
<Icon style={{ color: Colors.$iconPrimary }}>

// Sử dụng modifiers
<Icon $iconPrimary>
<Icon $iconSuccess>
<Icon $iconWarning>
<Icon $iconDanger>
```

#### Outline Tokens
```typescript
// Trực tiếp
<View style={{ borderColor: Colors.$outlinePrimary }}>

// Sử dụng modifiers
<View br-$outlinePrimary>
```

### 3. Button Tokens (Mới!)

#### Sử dụng Button Tokens
```typescript
// Nút chính
<Button bg-$buttonPrimary $textInverted>
  Primary Button
</Button>

// Nút phụ
<Button bg-$buttonSecondary $textInverted>
  Secondary Button
</Button>

// Nút thành công
<Button bg-$buttonSuccess $textInverted>
  Success Button
</Button>

// Nút cảnh báo
<Button bg-$buttonWarning $textDefault>
  Warning Button
</Button>

// Nút nguy hiểm
<Button bg-$buttonDanger $textInverted>
  Danger Button
</Button>

// Nút vô hiệu hóa
<Button bg-$buttonDisabled $textDisabled disabled={true}>
  Disabled Button
</Button>
```

#### Thay đổi màu nút
```typescript
// Trong file src/core/constants/Colors.ts

// Light mode
$buttonPrimary: '#1E3AAF',        // Màu nút chính
$buttonSecondary: '#4CAF50',      // Màu nút phụ
$buttonSuccess: '#4CAF50',        // Màu nút thành công
$buttonWarning: '#FFC50D',        // Màu nút cảnh báo
$buttonDanger: '#FC3D2F',         // Màu nút nguy hiểm

// Dark mode
$buttonPrimary: '#4633E9',        // Màu nút chính (dark)
$buttonSecondary: '#45C3A4',      // Màu nút phụ (dark)
$buttonSuccess: '#45C3A4',        // Màu nút thành công (dark)
$buttonWarning: '#FFD54E',        // Màu nút cảnh báo (dark)
$buttonDanger: '#FD7267',         // Màu nút nguy hiểm (dark)
```

### 4. Custom Colors

```typescript
// Legacy colors vẫn hoạt động
Colors.black
Colors.white
Colors.grey
Colors.blue

// Button colors
Colors.buttonPrimary
Colors.buttonSecondary
Colors.buttonBorder

// Tint color
Colors.tintColor
```

## Migration Guide

### Từ cách cũ sang mới

| Cách cũ | Cách mới (Trực tiếp) | Cách mới (Modifiers) |
|---------|---------------------|---------------------|
| `Colors.light.background` | `Colors.$backgroundDefault` | `bg-$backgroundDefault` |
| `Colors.light.text` | `Colors.$textDefault` | `$textDefault` |
| `Colors.light.primary` | `Colors.$backgroundPrimary` | `bg-$backgroundPrimary` |
| `Colors.dark.background` | `Colors.$backgroundDefault` | `bg-$backgroundDefault` |
| `Colors.dark.text` | `Colors.$textDefault` | `$textDefault` |
| `Colors.button.primary` | `Colors.$buttonPrimary` | `bg-$buttonPrimary` |
| `Colors.button.secondary` | `Colors.$buttonSecondary` | `bg-$buttonSecondary` |

### Ví dụ chuyển đổi

#### Trước:
```typescript
<View style={{ backgroundColor: Colors.light.background }}>
  <Text style={{ color: Colors.light.text }}>
    Hello World
  </Text>
  <Button style={{ backgroundColor: Colors.button.primary }}>
    Click me
  </Button>
</View>
```

#### Sau:
```typescript
// Cách 1: Sử dụng modifiers (khuyến nghị)
<View bg-$backgroundDefault>
  <Text $textDefault>
    Hello World
  </Text>
  <Button bg-$buttonPrimary $textInverted>
    Click me
  </Button>
</View>

// Cách 2: Sử dụng trực tiếp
<View style={{ backgroundColor: Colors.$backgroundDefault }}>
  <Text style={{ color: Colors.$textDefault }}>
    Hello World
  </Text>
  <Button style={{ backgroundColor: Colors.$buttonPrimary }}>
    Click me
  </Button>
</View>
```

## Dark Mode

Dark mode được hỗ trợ tự động. Khi người dùng chuyển đổi theme, các design tokens sẽ tự động map sang màu tương ứng:

```typescript
// Light mode: $backgroundDefault = '#FFFFFF'
// Dark mode: $backgroundDefault = '#000000'

<View bg-$backgroundDefault> // Tự động thích ứng
  <Text $textDefault> // Tự động thích ứng
    Content
  </Text>
  <Button bg-$buttonPrimary> // Tự động thích ứng
    Button
  </Button>
</View>
```

## Design Tokens Available

### Background Tokens
- `$backgroundDefault` - Background mặc định
- `$backgroundElevated` - Background nổi
- `$backgroundNeutral` - Background trung tính
- `$backgroundPrimary` - Background chính
- `$backgroundSuccess` - Background thành công
- `$backgroundWarning` - Background cảnh báo
- `$backgroundDanger` - Background nguy hiểm
- `$backgroundDisabled` - Background vô hiệu hóa

### Text Tokens
- `$textDefault` - Text mặc định
- `$textNeutral` - Text trung tính
- `$textPrimary` - Text chính
- `$textSuccess` - Text thành công
- `$textWarning` - Text cảnh báo
- `$textDanger` - Text nguy hiểm
- `$textDisabled` - Text vô hiệu hóa
- `$textInverted` - Text đảo ngược

### Icon Tokens
- `$iconDefault` - Icon mặc định
- `$iconNeutral` - Icon trung tính
- `$iconPrimary` - Icon chính
- `$iconSuccess` - Icon thành công
- `$iconWarning` - Icon cảnh báo
- `$iconDanger` - Icon nguy hiểm
- `$iconDisabled` - Icon vô hiệu hóa
- `$iconInverted` - Icon đảo ngược

### Outline Tokens
- `$outlineDefault` - Viền mặc định
- `$outlineNeutral` - Viền trung tính
- `$outlinePrimary` - Viền chính
- `$outlineSuccess` - Viền thành công
- `$outlineWarning` - Viền cảnh báo
- `$outlineDanger` - Viền nguy hiểm
- `$outlineDisabled` - Viền vô hiệu hóa

### Button Tokens (Mới!)
- `$buttonPrimary` - Nút chính
- `$buttonPrimaryLight` - Nút chính nhẹ
- `$buttonSecondary` - Nút phụ
- `$buttonSecondaryLight` - Nút phụ nhẹ
- `$buttonSuccess` - Nút thành công
- `$buttonWarning` - Nút cảnh báo
- `$buttonDanger` - Nút nguy hiểm
- `$buttonDisabled` - Nút vô hiệu hóa

## Thay đổi màu nút

### Cách nhanh nhất:

1. **Mở file**: `src/core/constants/Colors.ts`
2. **Tìm phần**: "Design tokens cho button"
3. **Thay đổi giá trị** màu mong muốn
4. **Lưu file** và restart app

### Ví dụ thay đổi màu nút chính:

```typescript
// Từ màu xanh dương
$buttonPrimary: '#1E3AAF',

// Thành màu tím
$buttonPrimary: '#9C27B0',
```

### Xem ví dụ đầy đủ:
- File: `src/core/constants/ButtonExamples.tsx`
- File: `src/core/constants/ButtonColorsGuide.md`

## Best Practices

1. **Ưu tiên sử dụng modifiers** thay vì style trực tiếp
2. **Sử dụng design tokens** thay vì hardcode màu
3. **Test trên cả light và dark mode**
4. **Sử dụng semantic tokens** (success, warning, danger) cho trạng thái
5. **Tận dụng inverted tokens** cho contrast tốt hơn
6. **Thay đổi màu nút** trong file Colors.ts thay vì trong từng component

## Troubleshooting

### Lỗi thường gặp

1. **Colors không load**: Đảm bảo import Colors trước khi sử dụng
2. **Dark mode không hoạt động**: Kiểm tra cấu hình `appScheme: 'default'`
3. **Modifiers không hoạt động**: Đảm bảo đã import đúng từ `react-native-ui-lib`
4. **Màu nút không thay đổi**: Restart app sau khi thay đổi Colors.ts

### Debug

```typescript
// Kiểm tra màu hiện tại
console.log('Current background:', Colors.$backgroundDefault);
console.log('Current text:', Colors.$textDefault);
console.log('Button primary:', Colors.$buttonPrimary);

// Kiểm tra theme
console.log('Is dark mode:', Colors.isDark(Colors.$backgroundDefault));
``` 