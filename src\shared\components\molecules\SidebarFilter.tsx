import React, { useState } from 'react';
import { SafeAreaView, ScrollView, Text } from 'react-native';
import {
  View,
  Checkbox,
  TouchableOpacity,
  Dividers,
} from 'react-native-ui-lib';
import { Ionicons } from '@expo/vector-icons';
import Divider from '@atoms/Divider';

const SidebarFilter = () => {
  const [selectedView, setSelectedView] = useState('Week');
  const [selectedTrainer, setSelectedTrainer] = useState<string[]>(['John']);
  const [selectedLocation, setSelectedLocation] = useState<string[]>([
    'Emmeloord',
    'Hoogeveen',
  ]);

  const toggleItem = (
    value: string,
    list: string[],
    setList: (v: string[]) => void,
  ) => {
    if (list.includes(value)) {
      setList(list.filter(item => item !== value));
    } else {
      setList([...list, value]);
    }
  };

  return (
    <SafeAreaView>
      <ScrollView contentContainerStyle={{ flexGrow: 1, padding: 16 }}>
        <View style={{ flex: 1, justifyContent: 'space-between' }}>
          <View>
            {/* Logo */}
            <Text
              style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 20 }}
            >
              CK<Text style={{ fontWeight: '300' }}>ACTIVE</Text>
            </Text>

            {/* View Switcher */}
            <View marginB-16>
              {['Day', 'Week', 'Month'].map(mode => (
                <TouchableOpacity
                  key={mode}
                  onPress={() => setSelectedView(mode)}
                  style={{
                    paddingVertical: 6,
                    paddingHorizontal: 12,
                    backgroundColor:
                      selectedView === mode ? '#E0F0FF' : 'transparent',
                    borderRadius: 6,
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginBottom: 8,
                  }}
                >
                  <Ionicons
                    name={
                      mode === 'Day'
                        ? 'menu'
                        : mode === 'Week'
                          ? 'calendar-outline'
                          : 'grid-outline'
                    }
                    size={16}
                    color='#000'
                    style={{ marginRight: 6 }}
                  />
                  <Text>{mode}</Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Trainer */}
            <Text style={{ fontWeight: '600', marginBottom: 8 }}>Trainer</Text>
            {[
              { name: 'You (John Doe)', value: 'John', color: '#8E82FC' },
              { name: 'Floyd Miles', value: 'Floyd', color: '#FF5A5F' },
              { name: 'Cody Fisher', value: 'Cody', color: '#34C759' },
            ].map(trainer => (
              <View key={trainer.value} row centerV marginB-8 gap-8>
                <Checkbox
                  style={{ borderRadius: 3 }}
                  value={selectedTrainer.includes(trainer.value)}
                  onValueChange={() =>
                    toggleItem(
                      trainer.value,
                      selectedTrainer,
                      setSelectedTrainer,
                    )
                  }
                  color={trainer.color}
                />
                <Text>{trainer.name}</Text>
              </View>
            ))}

            <Divider />

            {/* Location */}
            <Text style={{ fontWeight: '600', marginBottom: 8 }}>Location</Text>
            {['Emmeloord', 'Hoogeveen', 'Assen', 'Buren'].map(loc => (
              <View key={loc} row centerV marginB-8 gap-8>
                <Checkbox
                  value={selectedLocation.includes(loc)}
                  onValueChange={() =>
                    toggleItem(loc, selectedLocation, setSelectedLocation)
                  }
                />
                <Text>{loc}</Text>
              </View>
            ))}
            <Divider />
          </View>

          {/* Help */}
          <TouchableOpacity row centerV marginT-32 paddingR-10>
            <View gap-8 row>
              <Ionicons name='help-circle-outline' size={20} color='#888' />
              <Text marginL-8 style={{ flex: 1 }}>
                Help
              </Text>
            </View>
            <Ionicons name='chevron-forward' size={18} color='#aaa' />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default SidebarFilter;
