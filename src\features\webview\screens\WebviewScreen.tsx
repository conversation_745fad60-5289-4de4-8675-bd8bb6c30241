import { useLocalSearchParams } from 'expo-router';
import { WebView, WebViewMessageEvent } from 'react-native-webview';
import { <PERSON><PERSON>, Colors, Picker, SkeletonView } from 'react-native-ui-lib';
import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useRef, useState } from 'react';
import { View, TextInput, Modal, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { KeyboardAvoidingWrapper } from '@/shared/components/molecules/KeyboardAvoidingWrapper';

export default function WebViewPage() {
  const [inputs, setInputs] = useState<any[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<number | undefined>();
  const [newValue, setNewValue] = useState('');
  const [modalVisible, setModalVisible] = useState(false);

  const { uri, htmlContent, injectedJS } = useLocalSearchParams();
  const safeUri = Array.isArray(uri) ? uri[0] : uri;
  const safeHtml = Array.isArray(htmlContent) ? htmlContent[0] : htmlContent;
  const safeJS = Array.isArray(injectedJS) ? injectedJS[0] : injectedJS;
  const [webKey, setWebKey] = useState(0);

  const webViewRef = useRef<WebView>(null);

  const injectSearchScript = () => {
    const jsCode = `
      (function () {
        const inputs = Array.from(document.querySelectorAll('input.ms-input-item'));
        const data = inputs.map((input, index) => {
          const label = input.closest('.control-group')?.querySelector('label')?.textContent?.trim();
          return {
            index,
            label: label || '[Không label]',
            value: input.value
          };
        });
        window.ReactNativeWebView.postMessage(JSON.stringify({ type: 'input_data', payload: data }));
      })();
    `;
    webViewRef.current?.injectJavaScript(jsCode);
  };

  const handleWebViewMessage = (event: WebViewMessageEvent) => {
    try {
      const message = JSON.parse(event.nativeEvent.data);
      if (message.type === 'input_data') {
        setInputs(message.payload);
        setModalVisible(true);
      }
    } catch (e) {
      console.warn('⚠️ Dữ liệu không hợp lệ:', event.nativeEvent.data);
    }
  };

  const resetWebView = () => {
    setWebKey(prev => prev + 1);
  };

  const handleApplyChange = () => {
    if (selectedIndex != null && inputs[selectedIndex]) {
      const selectedInput = inputs[selectedIndex];
      const js = `
        (function () {
          const input = document.querySelectorAll('input.ms-input-item')[${selectedInput.index}];
          if (input) {
            input.removeAttribute('disabled');
            input.value = ${JSON.stringify(newValue)};
            input.dispatchEvent(new Event('input', { bubbles: true }));
          }
        })();
      `;
      webViewRef.current?.injectJavaScript(js);
      setModalVisible(false);
    }
  };

  const handleCloseModal = () => {
    setModalVisible(!modalVisible);
  };

  useEffect(() => {
    (webViewRef.current as any)?.clearCache?.(true);
  }, [webKey]);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View style={{ flex: 1 }}>
        <WebView
          incognito={true}
          key={webKey}
          ref={webViewRef}
          source={safeHtml ? { html: safeHtml } : { uri: safeUri }}
          onMessage={handleWebViewMessage}
          style={{ flex: 1 }}
          originWhitelist={['*']}
          startInLoadingState
          injectedJavaScript={safeJS}
          renderLoading={() => (
            <SkeletonView showContent={false} times={1} height={500} />
          )}
        />
        <Button label='📥 Lấy danh sách input' onPress={injectSearchScript} />
        {/* <Button label="Clear Cache" marginT-20 onPress={resetWebView} /> */}
        <Button
          label='🔐 Tự động đăng nhập'
          marginT-10
          onPress={() => {
            const loginScript = `
                      (function () {
                        const username = document.getElementById('Username');
                        const password = document.getElementById('Password');
                        if (username && password) {
                          username.removeAttribute('disabled');
                          username.value = '<EMAIL>'; // 👈 chỉnh sửa username tại đây
                          username.dispatchEvent(new Event('input', { bubbles: true }));
                          password.removeAttribute('disabled');
                          password.value = '12345678@Abc'; // 👈 chỉnh sửa password tại đây
                          password.dispatchEvent(new Event('input', { bubbles: true }));
                        } else {
                          console.log('❌ Không tìm thấy input Username hoặc Password');
                        }
                      })();
                    `;
            webViewRef.current?.injectJavaScript(loginScript);
          }}
        />

        <Modal visible={modalVisible} animationType='none'>
          <SafeAreaView
            style={{
              flex: 1,
              margin: 20,
              paddingTop: 20,
              backgroundColor: 'transparent',
            }}
          >
            {/* <Text style={{ fontWeight: 'bold', marginBottom: 10 }}>📝 Chọn input cần chỉnh:</Text> */}
            <Picker
              placeholder='Chọn input'
              value={selectedIndex}
              useSafeArea
              onChange={(val: any) => {
                setSelectedIndex(val);
                setNewValue(inputs[val]?.value || '');
              }}
              items={inputs.map((item, idx) => ({
                label: `${idx + 1}. ${item.label}`,
                value: idx,
              }))}
            />

            {selectedIndex != null && (
              <>
                <TextInput
                  style={{ borderWidth: 1, padding: 10, marginTop: 20 }}
                  placeholder='Nhập giá trị mới'
                  value={newValue}
                  onChangeText={setNewValue}
                  returnKeyType='done'
                  autoCapitalize='none'
                  autoCorrect={false}
                  clearButtonMode='while-editing'
                />
                <Button
                  label='✅ Ghi lại vào WebView'
                  marginT-20
                  onPress={handleApplyChange}
                />
                <Button label='Đóng' marginT-20 onPress={handleCloseModal} />
              </>
            )}
          </SafeAreaView>
        </Modal>
      </View>
    </SafeAreaView>
  );
}
