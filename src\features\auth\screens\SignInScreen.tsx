import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, Animated, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Controller, useForm } from 'react-hook-form';
import {
  View,
  Text,
  Image,
  Button,
  TextField,
  Checkbox,
  Card,
  Colors,
  TouchableOpacity,
} from 'react-native-ui-lib';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@core/theme/theme';
import { Session, useSession } from '@features/auth/context/ctx';
import { useLogin } from '@features/auth/hooks/useAuth';
import { useLoader } from '@features/auth/context/LoaderContext';
import useBiometric from '@features/auth/hooks/useBiometric';
import useStoredCredentials from '@features/auth/hooks/useStoredCredentials';
import loginIcon from '@assets/svg/logoAuth';
import { ROUTES } from '@core/constants/routes';
import { useNotification } from '@/shared/hooks/useNotification';
import { APP_INFO } from '@/core/constants/common';
import { useToast } from '@/shared/hooks/useToast';
import { SmartKeyboardView } from '@/shared/components/molecules/SmartKeyboardView';

interface LoginFormData {
  TenDangNhap: string;
  MatKhau: string;
}

const SignInScreen = () => {
  const router = useRouter();
  const { colors } = useTheme();
  const [isChecked, setChecked] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const passwordRef = useRef<any>(null);
  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<LoginFormData>();
  const { showLoader, hideLoader } = useLoader();
  const { signIn }: Session = useSession();
  const { showSuccess, showError } = useToast();
  const fadeAnim = useState(new Animated.Value(0))[0];
  const { sendNotification } = useNotification();

  // Sử dụng hooks đã được tạo
  const {
    credentials,
    hasStoredCredentials,
    saveCredentials,
    removeCredentials,
  } = useStoredCredentials();

  const {
    isBiometricAvailable,
    isBiometricEnabled,
    biometricType,
    authenticate,
  } = useBiometric();

  const loginMutation = useLogin();

  // Animation khi component mount
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();

    // Nếu đã có thông tin đăng nhập, điền vào form
    if (hasStoredCredentials && credentials) {
      setValue('TenDangNhap', credentials.username);
      setValue('MatKhau', credentials.password);
      setChecked(true);

      // Nếu có xác thực sinh trắc học và đã bật, hiện hộp thoại xác thực
      if (isBiometricAvailable && isBiometricEnabled) {
        setTimeout(() => handleBiometricLogin(), 1000);
      }
    }
  }, [hasStoredCredentials, credentials]);

  // Theo dõi kết quả đăng nhập
  useEffect(() => {
    if (loginMutation.isSuccess) {
      const handleSuccess = async () => {
        try {
          await signIn(loginMutation.data.AccessToken);

          // Lưu thông tin đăng nhập nếu "Ghi nhớ đăng nhập" được chọn
          if (isChecked) {
            handleSubmit(async data => {
              await saveCredentials(data.TenDangNhap, data.MatKhau);
            })();
          } else if (hasStoredCredentials) {
            // Nếu không chọn "Ghi nhớ" nhưng trước đó đã lưu, xóa thông tin đã lưu
            await removeCredentials();
          }
          showSuccess('Đăng nhập thành công! Chào mừng bạn trở lại.');
          router.replace(ROUTES.HOME);
        } catch (error) {
          console.error('Error setting session:', error);
          showError('Lỗi thiết lập phiên đăng nhập');
        } finally {
          hideLoader();
        }
      };

      handleSuccess();
    } else if (loginMutation.isError) {
      hideLoader();
      showError(
        'Đăng nhập không thành công. Vui lòng kiểm tra thông tin đăng nhập.',
      );
    }
  }, [loginMutation.isSuccess, loginMutation.isError, loginMutation.data]);

  // Xử lý đăng nhập
  const handleLogin = handleSubmit(async (data: LoginFormData) => {
    showLoader();
    console.log('Login data:', data);
    loginMutation.mutate({
      TenDangNhap: data.TenDangNhap,
      MatKhau: data.MatKhau,
    });
  });

  // Xử lý đăng nhập bằng sinh trắc học
  const handleBiometricLogin = async () => {
    try {
      const promptMessage =
        biometricType === 'faceid'
          ? 'Xác thực bằng Face ID để đăng nhập'
          : 'Xác thực bằng vân tay để đăng nhập';

      const success = await authenticate(promptMessage);

      if (success && credentials) {
        showLoader();
        loginMutation.mutate({
          TenDangNhap: credentials.username,
          MatKhau: credentials.password,
        });
      } else if (success) {
        showError('Không tìm thấy thông tin đăng nhập đã lưu');
      }
    } catch (error) {
      console.error('Biometric login error:', error);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Xác định icon sinh trắc học
  const getBiometricIcon = (): string => {
    return biometricType === 'faceid' ? 'scan' : 'finger-print';
  };

  const handleTestNotification = async () => {
    try {
      await sendNotification(
        'Test Notification',
        'This is a test notification',
        'basic',
      );
    } catch (error) {
      console.error('Failed to send notification:', error);
    }
  };

  return (
    <SmartKeyboardView
      enableTouchToDismiss={true}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View flex bg-bgColor>
        <LinearGradient
          // colors={['#141E74', '#2A36A8', '#3F4BD1']}
          style={styles.background}
          // start={{ x: 0, y: 0 }}
          // end={{ x: 1, y: 1 }}
          colors={['#1E3A8A', '#1E40AF', '#2563EB']}
        >
          <Animated.View style={{ flex: 1, opacity: fadeAnim }}>
            <View flex centerH paddingT-40 centerV>
              <View style={styles.logoContainer}>
                <View style={styles.logoWrapper}>
                  <Image
                    source={loginIcon}
                    style={styles.logo}
                    resizeMode='contain'
                  />
                </View>
              </View>

              <View centerV marginB-20 marginT-10 centerH>
                <Text white text40 marginB-8 style={styles.appTitle}>
                  {APP_INFO.NAME}
                </Text>
                <Text white text70 style={styles.appSlogan}>
                  {APP_INFO.SLOGAN}
                </Text>
              </View>

              <Card style={styles.formCard} padding-25>
                <Controller
                  name='TenDangNhap'
                  control={control}
                  defaultValue='dhdao'
                  rules={{ required: true }}
                  render={({ field: { onChange, value } }) => (
                    <TextField
                      floatingPlaceholder
                      placeholder='Tên đăng nhập'
                      floatingPlaceholderColor={colors.primary}
                      value={value}
                      onChangeText={onChange}
                      fieldStyle={styles.fieldStyle}
                      containerStyle={styles.inputContainer}
                      leadingAccessory={
                        <View paddingR-12>
                          <Ionicons
                            name='person-outline'
                            size={22}
                            color={colors.primary}
                          />
                        </View>
                      }
                      enableErrors={!!errors.TenDangNhap}
                      validationMessage={
                        errors.TenDangNhap && 'Vui lòng nhập tên đăng nhập'
                      }
                      validationMessageStyle={styles.errorMessage}
                      marginB-15
                      // Keyboard optimization
                      returnKeyType='next'
                      onSubmitEditing={() => passwordRef.current?.focus()}
                      blurOnSubmit={false}
                      autoCapitalize='none'
                      autoCorrect={false}
                    />
                  )}
                />

                <Controller
                  name='MatKhau'
                  control={control}
                  defaultValue='@Abc@123'
                  rules={{ required: true }}
                  render={({ field: { onChange, value } }) => (
                    <TextField
                      ref={passwordRef}
                      floatingPlaceholder
                      placeholder='Mật khẩu'
                      floatingPlaceholderColor={colors.primary}
                      secureTextEntry={!showPassword}
                      value={value}
                      onChangeText={onChange}
                      fieldStyle={styles.fieldStyle}
                      containerStyle={styles.inputContainer}
                      leadingAccessory={
                        <View paddingR-12>
                          <Ionicons
                            name='lock-closed-outline'
                            size={22}
                            color={colors.primary}
                          />
                        </View>
                      }
                      trailingAccessory={
                        <TouchableOpacity
                          onPress={togglePasswordVisibility}
                          style={{ padding: 8 }}
                        >
                          <Ionicons
                            name={
                              showPassword ? 'eye-off-outline' : 'eye-outline'
                            }
                            size={22}
                            color={colors.primary}
                          />
                        </TouchableOpacity>
                      }
                      enableErrors={!!errors.MatKhau}
                      validationMessage={
                        errors.MatKhau && 'Vui lòng nhập mật khẩu'
                      }
                      validationMessageStyle={styles.errorMessage}
                      marginB-15
                      // Keyboard optimization
                      returnKeyType='done'
                      onSubmitEditing={handleLogin}
                      autoCapitalize='none'
                      autoCorrect={false}
                    />
                  )}
                />

                <View row spread marginB-25>
                  <View row centerV>
                    <Checkbox
                      value={isChecked}
                      onValueChange={setChecked}
                      color={colors.primary}
                      size={20}
                    />
                    <Text marginL-10 style={styles.rememberText}>
                      Ghi nhớ
                    </Text>
                  </View>

                  <Button
                    link
                    label='Quên mật khẩu?'
                    color={colors.primary}
                    labelStyle={styles.forgotText}
                  />
                </View>

                <View row center>
                  <Button
                    iconOnRight
                    iconSource={() => (
                      <Ionicons
                        name='log-in-outline'
                        size={22}
                        style={{ marginTop: 3.5, marginLeft: 2 }}
                        color={Colors.white}
                      />
                    )}
                    label={
                      loginMutation.isPending
                        ? 'Đang đăng nhập...'
                        : 'Đăng nhập'
                    }
                    backgroundColor={colors.primary}
                    disabled={loginMutation.isPending}
                    borderRadius={10}
                    style={styles.loginButton}
                    labelStyle={styles.loginButtonLabel}
                    onPress={handleLogin}
                    flex-1
                  />

                  {isBiometricAvailable &&
                    isBiometricEnabled &&
                    hasStoredCredentials && (
                      <TouchableOpacity
                        style={styles.biometricIconButton}
                        onPress={handleBiometricLogin}
                        disabled={loginMutation.isPending}
                      >
                        <View style={styles.biometricIconContainer}>
                          <Ionicons
                            name={getBiometricIcon() as any}
                            size={26}
                            color={colors.primary}
                          />
                        </View>
                      </TouchableOpacity>
                    )}
                </View>

                <View row center marginT-25>
                  <Text marginR-5 style={styles.registerText}>
                    Chưa có tài khoản?
                  </Text>
                  <Button
                    onPress={() => router.push(ROUTES.AUTH.SIGN_UP)}
                    link
                    label='Đăng ký ngay'
                    color={colors.primary}
                    labelStyle={styles.registerButton}
                  />
                </View>
              </Card>
            </View>
          </Animated.View>
        </LinearGradient>
      </View>
    </SmartKeyboardView>
  );
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  logoContainer: {
    marginBottom: 20,
  },
  logoWrapper: {},
  logo: {
    width: 80,
    height: 80,
  },
  appTitle: {
    fontWeight: 'bold',
    fontSize: 30,
    letterSpacing: 0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.4)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  appSlogan: {
    opacity: 0.95,
    letterSpacing: 0.5,
  },
  loginTitle: {
    fontWeight: '600',
    fontSize: 24,
    textAlign: 'center',
    color: Colors.grey10,
  },
  formCard: {
    width: '90%',
    maxWidth: 420,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 14,
    elevation: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
  },
  fieldStyle: {
    borderBottomWidth: 1.5,
    borderColor: Colors.grey60,
    paddingBottom: 8,
  },
  inputContainer: {
    marginBottom: 10,
  },
  loginButton: {
    height: 56,
    shadowColor: '#1E3AAF',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.35,
    shadowRadius: 10,
    elevation: 6,
  },
  biometricIconButton: {
    marginLeft: 12,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
  },
  biometricIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#1E3AAF',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 4,
    borderWidth: 1,
    borderColor: Colors.rgba(Colors.grey60, 0.3),
  },
  loginButtonLabel: {
    fontWeight: '600',
    fontSize: 16,
  },
  rememberText: {
    fontSize: 14,
    color: Colors.grey30,
  },
  forgotText: {
    fontSize: 14,
    fontWeight: '500',
  },
  registerText: {
    fontSize: 14,
    color: Colors.grey30,
  },
  registerButton: {
    fontSize: 14,
    fontWeight: '600',
  },
  errorMessage: {
    color: Colors.red30,
    fontSize: 12,
  },
  testButton: {
    backgroundColor: Colors.yellow5,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
});

export default SignInScreen;
