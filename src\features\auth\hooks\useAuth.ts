import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { authKeys } from '@core/api/keys/authKeys';
import { useRouter } from 'expo-router';
import { ROUTES } from '@core/constants/routes';
import { authService, UserInfo } from '../services/authService';

export const useLogin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: authService.login,
    onSuccess: data => {
      queryClient.setQueryData(authKeys.profile(), data.userInfo);
      queryClient.invalidateQueries({ queryKey: authKeys.profile() });
    },
    onError: (error: Error) => {
      console.error('❌ Lỗi đăng nhập:', error);
    },
  });
};

export const useLogout = () => {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationFn: authService.logout,
    onSuccess: () => {
      // Clear related queries from cache
      queryClient.removeQueries({ queryKey: authKeys.all });
      router.replace(ROUTES.AUTH.SIGN_IN);
    },
    onError: (error: Error) => {
      console.error('❌ Lỗi đăng xuất:', error);
    },
  });
};

export const useProfile = () => {
  return useQuery<UserInfo | null>({
    queryKey: authKeys.profile(),
    queryFn: authService.getProfile,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 1,
  });
};
