{"name": "expo-base", "version": "1.6.0", "main": "index.js", "private": true, "scripts": {"start": "expo start", "start:dv": "npx expo run:ios --device", "start -c": "expo start -c", "prebuild": "expo prebuild", "android": "expo run:android", "android:release": "expo run:android --variant Release", "android:dev-build": "eas build --profile development --platform android", "android:dev-build:local": "eas build --profile development --platform android --local", "android:preview": "eas build --profile preview --platform android", "ios": "expo run:ios", "ios:release": "expo run:ios --variant Release", "ios:dev-build": "eas build --profile development --platform ios", "ios:dev-build:local": "eas build --profile development --platform ios --local", "ios:preview": "eas build --profile preview --platform ios", "web": "expo start --web", "typecheck": "tsc", "lint": "eslint ./src ./e2e --fix", "test": "jest --watchAll", "e2e:ios:build": "detox build -c ios.release", "e2e:ios:test": "detox test -c ios.release", "e2e:ios:debug:build": "detox build -c ios.debug", "e2e:ios:debug:test": "detox test -c ios.debug", "e2e:android:build": "detox build -c android.release", "e2e:android:test": "detox test -c android.release", "e2e:android:debug:build": "detox build -c android.debug", "e2e:android:debug:test": "detox test -c android.debug", "storybook-generate": "sb-rn-get-stories", "migrate-colors": "node scripts/migrate-colors.js"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@gorhom/bottom-sheet": "^5.0.6", "@hookform/resolvers": "^5.0.1", "@lottiefiles/dotlottie-react": "^0.14.2", "@react-native-community/blur": "^4.4.1", "@react-native-community/cli": "latest", "@react-native-community/netinfo": "11.4.1", "@reduxjs/toolkit": "^2.5.0", "@shopify/flash-list": "1.7.3", "@tanstack/react-form": "^1.5.0", "@tanstack/react-query": "^5.80.7", "@testing-library/jest-native": "^5.4.2", "@testing-library/react-native": "^12.4.3", "axios": "^1.7.9", "babel-plugin-module-resolver": "^5.0.2", "dayjs": "^1.11.13", "expo": "~52.0.46", "expo-auth-session": "~6.0.3", "expo-build-properties": "~0.13.3", "expo-camera": "~16.0.18", "expo-checkbox": "~4.0.1", "expo-crypto": "~14.0.2", "expo-dev-client": "~5.0.20", "expo-device": "~7.0.3", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image": "~2.0.7", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-local-authentication": "~15.0.2", "expo-localization": "~16.0.1", "expo-media-library": "~17.0.6", "expo-notifications": "~0.29.14", "expo-router": "~4.0.21", "expo-secure-store": "~14.0.1", "expo-sharing": "~13.0.1", "expo-splash-screen": "~0.29.24", "expo-sqlite": "~15.1.4", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.9", "expo-updates": "~0.27.4", "expo-web-browser": "~14.0.2", "i18next": "^24.2.0", "lottie-react-native": "7.1.0", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.54.2", "react-i18next": "^15.2.0", "react-native": "0.76.9", "react-native-big-calendar": "^4.17.1", "react-native-bottom-tabs": "^0.7.8", "react-native-calendars": "^1.1311.0", "react-native-curved-bottom-bar": "^3.5.1", "react-native-dialog": "^9.3.0", "react-native-document-scanner-plugin": "^1.0.1", "react-native-dotenv": "^3.4.11", "react-native-draggable-flatlist": "^4.0.3", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-gifted-charts": "^1.4.61", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "^3.2.0", "react-native-pager-view": "6.5.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-share": "^12.0.9", "react-native-shimmer-placeholder": "^2.0.9", "react-native-size-matters": "^0.4.2", "react-native-svg": "15.8.0", "react-native-tab-view": "^4.0.12", "react-native-tcp-socket": "^6.3.0", "react-native-toast-message": "^2.3.0", "react-native-ui-lib": "^7.43.0", "react-native-uuid": "^2.0.3", "react-native-vision-camera": "^4.7.0", "react-native-web": "~0.19.13", "react-native-websocket": "^1.0.2", "react-native-webview": "13.12.5", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "socket.io-client": "^4.8.1", "uuid": "^11.1.0", "zod": "^3.24.1"}, "devDependencies": {"@babel/core": "^7.24.0", "@commitlint/config-conventional": "^18.4.3", "@config-plugins/detox": "^7.0.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-community/slider": "4.5.5", "@storybook/addon-ondevice-actions": "^8.6.2", "@storybook/addon-ondevice-controls": "^8.6.2", "@storybook/react-native": "^8.6.2", "@types/jest": "^29.5.11", "@types/react": "~18.3.12", "@types/react-redux": "^7.1.34", "babel-loader": "^8.4.1", "commitlint": "^18.4.3", "detox": "^20.20.3", "detox-expo-helpers": "^0.6.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-config-universe": "^12.0.0", "eslint-plugin-detox": "^1.0.0", "eslint-plugin-prettier": "^5.0.1", "glob": "^10.3.10", "husky": "^8.0.3", "jest": "^29.7.0", "jest-expo": "~52.0.2", "lint-staged": "^15.1.0", "prettier": "^3.1.0", "react-test-renderer": "18.2.0", "sharp-cli": "^4.2.0", "typescript": "~5.3.3"}, "eslintConfig": {"extends": "universe/native"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "yarn lint"]}, "react-native": {"net": "react-native-tcp-socket"}}