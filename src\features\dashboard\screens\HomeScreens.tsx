import React from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  Image,
  Animated,
  TextInput,
  Alert,
  ToastAndroid,
  Platform,
  Dimensions,
  View as RNView,
} from 'react-native';
import { View, Text, Toast } from 'react-native-ui-lib';
import {
  MaterialIcons,
  AntDesign,
  Ionicons,
  Feather,
  FontAwesome5,
} from '@expo/vector-icons';
import TokenWidget from '@features/tokens/TokenWidget';
import { useRouter } from 'expo-router';
import { useProfile, useLogout } from '@features/auth/hooks/useAuth';
import { ROUTES } from '@core/constants/routes';
import {
  AnimatedView,
  ScaleView,
  AnimatedText,
} from '@/shared/animations/components';
import { useFadeIn, useScale, useSlideY } from '@/shared/animations';
import { useTheme } from '@/core/theme/theme';
// Quick action interface
interface QuickActionItem {
  id: string;
  title: string;
  icon: string;
  color: string;
  onPress: () => void;
}

// Storage data interface
interface StorageItem {
  type: string;
  color: string;
  percentage: number;
  size: string;
}

// Function to show a development toast message
const showDevelopmentMessage = () => {
  if (Platform.OS === 'android') {
    ToastAndroid.show('Tính năng đang phát triển', ToastAndroid.SHORT);
  } else {
    Alert.alert('Thông báo', 'Tính năng đang được phát triển');
  }
};

const HomeScreens = () => {
  const router = useRouter();
  const { colors } = useTheme();
  const {
    scale,
    width: screenWidth,
    height: screenHeight,
    fontScale,
  } = Dimensions.get('window');
  const { data: userProfile, isLoading: isLoadingProfile } = useProfile();

  // Set up animations
  const headerOpacity = useFadeIn(600);
  const headerScale = useScale(600, 0, 0.97);
  const cardSlide = useSlideY(700, 200, 20);
  const cardOpacity = useFadeIn(700, 200);
  const contentSlide = useSlideY(700, 400, 30);
  const contentOpacity = useFadeIn(700, 400);
  const actionButtonsScale = useScale(800, 600, 0.5);

  // Mock storage data
  const storageData: StorageItem[] = [
    { type: 'PDF', color: '#FF6B6B', percentage: 45, size: '2.25 GB' },
    { type: 'Ảnh', color: '#4F6CFF', percentage: 30, size: '1.5 GB' },
    { type: 'Word', color: '#38D9A9', percentage: 15, size: '750 MB' },
    { type: 'Khác', color: '#9775FA', percentage: 10, size: '500 MB' },
  ];

  // Total storage
  const totalStorage = '5 GB';
  const usedStorage = '4 GB';
  const freeStorage = '1 GB';
  const usagePercentage = 80;

  // Quick action buttons data
  const quickActions = [
    {
      id: 'scan',
      title: 'Quét mới',
      icon: 'scan-outline',
      color: '#4F6CFF',
      onPress: () => router.push('/scan'),
    },
    {
      id: 'qr',
      title: 'QR',
      icon: 'qr-code-outline',
      color: '#FFA500',
      onPress: () => router.push('/(dashboard)/qr-scanner'),
    },
    {
      id: 'share',
      title: 'Chia sẻ',
      icon: 'share-social',
      color: '#38D9A9',
      onPress: () => {
        showDevelopmentMessage();
      },
      // onPress: () => {
      //   router.push('/(dashboard)/socket');
      // },
    },
    {
      id: 'settings',
      title: 'Cài đặt',
      icon: 'settings',
      color: '#9775FA',
      onPress: () => router.push(ROUTES.SETTINGS),
    },
  ];

  // Render a quick action button
  const renderQuickAction = (item: QuickActionItem) => {
    return (
      <TouchableOpacity
        key={item.id}
        style={styles.quickActionButton}
        onPress={item.onPress}
        activeOpacity={0.7}
      >
        <View
          style={[
            styles.quickActionIcon,
            { backgroundColor: item.color + '20' },
          ]}
        >
          <Ionicons name={item.icon as any} size={24} color={item.color} />
        </View>
        <Text style={styles.quickActionLabel}>{item.title}</Text>
      </TouchableOpacity>
    );
  };

  // Render storage bar
  const renderStorageBar = () => {
    let currentWidth = 0;

    return (
      <View style={styles.storageBarContainer}>
        {storageData.map((item, index) => {
          const barWidth = (item.percentage / 100) * (screenWidth - 48);
          currentWidth += barWidth;

          return (
            <View
              key={index}
              style={[
                styles.storageBarSegment,
                {
                  backgroundColor: item.color,
                  width: `${item.percentage}%`,
                },
              ]}
            />
          );
        })}
      </View>
    );
  };

  // Render storage legend item
  const renderStorageLegendItem = (item: StorageItem) => {
    return (
      <View key={item.type} style={styles.legendItem}>
        <View style={[styles.legendColor, { backgroundColor: item.color }]} />
        <View style={styles.legendTextContainer}>
          <Text style={styles.legendType}>{item.type}</Text>
          <Text style={styles.legendPercentage}>
            {item.percentage}% ({item.size})
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View style={{ flex: 1, position: 'relative' }}>
      <RNView style={StyleSheet.absoluteFill}>
        <RNView style={{ flex: 1, backgroundColor: colors.primary }} />
        <RNView style={{ flex: 5, backgroundColor: colors.surface }} />
      </RNView>

      <View style={styles.container}>
        {/* Header */}
        <Animated.View
          style={[
            styles.header,
            {
              opacity: headerOpacity,
              transform: [{ scale: headerScale }],
            },
          ]}
        >
          <View gap-s1>
            <AnimatedText style={styles.hello as any} delay={100}>
              Xin chào: {userProfile?.TenDangNhap},
            </AnimatedText>
            <AnimatedText style={styles.subtitle as any} delay={200}>
              Sẵn sàng quét tài liệu ?
            </AnimatedText>
          </View>
          <View style={styles.headerRight}>
            <ScaleView
              duration={700}
              delay={300}
              style={styles.tokenWidgetContainer}
            >
              <TokenWidget size='small' />
            </ScaleView>
            <ScaleView duration={700} delay={400}>
              <TouchableOpacity>
                <Image
                  source={{ uri: 'https://i.pravatar.cc/300' }}
                  style={styles.avatar}
                />
              </TouchableOpacity>
            </ScaleView>
          </View>
        </Animated.View>

        {/* SelectedProjectDocInfo component */}

        {/* Summary Card */}
        <Animated.View
          style={[
            styles.contentCard,
            {
              opacity: cardOpacity,
              transform: [{ translateY: cardSlide }],
            },
          ]}
        >
          <View style={styles.summaryRow}>
            <AnimatedView style={styles.summaryItem} duration={600} delay={300}>
              <MaterialIcons
                name='folder-open'
                size={24}
                color={colors.primary}
              />
              <Text style={styles.summaryTitle}>120 Tài liệu</Text>
              <Text style={styles.summaryLabel}>Đã quét</Text>
            </AnimatedView>
            <AnimatedView style={styles.summaryItem} duration={600} delay={400}>
              <MaterialIcons name='drafts' size={24} color={colors.primary} />
              <Text style={styles.summaryTitle}>34 Bản nháp</Text>
              <Text style={styles.summaryLabel}>Chưa sắp xếp</Text>
            </AnimatedView>
            <AnimatedView style={styles.summaryItem} duration={600} delay={500}>
              <AntDesign name='export' size={24} color={colors.primary} />
              <Text style={styles.summaryTitle}>86 Đã gửi</Text>
              <Text style={styles.summaryLabel}>Đã chia sẻ</Text>
            </AnimatedView>
          </View>
        </Animated.View>

        {/* Storage Usage Card */}
        <Animated.View
          style={[
            styles.contentCard,
            styles.storageCard,
            {
              opacity: cardOpacity,
              transform: [{ translateY: cardSlide }],
            },
          ]}
        >
          <View style={styles.storageHeader}>
            <View>
              <Text style={styles.storageTitle}>Dung lượng lưu trữ</Text>
              <Text style={styles.storageSubtitle}>
                Đã dùng {usedStorage} / {totalStorage}
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => router.push(ROUTES.STORAGE_MANAGER)}
            >
              <Text style={styles.manageText}>Quản lý</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.storageVisuals}>
            {/* Circular Progress */}
            <View style={styles.circularContainer}>
              <View style={styles.progressCircle}>
                <View style={styles.progressBackground} />
                <View
                  style={[
                    styles.progressFill,
                    {
                      width: `${usagePercentage}%`,
                      backgroundColor:
                        usagePercentage > 80 ? '#FF6B6B' : '#2F4FCD',
                    },
                  ]}
                />
                <View style={styles.progressContent}>
                  <Text style={styles.progressPercentage}>
                    {usagePercentage}%
                  </Text>
                  <Text style={styles.progressLabel}>Đã dùng</Text>
                </View>
              </View>

              <View style={styles.storageInfo}>
                <View style={styles.storageInfoItem}>
                  <Text style={styles.storageInfoValue}>{usedStorage}</Text>
                  <Text style={styles.storageInfoLabel}>Đã dùng</Text>
                </View>
                <View style={styles.storageInfoDivider} />
                <View style={styles.storageInfoItem}>
                  <Text style={styles.storageInfoValue}>{freeStorage}</Text>
                  <Text style={styles.storageInfoLabel}>Còn trống</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Storage Types */}
          <View style={styles.storageTypesContainer}>
            <Text style={styles.storageTypesTitle}>Phân loại lưu trữ</Text>
            <View style={styles.storageBarContainer}>
              {storageData.map((item, index) => (
                <View
                  key={index}
                  style={[
                    styles.storageBarSegment,
                    {
                      backgroundColor: item.color,
                      width: `${item.percentage}%`,
                    },
                  ]}
                />
              ))}
            </View>

            <View style={styles.legendContainer}>
              {storageData.map(renderStorageLegendItem)}
            </View>
          </View>
        </Animated.View>
        {/* Quick Actions */}
        <Animated.View
          style={[
            styles.quickActionsContainer,
            {
              opacity: cardOpacity,
              transform: [{ translateY: cardSlide }],
            },
          ]}
        >
          <View style={styles.quickActionsHeader}>
            <Text style={styles.sectionTitle}>Chức năng nhanh</Text>
          </View>
          <View style={styles.quickActionsRow}>
            {quickActions.map(renderQuickAction)}
          </View>
        </Animated.View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: '13%',
  },
  contentCard: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 16,
    marginHorizontal: '6%',
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 5,
  },
  storageCard: {
    marginTop: 16,
  },
  storageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  storageTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  storageSubtitle: {
    fontSize: 13,
    color: '#666',
    marginTop: 2,
  },
  manageText: {
    color: '#2F4FCD',
    fontSize: 13,
    fontWeight: '500',
  },
  storageVisuals: {
    marginBottom: 20,
  },
  circularContainer: {
    alignItems: 'center',
  },
  progressCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    marginBottom: 16,
    position: 'relative',
    overflow: 'hidden',
  },
  progressBackground: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: '#f0f0f0',
  },
  progressFill: {
    position: 'absolute',
    left: 0,
    top: 0,
    height: '100%',
    backgroundColor: '#2F4FCD',
  },
  progressContent: {
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  progressPercentage: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  progressLabel: {
    fontSize: 12,
    color: 'white',
  },
  storageInfo: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  storageInfoItem: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  storageInfoValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  storageInfoLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  storageInfoDivider: {
    height: 30,
    width: 1,
    backgroundColor: '#eee',
  },
  storageTypesContainer: {
    marginTop: 10,
  },
  storageTypesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
  },
  storageBarContainer: {
    height: 12,
    backgroundColor: '#f0f0f0',
    borderRadius: 6,
    overflow: 'hidden',
    flexDirection: 'row',
    marginBottom: 12,
  },
  storageBarSegment: {
    height: '100%',
  },
  legendContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
    marginVertical: 4,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendTextContainer: {
    flex: 1,
  },
  legendType: {
    fontSize: 13,
    color: '#333',
    fontWeight: '500',
  },
  legendPercentage: {
    fontSize: 11,
    color: '#666',
  },
  header: {
    paddingHorizontal: '6%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tokenWidgetContainer: {
    marginRight: 10,
  },
  hello: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
  subtitle: {
    fontSize: 12,
    color: '#fff',
    opacity: 0.9,
  },
  avatar: {
    width: 46,
    height: 46,
    borderRadius: 23,
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.5)',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryItem: {
    alignItems: 'center',
    flex: 1,
    paddingVertical: 8,
  },
  summaryTitle: {
    fontWeight: '700',
    fontSize: 15,
    marginTop: 8,
    color: '#333',
  },
  summaryLabel: {
    fontSize: 12,
    color: '#888',
    marginTop: 2,
  },
  quickActionsContainer: {
    marginTop: 16,
    marginBottom: 16,
    paddingHorizontal: '6%',
  },
  quickActionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  quickActionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  quickActionButton: {
    alignItems: 'center',
    width: '22%',
    marginBottom: 12,
  },
  quickActionIcon: {
    width: 50,
    height: 50,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  quickActionLabel: {
    fontSize: 12,
    color: '#333',
    textAlign: 'center',
  },
});

export default HomeScreens;
