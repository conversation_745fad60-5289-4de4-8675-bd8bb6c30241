import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  ScrollView,
  View,
  Text,
  TouchableOpacity,
  Switch,
  Image,
  Animated,
  ActivityIndicator,
} from 'react-native';
import FeatherIcon from '@expo/vector-icons/Feather';
import { ActionSheet } from 'react-native-ui-lib';
import { Link, useRouter } from 'expo-router';
import { ROUTES } from '@core/constants/routes';
import { useProfile, useLogout } from '@features/auth/hooks/useAuth';
import useBiometric from '@features/auth/hooks/useBiometric';
import useStoredCredentials from '@features/auth/hooks/useStoredCredentials';
import { Colors } from 'react-native-ui-lib';
import { SafeAreaView } from 'react-native-safe-area-context';

const ProfileScreen = () => {
  const [form, setForm] = useState({
    emailNotifications: true,
    pushNotifications: false,
  });
  const [showActionSheetLogout, setShowActionSheetLogout] = useState(false);
  const router = useRouter();
  const [cardOpacity, setCardOpacity] = useState(new Animated.Value(0));
  const [cardSlide, setCardSlide] = useState(new Animated.Value(0));
  const { data: userProfile, isLoading: isLoadingProfile } = useProfile();
  // userData is the profile object or empty object if not loaded

  const logoutMutation = useLogout();

  // Get biometric state
  const {
    isBiometricAvailable,
    isBiometricEnabled,
    biometricType,
    toggleBiometric,
  } = useBiometric();

  // Get stored credentials state
  const { hasStoredCredentials } = useStoredCredentials();

  // Animation for profile info
  useEffect(() => {
    if (userProfile) {
      Animated.parallel([
        Animated.timing(cardOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(cardSlide, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [userProfile]);

  // Function to get biometric type name
  const getBiometricName = () => {
    return biometricType === 'faceid' ? 'Face ID' : 'Vân tay';
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8f8f8' }}>
      <ScrollView contentContainerStyle={styles.content}>
        <View style={[styles.section, { paddingTop: 4 }]}>
          <Text style={styles.sectionTitle}>Tài khoản</Text>
          <View style={styles.sectionBody}>
            {isLoadingProfile ? (
              <View style={[styles.profile, { justifyContent: 'center' }]}>
                <ActivityIndicator size='small' color={Colors.buttonPrimary} />
                <Text style={{ marginLeft: 12, color: '#666' }}>
                  Đang tải thông tin người dùng...
                </Text>
              </View>
            ) : (
              <TouchableOpacity
                onPress={() => router.push(ROUTES.USER_PROFILE)}
                style={styles.profile}
              >
                <Image
                  alt=''
                  source={{ uri: 'https://i.pravatar.cc/300' }}
                  style={styles.profileAvatar}
                />
                <View style={styles.profileBody}>
                  <Text style={styles.profileName}>
                    {userProfile?.TenDangNhap || 'Chưa đăng nhập'}
                  </Text>
                  {userProfile?.TenDonVi && (
                    <Text style={styles.profileHandle}>
                      {userProfile.TenDonVi}
                    </Text>
                  )}
                </View>
                <FeatherIcon color='#bcbcbc' name='chevron-right' size={22} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Cài đặt ứng dụng</Text>
          <View style={styles.sectionBody}>
            <View style={[styles.rowWrapper, styles.rowFirst]}>
              <TouchableOpacity
                onPress={() => router.push(ROUTES.PROJECT_MANAGER)}
                style={styles.row}
              >
                <Text style={styles.rowLabel}>Quản lý dự án</Text>
                <View style={styles.rowSpacer} />
                <FeatherIcon
                  color='#4285F4'
                  name='briefcase'
                  size={19}
                  style={{ marginRight: 8 }}
                />
                <FeatherIcon color='#bcbcbc' name='chevron-right' size={19} />
              </TouchableOpacity>
            </View>

            <View style={styles.rowWrapper}>
              <TouchableOpacity
                onPress={() => router.push(ROUTES.DOCUMENT_TYPE)}
                style={styles.row}
              >
                <Text style={styles.rowLabel}>Loại tài liệu</Text>
                <View style={styles.rowSpacer} />
                <FeatherIcon
                  color='#34A853'
                  name='file-text'
                  size={19}
                  style={{ marginRight: 8 }}
                />
                <FeatherIcon color='#bcbcbc' name='chevron-right' size={19} />
              </TouchableOpacity>
            </View>
            <View style={styles.rowWrapper}>
              <TouchableOpacity
                onPress={() => router.push('/settings/ai-models')}
                style={styles.row}
              >
                <Text style={styles.rowLabel}>Mô hình AI</Text>
                <View style={styles.rowSpacer} />
                <FeatherIcon
                  color='#34A853'
                  name='zap'
                  size={19}
                  style={{ marginRight: 8 }}
                />
                <FeatherIcon color='#bcbcbc' name='chevron-right' size={19} />
              </TouchableOpacity>
            </View>

            <View style={styles.rowWrapper}>
              <TouchableOpacity
                onPress={() => router.push(ROUTES.NOTIFICATIONS)}
                style={styles.row}
              >
                <Text style={styles.rowLabel}>Thông báo</Text>
                <View style={styles.rowSpacer} />
                <FeatherIcon
                  color='#FF6B00'
                  name='bell'
                  size={19}
                  style={{ marginRight: 8 }}
                />
                <FeatherIcon color='#bcbcbc' name='chevron-right' size={19} />
              </TouchableOpacity>
            </View>

            <View style={[styles.rowWrapper, styles.rowLast]}>
              <TouchableOpacity
                onPress={() => router.push(ROUTES.SETTINGS)}
                style={styles.row}
              >
                <Text style={styles.rowLabel}>Tất cả cài đặt</Text>
                <View style={styles.rowSpacer} />
                <FeatherIcon
                  color='#8E44AD'
                  name='settings'
                  size={19}
                  style={{ marginRight: 8 }}
                />
                <FeatherIcon color='#bcbcbc' name='chevron-right' size={19} />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tuỳ chỉnh</Text>
          <View style={styles.sectionBody}>
            <View style={[styles.rowWrapper, styles.rowFirst]}>
              <TouchableOpacity onPress={() => { }} style={styles.row}>
                <Text style={styles.rowLabel}>Ngôn ngữ</Text>

                <View style={styles.rowSpacer} />

                <Text style={styles.rowValue}>Tiếng Việt</Text>

                <FeatherIcon color='#bcbcbc' name='chevron-right' size={19} />
              </TouchableOpacity>
            </View>
            <View style={styles.rowWrapper}>
              <TouchableOpacity onPress={() => { }} style={styles.row}>
                <Text style={styles.rowLabel}>Vị trí</Text>

                <View style={styles.rowSpacer} />

                <Text style={styles.rowValue}>Hồ Chí Minh</Text>

                <FeatherIcon color='#bcbcbc' name='chevron-right' size={19} />
              </TouchableOpacity>
            </View>

            <View style={styles.rowWrapper}>
              <View style={styles.row}>
                <Text style={styles.rowLabel}>Email thông báo</Text>

                <View style={styles.rowSpacer} />

                <Switch
                  onValueChange={emailNotifications =>
                    setForm({ ...form, emailNotifications })
                  }
                  style={{ transform: [{ scaleX: 0.95 }, { scaleY: 0.95 }] }}
                  value={form.emailNotifications}
                />
              </View>
            </View>

            <View style={styles.rowWrapper}>
              <View style={styles.row}>
                <Text style={styles.rowLabel}>Thông báo đẩy</Text>
                <View style={styles.rowSpacer} />
                <Switch
                  onValueChange={pushNotifications =>
                    setForm({ ...form, pushNotifications })
                  }
                  style={{ transform: [{ scaleX: 0.95 }, { scaleY: 0.95 }] }}
                  value={form.pushNotifications}
                />
              </View>
            </View>

            {/* Thêm tùy chọn xác thực sinh trắc học */}
            {isBiometricAvailable && hasStoredCredentials && (
              <View style={[styles.rowWrapper, styles.rowLast]}>
                <View style={styles.row}>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text style={styles.rowLabel}>
                      Đăng nhập bằng {getBiometricName()}
                    </Text>
                    {isBiometricEnabled && (
                      <View style={styles.badgeContainer}>
                        <Text style={styles.badgeText}>Đã bật</Text>
                      </View>
                    )}
                  </View>
                  <View style={styles.rowSpacer} />
                  <Switch
                    onValueChange={toggleBiometric}
                    style={{ transform: [{ scaleX: 0.95 }, { scaleY: 0.95 }] }}
                    value={isBiometricEnabled}
                  />
                </View>
              </View>
            )}

            {!hasStoredCredentials && isBiometricAvailable && (
              <View style={[styles.rowWrapper, styles.rowLast]}>
                <View style={styles.row}>
                  <View>
                    <Text style={styles.rowLabel}>
                      Đăng nhập bằng {getBiometricName()}
                    </Text>
                    <Text style={styles.rowHint}>
                      Bật "Ghi nhớ đăng nhập" để sử dụng
                    </Text>
                  </View>
                  <View style={styles.rowSpacer} />
                  <Switch
                    disabled={true}
                    style={{
                      transform: [{ scaleX: 0.95 }, { scaleY: 0.95 }],
                      opacity: 0.5,
                    }}
                    value={false}
                  />
                </View>
              </View>
            )}

            {!isBiometricAvailable && (
              <View style={[styles.rowWrapper, styles.rowLast]}>
                <View style={styles.row}>
                  <View>
                    <Text style={styles.rowLabel}>Đăng nhập sinh trắc học</Text>
                    <Text style={styles.rowHint}>
                      Thiết bị của bạn không hỗ trợ Face ID hoặc vân tay
                    </Text>
                  </View>
                  <View style={styles.rowSpacer} />
                  <Switch
                    disabled={true}
                    style={{
                      transform: [{ scaleX: 0.95 }, { scaleY: 0.95 }],
                      opacity: 0.5,
                    }}
                    value={false}
                  />
                </View>
              </View>
            )}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tài nguyên</Text>
          <View style={styles.sectionBody}>
            <View style={[styles.rowWrapper, styles.rowFirst]}>
              <TouchableOpacity onPress={() => { }} style={styles.row}>
                <Text style={styles.rowLabel}>Liên hệ hỗ trợ</Text>
                <View style={styles.rowSpacer} />
                <FeatherIcon color='#bcbcbc' name='chevron-right' size={19} />
              </TouchableOpacity>
            </View>

            <View style={styles.rowWrapper}>
              <TouchableOpacity
                onPress={() => router.push(ROUTES.STORAGE_MANAGER)}
                style={styles.row}
              >
                <Text style={styles.rowLabel}>Quản lý dung lượng</Text>
                <View style={styles.rowSpacer} />
                <FeatherIcon color='#bcbcbc' name='chevron-right' size={19} />
              </TouchableOpacity>
            </View>
            <View style={styles.rowWrapper}>
              <TouchableOpacity
                onPress={() => {
                  router.push({
                    pathname: 'webview',
                    params: {
                      uri: 'http://api.nhattamsoft.vn:4546/docs',
                      // uri: 'https://mimosaapp.misa.vn/popup/butransferdepositdetail/cb6a8db8-d462-4a23-81aa-d0f7de76b02c',
                      title: 'Tìm kiếm',
                      injectedJS: `
                      setTimeout(function () {
                        const input = document.getElementById('skw');
                        if (input) {
                          input.value = 'iPhone 15 Pro Max';
                          const keyupEvent = new KeyboardEvent('keyup', {
                            bubbles: true,
                            cancelable: true,
                            key: 'Enter'
                          });
                          input.dispatchEvent(keyupEvent);
                        } else {
                          console.log('❌ Không tìm thấy input');
                        }
                      }, 500);
                      true;
                    `,
                    },
                  });
                }}
                style={styles.row}
              >
                <Text style={styles.rowLabel}>Đánh giá ứng dụng</Text>

                <View style={styles.rowSpacer} />

                <FeatherIcon color='#bcbcbc' name='chevron-right' size={19} />
              </TouchableOpacity>
            </View>

            <View style={[styles.rowWrapper, styles.rowLast]}>
              <TouchableOpacity onPress={() => { }} style={styles.row}>
                <Text style={styles.rowLabel}>Điều khoản & Chính sách</Text>
                <View style={styles.rowSpacer} />
                <FeatherIcon color='#bcbcbc' name='chevron-right' size={19} />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionBody}>
            <View
              style={[
                styles.rowWrapper,
                styles.rowFirst,
                styles.rowLast,
                { alignItems: 'center' },
              ]}
            >
              <TouchableOpacity
                onPress={() => setShowActionSheetLogout(true)}
                style={styles.row}
              >
                <Text style={[styles.rowLabel, styles.rowLabelLogout]}>
                  Đăng xuất
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
      <ActionSheet
        useNativeIOS
        title={'Đăng xuất'}
        message={'Bạn có chắc chắn muốn đăng xuất không?'}
        cancelButtonIndex={1}
        destructiveButtonIndex={0}
        visible={showActionSheetLogout}
        showCancelButton={false}
        onDismiss={() => {
          setShowActionSheetLogout(false);
        }}
        options={[
          {
            label: 'Đăng xuất',
            onPress: () => {
              console.log('Đã đăng xuất');
              logoutMutation.mutate();
            },
          },
          {
            label: 'Hủy',
            onPress: () => {
              setShowActionSheetLogout(false);
            },
          },
        ]}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  /** Header */
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 26,
  },
  headerAction: {
    width: 40,
    height: 40,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 19,
    fontWeight: '600',
    color: '#000',
    flexGrow: 1,
    flexShrink: 1,
    flexBasis: 0,
    textAlign: 'center',
  },
  /** Content */
  content: {
    paddingHorizontal: 16,
    paddingBottom: '20%',
  },
  contentFooter: {
    marginTop: 24,
    fontSize: 13,
    fontWeight: '500',
    textAlign: 'center',
    color: '#a69f9f',
  },
  /** Section */
  section: {
    paddingVertical: 12,
  },
  sectionTitle: {
    margin: 8,
    marginLeft: 12,
    fontSize: 13,
    letterSpacing: 0.33,
    fontWeight: '500',
    color: '#a69f9f',
    textTransform: 'uppercase',
  },
  sectionBody: {
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  /** Profile */
  profile: {
    padding: 12,
    backgroundColor: '#fff',
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  profileAvatar: {
    width: 60,
    height: 60,
    borderRadius: 9999,
    marginRight: 12,
  },
  profileBody: {
    marginRight: 'auto',
  },
  profileName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#292929',
  },
  profileHandle: {
    marginTop: 2,
    fontSize: 12,
    fontWeight: '400',
    color: '#858585',
  },
  /** Row */
  row: {
    height: 44,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingRight: 12,
  },
  rowWrapper: {
    paddingLeft: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderColor: '#f0f0f0',
  },
  rowFirst: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  rowLabel: {
    fontSize: 16,
    letterSpacing: 0.24,
    color: '#000',
  },
  rowSpacer: {
    flexGrow: 1,
    flexShrink: 1,
    flexBasis: 0,
  },
  rowValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#ababab',
    marginRight: 4,
  },
  rowLast: {
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  rowLabelLogout: {
    width: '100%',
    textAlign: 'center',
    fontWeight: '600',
    color: '#dc2626',
  },
  rowHint: {
    fontSize: 12,
    color: '#888',
    marginTop: 2,
    maxWidth: 220,
  },
  badgeContainer: {
    backgroundColor: '#4285F4',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginLeft: 8,
  },
  badgeText: {
    fontSize: 10,
    color: 'white',
    fontWeight: '600',
  },
});

export default ProfileScreen;
