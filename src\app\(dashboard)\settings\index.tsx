import React from 'react';
import {
  View,
  ScrollView,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons, FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { ROUTES } from '@/core/constants/routes';
import { useTheme } from '@/core/theme/theme';
import Header from '@/shared/components/molecules/Header';

// Fix TypeScript error by properly typing the icon names
type IconName =
  | 'business'
  | 'document-text'
  | 'server'
  | 'color-palette'
  | 'language'
  | 'information-circle'
  | 'person'
  | 'chevron-forward'
  | 'notifications'
  | 'settings'
  | 'apps'
  | 'wifi';

// Interface for menu items
export interface MenuItem {
  id: string;
  title: string;
  icon?: IconName;
  customIcon?: () => React.ReactNode;
  color: string;
  onPress: () => void;
  description: string;
  route: string;
}

// Export getMenuItems để có thể sử dụng ở các component khác
export const getMenuItems = (router: any): MenuItem[] => {
  const { colors } = useTheme();
  return [
    {
      id: 'tokens',
      title: 'Token của tôi',
      customIcon: () => <FontAwesome5 name='coins' size={24} color='#FBBC05' />,
      color: '#FBBC05',
      onPress: () => router.push(ROUTES.TOKENS),
      description: 'Quản lý token và xem lịch sử giao dịch',
      route: ROUTES.TOKENS,
    },
    {
      id: 'projects',
      title: 'Quản lý dự án',
      icon: 'business' as IconName,
      color: '#4285F4',
      onPress: () => router.push(ROUTES.PROJECT_MANAGER),
      description: 'Chọn dự án mặc định cho ứng dụng',
      route: ROUTES.PROJECT_MANAGER,
    },
    {
      id: 'projects',
      title: 'Mô hình AI',
      icon: 'psychology' as IconName,
      color: '#4285F4',
      onPress: () => router.push(ROUTES.AI_MODELS),
      description: 'Chọn mô hình AI mặc định cho ứng dụng',
      route: ROUTES.AI_MODELS,
    },
    {
      id: 'document-types',
      title: 'Loại tài liệu',
      icon: 'document-text' as IconName,
      color: '#34A853',
      onPress: () => router.push(ROUTES.DOCUMENT_TYPE),
      description: 'Thiết lập loại tài liệu mặc định',
      route: ROUTES.DOCUMENT_TYPE,
    },
    {
      id: 'project-document-settings',
      title: 'Cài đặt dự án & tài liệu',
      icon: 'settings' as IconName,
      color: '#8E44AD',
      onPress: () => router.push(ROUTES.PROJECT_DOC_SETTINGS),
      description: 'Quản lý cài đặt dự án và loại tài liệu',
      route: ROUTES.PROJECT_DOC_SETTINGS,
    },
    {
      id: 'storage',
      title: 'Quản lý bộ nhớ',
      icon: 'server' as IconName,
      color: '#EA4335',
      onPress: () => router.push(ROUTES.STORAGE_MANAGER),
      description: 'Kiểm tra và tối ưu dung lượng',
      route: ROUTES.STORAGE_MANAGER,
    },
    {
      id: 'notifications',
      title: 'Thông báo',
      icon: 'notifications' as IconName,
      color: '#FF6B00',
      onPress: () => router.push(ROUTES.NOTIFICATIONS),
      description: 'Quản lý cài đặt thông báo',
      route: ROUTES.NOTIFICATIONS,
    },
    {
      id: 'appearance',
      title: 'Giao diện',
      icon: 'color-palette' as IconName,
      color: '#FBBC05',
      onPress: () => router.push(ROUTES.APPEARANCE),
      description: 'Tùy chỉnh giao diện người dùng',
      route: ROUTES.APPEARANCE,
    },
    {
      id: 'quick-actions',
      title: 'Chức năng nhanh',
      icon: 'apps' as IconName,
      color: '#2F4FCD',
      onPress: () => router.push(ROUTES.QUICK_ACTIONS),
      description: 'Tùy chỉnh chức năng nhanh ở trang chủ',
      route: ROUTES.QUICK_ACTIONS,
    },
    {
      id: 'language',
      title: 'Ngôn ngữ',
      icon: 'language' as IconName,
      color: '#673AB7',
      onPress: () => router.push(ROUTES.LANGUAGE),
      description: 'Thay đổi ngôn ngữ hiển thị',
      route: ROUTES.LANGUAGE,
    },
    {
      id: 'about',
      title: 'Về ứng dụng',
      icon: 'information-circle' as IconName,
      color: '#0F9D58',
      onPress: () => router.push(ROUTES.ABOUT),
      description: 'Thông tin phiên bản và cập nhật',
      route: ROUTES.ABOUT,
    },
    {
      id: 'user-profile',
      title: 'Hồ sơ người dùng',
      icon: 'person' as IconName,
      color: '#9C27B0',
      onPress: () => router.push(ROUTES.USER_PROFILE),
      description: 'Thông tin và cài đặt tài khoản',
      route: ROUTES.USER_PROFILE,
    },
    {
      id: 'websocket-test',
      title: 'WebSocket Test',
      icon: 'wifi' as IconName,
      color: '#00BCD4',
      onPress: () => router.push('/websocket-test'),
      description: 'Test kết nối và gửi nhận WebSocket',
      route: '/websocket-test',
    },
  ];
};

const SettingsScreen = () => {
  const router = useRouter();
  const menuItems = getMenuItems(router);

  return (
    <SafeAreaView style={styles.container} edges={['top', 'bottom']}>
      <Header title='Cài đặt' />
      <ScrollView style={styles.scrollView}>
        <View style={styles.menuContainer}>
          {menuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.menuItem}
              onPress={item.onPress}
              activeOpacity={0.7}
            >
              <View
                style={[
                  styles.iconContainer,
                  { backgroundColor: item.color + '20' },
                ]}
              >
                {item.customIcon ? (
                  item.customIcon()
                ) : (
                  <Ionicons name={item.icon!} size={24} color={item.color} />
                )}
              </View>
              <View style={styles.menuTextContainer}>
                <Text style={styles.menuTitle}>{item.title}</Text>
                <Text style={styles.menuDescription}>{item.description}</Text>
              </View>
              <Ionicons
                name={'chevron-forward' as IconName}
                size={20}
                color='#999'
              />
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  scrollView: {
    flex: 1,
  },
  menuContainer: {
    padding: 16,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  iconContainer: {
    width: 42,
    height: 42,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  menuTextContainer: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  menuDescription: {
    fontSize: 13,
    color: '#666',
  },
});

export default SettingsScreen;
