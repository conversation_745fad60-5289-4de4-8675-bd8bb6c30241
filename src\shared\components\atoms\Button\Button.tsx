import React from 'react';
import { Button as RNUIButton } from 'react-native-ui-lib';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'link';
type ButtonSize = 'small' | 'medium' | 'large';

interface ButtonProps {
  label?: string;
  children?: React.ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  fullWidth?: boolean;
  iconSource?: any;
  rightIconSource?: any;
  style?: object;
  onPress?: () => void;
  testID?: string;
}

export const Button: React.FC<ButtonProps> = ({
  label,
  children,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  fullWidth = false,
  iconSource,
  rightIconSource,
  style,
  onPress,
  testID,
  ...props
}) => {
  const getButtonProps = () => {
    const buttonProps: Record<string, any> = {
      disabled,
      fullWidth,
      testID,
    };

    // Variant props
    switch (variant) {
      case 'primary':
        buttonProps.backgroundColor = 'primary';
        break;
      case 'secondary':
        buttonProps.backgroundColor = 'secondary';
        break;
      case 'outline':
        buttonProps.outline = true;
        buttonProps.outlineColor = 'primary';
        break;
      case 'link':
        buttonProps.link = true;
        break;
    }

    // Size props
    switch (size) {
      case 'small':
        buttonProps.size = 'small';
        break;
      case 'medium':
        buttonProps.size = 'medium';
        break;
      case 'large':
        buttonProps.size = 'large';
        break;
    }

    return buttonProps;
  };

  return (
    <RNUIButton
      {...getButtonProps()}
      label={label}
      iconSource={iconSource}
      rightIconSource={rightIconSource}
      style={style}
      onPress={onPress}
      {...props}
    >
      {children}
    </RNUIButton>
  );
};
