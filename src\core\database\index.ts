import * as SQLite from 'expo-sqlite';
import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';

// Open the database connection
export const openDatabase = async (): Promise<SQLite.SQLiteDatabase> => {
  if (Platform.OS === 'web') {
    // Use mockup for web
    return {
      execAsync: async () => { },
      closeAsync: async () => { },
    } as unknown as SQLite.SQLiteDatabase;
  }

  return await SQLite.openDatabaseAsync('ntsoftDocumentAI.db');
};

// Initialize the database with tables
export const initDatabase = async (db: SQLite.SQLiteDatabase) => {
  // User table
  const createUserTable = `
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      username TEXT NOT NULL,
      email TEXT,
      full_name TEXT,
      avatar TEXT,
      created_at INTEGER,
      updated_at INTEGER
    );
  `;

  // Project table
  const createProjectTable = `
    CREATE TABLE IF NOT EXISTS projects (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      created_at INTEGER,
      updated_at INTEGER
    );
  `;

  // Scanned document table
  const createScannedDocumentTable = `
    CREATE TABLE IF NOT EXISTS scanned_documents (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      description TEXT,
      file_path TEXT NOT NULL,
      file_type TEXT,
      file_size INTEGER,
      thumbnail_path TEXT,
      project_id TEXT,
      user_id TEXT,
      is_synchronized BOOLEAN DEFAULT FALSE,
      created_at INTEGER,
      updated_at INTEGER,
      FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    );
  `;

  // Document tags table
  const createDocumentTagTable = `
    CREATE TABLE IF NOT EXISTS document_tags (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      color TEXT,
      created_at INTEGER,
      updated_at INTEGER
    );
  `;

  // Document to tag relationship (many-to-many)
  const createDocumentTagRelationTable = `
    CREATE TABLE IF NOT EXISTS document_tag_relations (
      document_id TEXT NOT NULL,
      tag_id TEXT NOT NULL,
      created_at INTEGER,
      PRIMARY KEY (document_id, tag_id),
      FOREIGN KEY (document_id) REFERENCES scanned_documents (id) ON DELETE CASCADE,
      FOREIGN KEY (tag_id) REFERENCES document_tags (id) ON DELETE CASCADE
    );
  `;

  // Settings table
  const createSettingsTable = `
    CREATE TABLE IF NOT EXISTS settings (
      key TEXT PRIMARY KEY,
      value TEXT NOT NULL,
      created_at INTEGER,
      updated_at INTEGER
    );
  `;

  // Create tables
  const execQuery = async (query: string) => {
    try {
      await db.execAsync(query);
      return true;
    } catch (error) {
      console.error('Error executing query:', error);
      return false;
    }
  };

  // Execute all queries in sequence
  try {
    await execQuery(createUserTable);
    await execQuery(createProjectTable);
    await execQuery(createScannedDocumentTable);
    await execQuery(createDocumentTagTable);
    await execQuery(createDocumentTagRelationTable);
    await execQuery(createSettingsTable);
    console.log('Database initialized successfully');
  } catch (error: any) {
    console.error('Error initializing database:', error);
  }
};

// Initialize the database and export the instance
const initializeDb = async () => {
  const db = await openDatabase();
  await initDatabase(db);
  return db;
};

// Export the database promise
export const databasePromise = initializeDb();

// Default export for backward compatibility
export default {
  getDatabase: async () => await databasePromise
};

// Hàm để lấy đường dẫn database, sử dụng cho debug
export const getDatabasePath = async (): Promise<string> => {
  const db = await databasePromise;
  return `${FileSystem.documentDirectory}SQLite/ntsoft_document_ai.db`;
}; 