import { useState, useCallback } from 'react';
import { ScanSessionRepository, ScanSession } from '@core/database/repositories/scanSessionRepository';

export const useScanSessionRepository = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);
    const repository = new ScanSessionRepository();

    const getAllSessions = useCallback(async (userId?: string): Promise<ScanSession[]> => {
        setLoading(true);
        setError(null);

        try {
            const sessions = await repository.getAllSessions(userId);
            setLoading(false);
            return sessions;
        } catch (err) {
            const error = err instanceof Error ? err : new Error('Unknown error occurred');
            setError(error);
            setLoading(false);
            throw error;
        }
    }, [repository]);

    const getById = useCallback(async (id: string): Promise<ScanSession | null> => {
        setLoading(true);
        setError(null);

        try {
            const session = await repository.getById(id);
            setLoading(false);
            return session;
        } catch (err) {
            const error = err instanceof Error ? err : new Error('Unknown error occurred');
            setError(error);
            setLoading(false);
            throw error;
        }
    }, [repository]);

    const addSession = useCallback(async (session: Omit<ScanSession, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
        setLoading(true);
        setError(null);

        try {
            const id = await repository.add(session);
            setLoading(false);
            return id;
        } catch (err) {
            const error = err instanceof Error ? err : new Error('Unknown error occurred');
            setError(error);
            setLoading(false);
            throw error;
        }
    }, [repository]);

    const updateSession = useCallback(async (id: string, updates: Partial<Omit<ScanSession, 'id' | 'createdAt' | 'updatedAt'>>): Promise<void> => {
        setLoading(true);
        setError(null);

        try {
            await repository.update(id, updates);
            setLoading(false);
        } catch (err) {
            const error = err instanceof Error ? err : new Error('Unknown error occurred');
            setError(error);
            setLoading(false);
            throw error;
        }
    }, [repository]);

    const deleteSession = useCallback(async (id: string): Promise<void> => {
        setLoading(true);
        setError(null);

        try {
            await repository.delete(id);
            setLoading(false);
        } catch (err) {
            const error = err instanceof Error ? err : new Error('Unknown error occurred');
            setError(error);
            setLoading(false);
            throw error;
        }
    }, [repository]);

    const deleteOldSessions = useCallback(async (daysOld: number): Promise<number> => {
        setLoading(true);
        setError(null);

        try {
            const deletedCount = await repository.deleteOldSessions(daysOld);
            setLoading(false);
            return deletedCount;
        } catch (err) {
            const error = err instanceof Error ? err : new Error('Unknown error occurred');
            setError(error);
            setLoading(false);
            throw error;
        }
    }, [repository]);

    return {
        loading,
        error,
        getAllSessions,
        getById,
        addSession,
        updateSession,
        deleteSession,
        deleteOldSessions,
    };
}; 