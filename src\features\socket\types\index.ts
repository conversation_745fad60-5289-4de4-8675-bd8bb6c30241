export interface WebSocketMessage {
    action: string;
    data?: any;
    timestamp?: number;
}

export interface WebSocketState {
    isConnected: boolean;
    isConnecting: boolean;
    error: string | null;
    lastMessage: WebSocketMessage | null;
    messageHistory: WebSocketMessage[];
}

export interface WebSocketConfig {
    url: string;
    protocols?: string | string[];
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
}

export type WebSocketEventType =
    | 'open'
    | 'close'
    | 'error'
    | 'message';

export interface WebSocketEventHandlers {
    onOpen?: (event: any) => void;
    onClose?: (event: any) => void;
    onError?: (error: any) => void;
    onMessage?: (message: WebSocketMessage) => void;
} 