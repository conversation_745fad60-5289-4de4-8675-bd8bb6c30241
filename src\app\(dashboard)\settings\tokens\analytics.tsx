import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons, FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { AnimatedView } from '@/shared/animations/components';
import { useTokens } from '@features/tokens/tokenContext';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, LineChart } from 'react-native-gifted-charts';

type IconName = 'arrow-back';
type ChartPeriod = 'week' | 'month' | 'year';

const screenWidth = Dimensions.get('window').width;

const TokenAnalyticsScreen = () => {
  const router = useRouter();
  const { tokenBalance, tokenHistory } = useTokens();
  const [selectedPeriod, setSelectedPeriod] = useState<ChartPeriod>('week');

  // Dữ liệu cho biểu đồ phân loại chi tiêu token
  const tokenUsageData = [
    { value: 60, color: '#4285F4', text: '60%', label: 'Quét' },
    { value: 25, color: '#34A853', text: '25%', label: 'OCR' },
    { value: 15, color: '#FBBC05', text: '15%', label: 'Lưu trữ' },
  ];

  // Dữ liệu cho biểu đồ xu hướng sử dụng token theo tuần
  const weeklyData = [
    { value: 8, label: 'T2', frontColor: '#4285F4' },
    { value: 12, label: 'T3', frontColor: '#4285F4' },
    { value: 6, label: 'T4', frontColor: '#4285F4' },
    { value: 14, label: 'T5', frontColor: '#4285F4' },
    { value: 10, label: 'T6', frontColor: '#4285F4' },
    { value: 5, label: 'T7', frontColor: '#4285F4' },
    { value: 2, label: 'CN', frontColor: '#4285F4' },
  ];

  // Dữ liệu cho biểu đồ xu hướng sử dụng token theo tháng
  const monthlyData = [
    { value: 45, label: 'Tuần 1', frontColor: '#4285F4' },
    { value: 32, label: 'Tuần 2', frontColor: '#4285F4' },
    { value: 38, label: 'Tuần 3', frontColor: '#4285F4' },
    { value: 41, label: 'Tuần 4', frontColor: '#4285F4' },
  ];

  // Dữ liệu cho biểu đồ xu hướng theo năm
  const yearlyData = [
    { value: 140, label: 'T1', frontColor: '#4285F4' },
    { value: 120, label: 'T2', frontColor: '#4285F4' },
    { value: 160, label: 'T3', frontColor: '#4285F4' },
    { value: 180, label: 'T4', frontColor: '#4285F4' },
    { value: 150, label: 'T5', frontColor: '#4285F4' },
    { value: 170, label: 'T6', frontColor: '#4285F4' },
    { value: 190, label: 'T7', frontColor: '#4285F4' },
    { value: 160, label: 'T8', frontColor: '#4285F4' },
    { value: 150, label: 'T9', frontColor: '#4285F4' },
    { value: 180, label: 'T10', frontColor: '#4285F4' },
    { value: 200, label: 'T11', frontColor: '#4285F4' },
    { value: 220, label: 'T12', frontColor: '#4285F4' },
  ];

  // Dữ liệu cho biểu đồ dòng token balance theo thời gian
  const balanceData = [
    { value: 80 },
    { value: 70 },
    { value: 110 },
    { value: 95 },
    { value: 130 },
    { value: 120 },
    { value: 150 },
    { value: 140 },
    { value: 160 },
    { value: tokenBalance },
  ];

  // Lấy dữ liệu biểu đồ theo khoảng thời gian được chọn
  const getChartData = () => {
    switch (selectedPeriod) {
      case 'week':
        return weeklyData;
      case 'month':
        return monthlyData;
      case 'year':
        return yearlyData;
      default:
        return weeklyData;
    }
  };

  // Tính tổng token đã sử dụng
  const totalTokensUsed = tokenHistory
    .filter(item => item.type !== 'purchase')
    .reduce((sum, item) => sum + Math.abs(item.amount), 0);

  // Tính tổng token đã mua
  const totalTokensPurchased = tokenHistory
    .filter(item => item.type === 'purchase')
    .reduce((sum, item) => sum + item.amount, 0);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name={'arrow-back' as IconName} size={24} color='#333' />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Phân tích Token</Text>
        <View style={styles.placeholderView} />
      </View>

      <ScrollView style={styles.scrollView}>
        {/* Token Summary */}
        <AnimatedView style={styles.summaryContainer} duration={600}>
          <View style={styles.summaryCard}>
            <View style={styles.summaryIconContainer}>
              <FontAwesome5 name='coins' size={24} color='#FBBC05' />
            </View>
            <View style={styles.summaryInfo}>
              <Text style={styles.summaryLabel}>Token hiện có</Text>
              <Text style={styles.summaryValue}>{tokenBalance}</Text>
            </View>
          </View>

          <View style={styles.summaryCard}>
            <View
              style={[
                styles.summaryIconContainer,
                { backgroundColor: 'rgba(66, 133, 244, 0.1)' },
              ]}
            >
              <FontAwesome5 name='chart-line' size={24} color='#4285F4' />
            </View>
            <View style={styles.summaryInfo}>
              <Text style={styles.summaryLabel}>Đã sử dụng</Text>
              <Text style={styles.summaryValue}>{totalTokensUsed}</Text>
            </View>
          </View>

          <View style={styles.summaryCard}>
            <View
              style={[
                styles.summaryIconContainer,
                { backgroundColor: 'rgba(52, 168, 83, 0.1)' },
              ]}
            >
              <FontAwesome5 name='shopping-cart' size={24} color='#34A853' />
            </View>
            <View style={styles.summaryInfo}>
              <Text style={styles.summaryLabel}>Đã mua</Text>
              <Text style={styles.summaryValue}>{totalTokensPurchased}</Text>
            </View>
          </View>
        </AnimatedView>

        {/* Distribution Chart - What tokens are spent on */}
        <AnimatedView style={styles.chartCard} duration={600} delay={100}>
          <Text style={styles.chartTitle}>Phân bổ sử dụng Token</Text>
          <Text style={styles.chartSubtitle}>
            Cách Token của bạn được sử dụng
          </Text>

          <View style={styles.pieChartContainer}>
            <PieChart
              data={tokenUsageData}
              donut
              radius={80}
              innerRadius={50}
              innerCircleColor={'#fff'}
              textSize={12}
              showTextBackground={false}
              textBackgroundRadius={12}
              textColor={'#fff'}
              showText
              labelsPosition={'outward'}
            />

            <View style={styles.legendContainer}>
              {tokenUsageData.map((item, index) => (
                <View key={index} style={styles.legendItem}>
                  <View
                    style={[
                      styles.legendColor,
                      { backgroundColor: item.color },
                    ]}
                  />
                  <Text style={styles.legendLabel}>{item.label}</Text>
                  <Text style={styles.legendValue}>{item.text}</Text>
                </View>
              ))}
            </View>
          </View>
        </AnimatedView>

        {/* Usage Trend Chart */}
        <AnimatedView style={styles.chartCard} duration={600} delay={200}>
          <Text style={styles.chartTitle}>Xu hướng sử dụng Token</Text>

          <View style={styles.periodSelector}>
            <TouchableOpacity
              style={[
                styles.periodButton,
                selectedPeriod === 'week' && styles.periodButtonActive,
              ]}
              onPress={() => setSelectedPeriod('week')}
            >
              <Text
                style={[
                  styles.periodButtonText,
                  selectedPeriod === 'week' && styles.periodButtonTextActive,
                ]}
              >
                Tuần
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.periodButton,
                selectedPeriod === 'month' && styles.periodButtonActive,
              ]}
              onPress={() => setSelectedPeriod('month')}
            >
              <Text
                style={[
                  styles.periodButtonText,
                  selectedPeriod === 'month' && styles.periodButtonTextActive,
                ]}
              >
                Tháng
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.periodButton,
                selectedPeriod === 'year' && styles.periodButtonActive,
              ]}
              onPress={() => setSelectedPeriod('year')}
            >
              <Text
                style={[
                  styles.periodButtonText,
                  selectedPeriod === 'year' && styles.periodButtonTextActive,
                ]}
              >
                Năm
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.barChartContainer}>
            <BarChart
              data={getChartData()}
              barWidth={selectedPeriod === 'year' ? 16 : 24}
              spacing={selectedPeriod === 'year' ? 10 : 20}
              barBorderRadius={4}
              xAxisThickness={0}
              yAxisThickness={0}
              xAxisLabelTextStyle={{ color: '#666', fontSize: 12 }}
              hideYAxisText
              noOfSections={4}
              maxValue={
                selectedPeriod === 'year'
                  ? 250
                  : selectedPeriod === 'month'
                    ? 50
                    : 20
              }
              showGradient
              gradientColor={'#4285F4'}
              disablePress
            />
          </View>
        </AnimatedView>

        {/* Balance Trend Chart */}
        <AnimatedView style={styles.chartCard} duration={600} delay={300}>
          <Text style={styles.chartTitle}>Xu hướng số dư Token</Text>
          <Text style={styles.chartSubtitle}>
            Biến động số dư trong 10 lần gần nhất
          </Text>

          <View style={styles.lineChartContainer}>
            <LineChart
              data={balanceData}
              color='#34A853'
              dataPointsColor='#34A853'
              startFillColor='rgba(52, 168, 83, 0.2)'
              startOpacity={0.8}
              endOpacity={0.2}
              spacing={(screenWidth - 80) / balanceData.length}
              thickness={3}
              hideDataPoints={false}
              hideRules
              yAxisTextStyle={{ color: '#999', fontSize: 12 }}
              noOfSections={5}
              maxValue={200}
              curved
            />
          </View>
        </AnimatedView>

        {/* Recommendations */}
        <AnimatedView
          style={styles.recommendationCard}
          duration={600}
          delay={400}
        >
          <Text style={styles.chartTitle}>Khuyến nghị</Text>

          <View style={styles.recommendationItem}>
            <Ionicons
              name='flash'
              size={24}
              color='#FBBC05'
              style={styles.recommendationIcon}
            />
            <View style={styles.recommendationContent}>
              <Text style={styles.recommendationTitle}>
                Mua gói token lớn hơn để tiết kiệm
              </Text>
              <Text style={styles.recommendationText}>
                Với tốc độ sử dụng hiện tại, bạn có thể tiết kiệm 20% khi mua
                gói 300 token.
              </Text>
            </View>
          </View>

          <View style={styles.recommendationItem}>
            <Ionicons
              name='trending-down'
              size={24}
              color='#34A853'
              style={styles.recommendationIcon}
            />
            <View style={styles.recommendationContent}>
              <Text style={styles.recommendationTitle}>
                Tối ưu hóa sử dụng OCR
              </Text>
              <Text style={styles.recommendationText}>
                Nhận dạng văn bản tiêu thụ 25% token của bạn. Hãy cân nhắc chỉ
                sử dụng OCR cho các tài liệu quan trọng.
              </Text>
            </View>
          </View>
        </AnimatedView>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f6fa',
  },
  placeholderView: {
    width: 40,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryCard: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 4,
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  summaryIconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(251, 188, 5, 0.1)',
    marginBottom: 8,
  },
  summaryInfo: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
  },
  chartCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  chartSubtitle: {
    fontSize: 13,
    color: '#666',
    marginBottom: 16,
  },
  pieChartContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  legendContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: 20,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  legendLabel: {
    fontSize: 13,
    color: '#333',
    marginRight: 4,
  },
  legendValue: {
    fontSize: 13,
    fontWeight: '600',
    color: '#333',
  },
  periodSelector: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 16,
  },
  periodButton: {
    paddingVertical: 6,
    paddingHorizontal: 16,
    borderRadius: 16,
    marginHorizontal: 6,
    backgroundColor: '#f5f6fa',
  },
  periodButtonActive: {
    backgroundColor: '#2F4FCD',
  },
  periodButtonText: {
    fontSize: 14,
    color: '#666',
  },
  periodButtonTextActive: {
    color: '#fff',
    fontWeight: '500',
  },
  barChartContainer: {
    marginTop: 8,
    height: 220,
  },
  lineChartContainer: {
    height: 220,
    paddingTop: 20,
  },
  recommendationCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  recommendationItem: {
    flexDirection: 'row',
    marginVertical: 10,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
  },
  recommendationIcon: {
    marginRight: 16,
  },
  recommendationContent: {
    flex: 1,
  },
  recommendationTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  recommendationText: {
    fontSize: 13,
    color: '#666',
    lineHeight: 18,
  },
});

export default TokenAnalyticsScreen;
