import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { databasePromise } from '@core/database';
import { DocumentRepository } from '@core/database/repositories/documentRepository';
import { SettingsRepository } from '@core/database/repositories/settingsRepository';

interface DatabaseContextType {
  isReady: boolean;
  documentRepository: DocumentRepository;
  settingsRepository: SettingsRepository;
}

const DatabaseContext = createContext<DatabaseContextType | null>(null);

export const useDatabaseContext = () => {
  const context = useContext(DatabaseContext);
  if (!context) {
    throw new Error(
      'useDatabaseContext must be used within a DatabaseProvider',
    );
  }
  return context;
};

interface DatabaseProviderProps {
  children: ReactNode;
}

export const DatabaseProvider: React.FC<DatabaseProviderProps> = ({
  children,
}) => {
  const [isReady, setIsReady] = useState(false);
  const [repositories] = useState({
    documentRepository: new DocumentRepository(),
    settingsRepository: new SettingsRepository(),
  });

  useEffect(() => {
    const initDatabase = async () => {
      try {
        // Wait for database to initialize
        await databasePromise;
        setIsReady(true);
      } catch (error) {
        console.error('Failed to initialize database:', error);
      }
    };

    initDatabase();
  }, []);

  const value = {
    isReady,
    ...repositories,
  };

  return (
    <DatabaseContext.Provider value={value}>
      {children}
    </DatabaseContext.Provider>
  );
};
