import * as SQLite from 'expo-sqlite';
import { databasePromise } from '../index';
import * as FileSystem from 'expo-file-system';

export interface Setting {
    key: string;
    value: string;
    createdAt: number;
    updatedAt: number;
}

export class SettingsRepository {
    private db: SQLite.SQLiteDatabase | null = null;

    constructor() {
        this.initialize();
    }

    private async initialize() {
        this.db = await databasePromise;
    }

    async getSetting(key: string): Promise<string | null> {
        if (!this.db) await this.initialize();

        const query = 'SELECT value FROM settings WHERE key = ?';
        const result = await this.db!.getFirstAsync<{ value: string }>(query, [key]);

        if (!result) return null;
        return result.value;
    }

    async setSetting(key: string, value: string): Promise<void> {
        if (!this.db) await this.initialize();

        const now = Date.now();
        const existingSetting = await this.getSetting(key);

        if (existingSetting !== null) {
            // Update existing setting
            const updateQuery = 'UPDATE settings SET value = ?, updated_at = ? WHERE key = ?';
            await this.db!.runAsync(updateQuery, [value, now, key]);
        } else {
            // Insert new setting
            const insertQuery = 'INSERT INTO settings (key, value, created_at, updated_at) VALUES (?, ?, ?, ?)';
            await this.db!.runAsync(insertQuery, [key, value, now, now]);
        }
    }

    async deleteSetting(key: string): Promise<void> {
        if (!this.db) await this.initialize();

        const query = 'DELETE FROM settings WHERE key = ?';
        await this.db!.runAsync(query, [key]);
    }

    async getAllSettings(): Promise<Record<string, string>> {
        if (!this.db) await this.initialize();

        const query = 'SELECT key, value FROM settings';
        const results = await this.db!.getAllAsync<{ key: string, value: string }>(query, []);

        const settings: Record<string, string> = {};

        for (const row of results) {
            settings[row.key] = row.value;
        }

        return settings;
    }

    async debug(): Promise<string> {
        const dbPath = `${FileSystem.documentDirectory}SQLite/nts_document_ai.db`;
        console.log(`Database path: ${dbPath}`);
        return dbPath;
    }
} 