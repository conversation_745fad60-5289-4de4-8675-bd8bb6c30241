import { useCallback } from 'react';
import Toast from 'react-native-toast-message';

export type ToastType = 'success' | 'error' | 'info';

interface UseToastReturn {
  showSuccess: (message: string, title?: string) => void;
  showError: (message: string, title?: string) => void;
  showInfo: (message: string, title?: string) => void;
  showToast: (type: ToastType, message: string, title?: string) => void;
}

export const useToast = (): UseToastReturn => {
  const showToast = useCallback(
    (type: ToastType, message: string, title?: string) => {
      Toast.show({
        type,
        text1:
          title ||
          (type === 'success'
            ? 'Thành công'
            : type === 'error'
              ? 'Lỗi'
              : 'Thông báo'),
        text2: message,
        position: 'bottom',
        visibilityTime: 3000,
      });
    },
    [],
  );

  const showSuccess = useCallback(
    (message: string, title?: string) => {
      showToast('success', message, title);
    },
    [showToast],
  );

  const showError = useCallback(
    (message: string, title?: string) => {
      showToast('error', message, title);
    },
    [showToast],
  );

  const showInfo = useCallback(
    (message: string, title?: string) => {
      showToast('info', message, title);
    },
    [showToast],
  );

  return {
    showSuccess,
    showError,
    showInfo,
    showToast,
  };
};
