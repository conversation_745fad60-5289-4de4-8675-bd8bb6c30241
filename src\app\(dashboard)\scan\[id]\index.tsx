import React, { useCallback, useState, useRef } from 'react';
import {
  Image,
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  Platform,
  Dimensions,
  Modal,
  StatusBar,
  ScrollView,
  Animated,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useScan } from '@features/scan/context/ScanContext';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { COLORS } from '@core/constants/theme';
import {
  AnimatedView,
  FadeInView,
  ScaleView,
} from '@/shared/animations/components';
import { useFadeIn, useScale, useSlideY } from '@/shared/animations';
import { SafeAreaView } from 'react-native-safe-area-context';

const { width, height } = Dimensions.get('window');
const IMAGE_WIDTH = width;
const IMAGE_HEIGHT = width * 1.4; // Tỷ lệ khung hình A4

export default function ScanDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { scanSessions } = useScan();
  const router = useRouter();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const scrollY = useRef(new Animated.Value(0)).current;

  // Setup animations
  const headerOpacity = useFadeIn(500);
  const contentFade = useFadeIn(600, 200);
  const contentScale = useScale(600, 200, 0.98);

  // Header animations based on scroll
  const headerElevation = scrollY.interpolate({
    inputRange: [0, 50],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  const headerBackgroundColor = scrollY.interpolate({
    inputRange: [0, 50],
    outputRange: ['rgba(255,255,255,0.9)', '#ffffff'],
    extrapolate: 'clamp',
  });

  const session = scanSessions.find(s => s.id === id);

  if (!session) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <AnimatedView duration={800} slideValue={20}>
          <Ionicons
            name='alert-circle-outline'
            size={64}
            color={COLORS.textLight}
          />
          <Text style={styles.errorText}>Không tìm thấy tài liệu</Text>
          <TouchableOpacity
            style={styles.backToHomeButton}
            onPress={() => router.back()}
          >
            <Text style={styles.backToHomeText}>Quay lại</Text>
          </TouchableOpacity>
        </AnimatedView>
      </SafeAreaView>
    );
  }

  const handleImagePress = (image: string) => {
    setSelectedImage(image);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle='dark-content' />

      {/* Animated Header */}
      <Animated.View
        style={[
          styles.header,
          {
            opacity: headerOpacity,
            backgroundColor: headerBackgroundColor,
            shadowOpacity: headerElevation,
          },
        ]}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name='arrow-back' size={24} color={COLORS.text} />
        </TouchableOpacity>
        <Animated.Text style={styles.title} numberOfLines={1}>
          {session.title}
        </Animated.Text>
        {/* <TouchableOpacity style={styles.shareButton}>
            <Ionicons name='share-outline' size={24} color={COLORS.text} />
          </TouchableOpacity> */}
      </Animated.View>

      <Animated.ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        bounces={false}
        scrollEventThrottle={16}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false },
        )}
      >
        <FadeInView style={styles.documentInfo} delay={100}>
          <View style={styles.infoRow}>
            <MaterialIcons name='insert-drive-file' size={20} color='#555' />
            <Text style={styles.infoText}>{session.images.length} trang</Text>
          </View>
          <View style={styles.infoRow}>
            <MaterialIcons name='access-time' size={20} color='#555' />
            <Text style={styles.infoText}>
              {new Date(session.createdAt).toLocaleDateString('vi-VN')}
            </Text>
          </View>
        </FadeInView>

        <Animated.View
          style={[
            styles.documentContainer,
            {
              opacity: contentFade,
              transform: [{ scale: contentScale }],
            },
          ]}
        >
          {session.images.map((image, index) => (
            <FadeInView
              key={`${image}-${index}`}
              style={styles.pageContainer}
              delay={200 + index * 100}
            >
              <TouchableOpacity
                activeOpacity={0.9}
                onPress={() => handleImagePress(image)}
              >
                <Image
                  source={{ uri: image }}
                  style={styles.pageImage}
                  resizeMode='contain'
                />
                <View style={styles.pageNumber}>
                  <Text style={styles.pageNumberText}>Trang {index + 1}</Text>
                </View>
              </TouchableOpacity>
            </FadeInView>
          ))}

          {/* <View style={styles.documentActions}>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name='cloud-upload-outline' size={22} color='#fff' />
              <Text style={styles.actionText}>Tải lên</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name='share-social-outline' size={22} color='#fff' />
              <Text style={styles.actionText}>Chia sẻ</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: '#f44336' }]}
            >
              <Ionicons name='trash-outline' size={22} color='#fff' />
              <Text style={styles.actionText}>Xóa</Text>
            </TouchableOpacity>
          </View> */}
        </Animated.View>
      </Animated.ScrollView>

      <Modal
        visible={!!selectedImage}
        transparent={true}
        animationType='fade'
        onRequestClose={() => setSelectedImage(null)}
      >
        <View style={styles.modalContainer}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setSelectedImage(null)}
          >
            <Ionicons name='close' size={28} color='#fff' />
          </TouchableOpacity>

          <Image
            source={{ uri: selectedImage || '' }}
            style={styles.modalImage}
            resizeMode='contain'
          />
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eef2f7',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#f0f4f8',
  },
  shareButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#f0f4f8',
  },
  title: {
    flex: 1,
    fontSize: 17,
    fontWeight: '600',
    color: COLORS.text,
    marginHorizontal: 12,
  },
  scrollView: {
    flex: 1,
  },
  documentInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    marginBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eef2f7',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoText: {
    marginLeft: 6,
    color: '#555',
    fontSize: 14,
  },
  documentContainer: {
    backgroundColor: '#fff',
    paddingBottom: 20,
  },
  pageContainer: {
    position: 'relative',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eef2f7',
    margin: 8,
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  pageImage: {
    width: IMAGE_WIDTH - 16,
    height: IMAGE_HEIGHT - 16,
    backgroundColor: '#fafbfc',
  },
  pageNumber: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  pageNumberText: {
    color: '#fff',
    fontSize: 13,
    fontWeight: '600',
  },
  documentActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 16,
    paddingHorizontal: 8,
    marginTop: 12,
  },
  actionButton: {
    backgroundColor: '#4361ee',
    paddingVertical: 12,
    paddingHorizontal: 18,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 100,
  },
  actionText: {
    color: '#fff',
    marginLeft: 8,
    fontWeight: '600',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 24,
  },
  errorText: {
    marginTop: 16,
    fontSize: 17,
    color: COLORS.textLight,
    textAlign: 'center',
  },
  backToHomeButton: {
    marginTop: 24,
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: '#4361ee',
    borderRadius: 8,
  },
  backToHomeText: {
    color: '#fff',
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalImage: {
    width: width,
    height: height,
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
});
