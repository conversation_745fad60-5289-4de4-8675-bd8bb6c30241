import React from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  ScrollView,
  StyleSheet,
} from 'react-native';
import { View } from 'react-native-ui-lib';
import { useKeyboardDismiss } from '@/shared/hooks/useKeyboardDismiss';
import { useKeyboardVisible } from '@/shared/hooks/useKeyboardVisible';

interface KeyboardAvoidingWrapperProps {
  children: React.ReactNode;
  style?: object;
  scrollEnabled?: boolean;
  behavior?: 'height' | 'position' | 'padding';
  keyboardVerticalOffset?: number;
  enableTouchToDismiss?: boolean;
  contentContainerStyle?: object;
}

export const KeyboardAvoidingWrapper: React.FC<
  KeyboardAvoidingWrapperProps
> = ({
  children,
  style,
  scrollEnabled = false, // Mặc định tắt scroll để UI đẹp hơn
  behavior = Platform.OS === 'ios' ? 'padding' : 'height',
  keyboardVerticalOffset = Platform.OS === 'ios' ? 0 : 20,
  enableTouchToDismiss = true,
  contentContainerStyle,
}) => {
  const { dismissKeyboard } = useKeyboardDismiss();
  const isKeyboardVisible = useKeyboardVisible();

  const content = scrollEnabled ? (
    <ScrollView
      style={styles.scrollView}
      contentContainerStyle={[
        styles.scrollContent,
        contentContainerStyle,
        // Chỉ flex khi keyboard không hiện để tránh UI xấu
        !isKeyboardVisible && { flexGrow: 1 },
      ]}
      keyboardShouldPersistTaps='handled'
      showsVerticalScrollIndicator={false}
      bounces={false} // Tắt bounce để UI mượt hơn
      overScrollMode='never' // Android: tắt over scroll
    >
      {children}
    </ScrollView>
  ) : (
    <View style={[styles.container, contentContainerStyle]}>{children}</View>
  );

  if (enableTouchToDismiss) {
    return (
      <KeyboardAvoidingView
        style={[styles.keyboardAvoidingView, style]}
        behavior={behavior}
        keyboardVerticalOffset={keyboardVerticalOffset}
      >
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          {content}
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    );
  }

  return (
    <KeyboardAvoidingView
      style={[styles.keyboardAvoidingView, style]}
      behavior={behavior}
      keyboardVerticalOffset={keyboardVerticalOffset}
    >
      {content}
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    // Không set flexGrow mặc định, sẽ được điều chỉnh động
  },
});
