import { useState, useEffect, useRef, useCallback } from 'react';
import { WebSocketService } from '../services/websocketService';
import { WebSocketConfig, WebSocketState, WebSocketMessage, WebSocketEventHandlers } from '../types';

export const useWebSocket = (
    config: WebSocketConfig,
    eventHandlers: WebSocketEventHandlers = {}
) => {
    const [state, setState] = useState<WebSocketState>({
        isConnected: false,
        isConnecting: false,
        error: null,
        lastMessage: null,
        messageHistory: []
    });

    const wsServiceRef = useRef<WebSocketService | null>(null);

    const updateState = useCallback(() => {
        if (wsServiceRef.current) {
            setState(wsServiceRef.current.getState());
        }
    }, []);

    const handleOpen = useCallback((event: any) => {
        updateState();
        eventHandlers.onOpen?.(event);
    }, [updateState, eventHandlers]);

    const handleClose = useCallback((event: any) => {
        updateState();
        eventHandlers.onClose?.(event);
    }, [updateState, eventHandlers]);

    const handleError = useCallback((error: any) => {
        updateState();
        eventHandlers.onError?.(error);
    }, [updateState, eventHandlers]);

    const handleMessage = useCallback((message: WebSocketMessage) => {
        updateState();
        eventHandlers.onMessage?.(message);
    }, [updateState, eventHandlers]);

    const connect = useCallback(async () => {
        if (!wsServiceRef.current) {
            wsServiceRef.current = new WebSocketService(config, {
                onOpen: handleOpen,
                onClose: handleClose,
                onError: handleError,
                onMessage: handleMessage
            });
        }

        try {
            await wsServiceRef.current.connect();
        } catch (error) {
            console.error('Failed to connect:', error);
        }
    }, [config, handleOpen, handleClose, handleError, handleMessage]);

    const disconnect = useCallback(() => {
        if (wsServiceRef.current) {
            wsServiceRef.current.disconnect();
            updateState();
        }
    }, [updateState]);

    const send = useCallback((message: WebSocketMessage) => {
        if (wsServiceRef.current) {
            const success = wsServiceRef.current.send(message);
            if (success) {
                updateState();
            }
            return success;
        }
        return false;
    }, [updateState]);

    const sendPing = useCallback(() => {
        return send({ action: 'ping' });
    }, [send]);

    const clearHistory = useCallback(() => {
        if (wsServiceRef.current) {
            wsServiceRef.current.clearMessageHistory();
            updateState();
        }
    }, [updateState]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (wsServiceRef.current) {
                wsServiceRef.current.disconnect();
            }
        };
    }, []);

    return {
        state,
        connect,
        disconnect,
        send,
        sendPing,
        clearHistory,
        isConnected: state.isConnected,
        isConnecting: state.isConnecting,
        error: state.error,
        lastMessage: state.lastMessage,
        messageHistory: state.messageHistory
    };
}; 