import * as SQLite from 'expo-sqlite';
import { databasePromise } from '../index';

export interface ScannedDocument {
    id: string;
    title: string;
    description?: string;
    filePath: string;
    fileType?: string;
    fileSize?: number;
    thumbnailPath?: string;
    projectId?: string;
    userId?: string;
    isSynchronized: boolean;
    createdAt: number;
    updatedAt: number;
}

export class DocumentRepository {
    private db: SQLite.SQLiteDatabase | null = null;

    constructor() {
        this.initialize();
    }

    private async initialize() {
        this.db = await databasePromise;
    }

    async getAllDocuments(options?: { projectId?: string, userId?: string }): Promise<ScannedDocument[]> {
        if (!this.db) await this.initialize();
        const query = 'SELECT * FROM scanned_documents';
        const results = await this.db!.getAllAsync<any>(query, []);
        return results.map(row => this.mapRowToDocument(row));
    }

    async add(document: Omit<ScannedDocument, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
        if (!this.db) await this.initialize();

        const id = this.generateUUID();
        const now = Date.now();

        const query = `
      INSERT INTO scanned_documents (
        id, title, description, file_path, file_type, file_size, 
        thumbnail_path, project_id, user_id, is_synchronized, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

        await this.db!.runAsync(query, [
            id,
            document.title,
            document.description || null,
            document.filePath,
            document.fileType || null,
            document.fileSize || null,
            document.thumbnailPath || null,
            document.projectId || null,
            document.userId || null,
            document.isSynchronized ? 1 : 0,
            now,
            now
        ]);

        return id;
    }

    async update(id: string, document: Partial<Omit<ScannedDocument, 'id' | 'createdAt' | 'updatedAt'>>): Promise<void> {
        if (!this.db) await this.initialize();

        const now = Date.now();
        const updateFields: string[] = [];
        const params: any[] = [];

        // Build update fields and parameters
        Object.entries(document).forEach(([key, value]) => {
            if (value !== undefined) {
                let fieldName = key;

                // Convert camelCase to snake_case for DB column names
                fieldName = fieldName.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);

                updateFields.push(`${fieldName} = ?`);

                // Handle boolean values
                if (typeof value === 'boolean') {
                    params.push(value ? 1 : 0);
                } else {
                    params.push(value);
                }
            }
        });

        // Always update the updated_at field
        updateFields.push('updated_at = ?');
        params.push(now);

        // Add the ID for the WHERE clause
        params.push(id);

        const query = `
      UPDATE scanned_documents 
      SET ${updateFields.join(', ')} 
      WHERE id = ?
    `;

        await this.db!.runAsync(query, params);
    }

    async getById(id: string): Promise<ScannedDocument | null> {
        if (!this.db) await this.initialize();

        const query = 'SELECT * FROM scanned_documents WHERE id = ?';
        const result = await this.db!.getFirstAsync<any>(query, [id]);

        if (!result) return null;

        return this.mapRowToDocument(result);
    }

    async getAll(options?: { projectId?: string, userId?: string }): Promise<ScannedDocument[]> {
        if (!this.db) await this.initialize();

        let query = 'SELECT * FROM scanned_documents';
        const params: any[] = [];

        // Apply filters if provided
        if (options) {
            const whereClauses: string[] = [];

            if (options.projectId) {
                whereClauses.push('project_id = ?');
                params.push(options.projectId);
            }

            if (options.userId) {
                whereClauses.push('user_id = ?');
                params.push(options.userId);
            }

            if (whereClauses.length > 0) {
                query += ' WHERE ' + whereClauses.join(' AND ');
            }
        }

        query += ' ORDER BY created_at DESC';

        const results = await this.db!.getAllAsync<any>(query, params);

        return results.map(row => this.mapRowToDocument(row));
    }

    async delete(id: string): Promise<void> {
        if (!this.db) await this.initialize();

        const query = 'DELETE FROM scanned_documents WHERE id = ?';
        await this.db!.runAsync(query, [id]);
    }

    async deleteByUserId(userId: string): Promise<void> {
        if (!this.db) await this.initialize();

        const query = 'DELETE FROM scanned_documents WHERE user_id = ?';
        await this.db!.runAsync(query, [userId]);
    }

    private mapRowToDocument(row: any): ScannedDocument {
        return {
            id: row.id,
            title: row.title,
            description: row.description,
            filePath: row.file_path,
            fileType: row.file_type,
            fileSize: row.file_size,
            thumbnailPath: row.thumbnail_path,
            projectId: row.project_id,
            userId: row.user_id,
            isSynchronized: Boolean(row.is_synchronized),
            createdAt: row.created_at,
            updatedAt: row.updated_at
        };
    }

    // Simple UUID generator
    private generateUUID(): string {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
} 