import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Text } from 'react-native-ui-lib';
import { LinearGradient } from 'expo-linear-gradient';
import LottieView from 'lottie-react-native';
import {
  COLORS,
  FONT_SIZES,
  FONT_WEIGHTS,
  SPACING,
} from '@core/constants/theme';

const { width, height } = Dimensions.get('window');

interface LottieSplashScreenProps {
  onAnimationFinish?: () => void;
}

export default function LottieSplashScreen({
  onAnimationFinish,
}: LottieSplashScreenProps) {
  const animationRef = useRef<LottieView>(null);

  useEffect(() => {
    // Auto play animation when component mounts
    if (animationRef.current) {
      animationRef.current.play();
    }
  }, []);

  const handleAnimationFinish = () => {
    // Call the callback after animation finishes
    if (onAnimationFinish) {
      setTimeout(onAnimationFinish, 500); // Small delay for smooth transition
    }
  };

  return (
    <LinearGradient
      colors={['#1E3A8A', '#3B82F6', '#60A5FA']}
      style={styles.container}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <View style={styles.content}>
        {/* Lottie Animation */}
        <View style={styles.animationContainer}>
          <LottieView
            ref={animationRef}
            source={require('@assets/lottie/splash_animation.json')}
            autoPlay
            loop={false}
            style={styles.animation}
            onAnimationFinish={handleAnimationFinish}
            resizeMode='contain'
          />
        </View>

        {/* App Info */}
        <View style={styles.textContainer}>
          <Text style={styles.title}>NTSOFT</Text>
          <Text style={styles.title}>Document Scanner</Text>
          <Text style={styles.subtitle}>
            Biến tài liệu giấy thành dữ liệu thông minh
          </Text>
        </View>

        {/* Loading Indicator */}
        <View style={styles.loadingContainer}>
          <LottieView
            source={require('@assets/lottie/loading.json')}
            autoPlay
            loop
            style={styles.loadingAnimation}
          />
          <Text style={styles.loadingText}>Đang khởi tạo...</Text>
        </View>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
  },
  animationContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    maxHeight: height * 0.5,
  },
  animation: {
    width: width * 0.8,
    height: width * 0.8,
    maxWidth: 400,
    maxHeight: 400,
  },
  textContainer: {
    alignItems: 'center',
    marginVertical: SPACING.xl,
  },
  title: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
    textAlign: 'center',
    marginBottom: SPACING.sm,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  subtitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.white,
    textAlign: 'center',
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  loadingContainer: {
    alignItems: 'center',
    marginBottom: SPACING.xxl,
  },
  loadingAnimation: {
    width: 60,
    height: 60,
  },
  loadingText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: COLORS.white,
    marginTop: SPACING.sm,
    opacity: 0.8,
  },
});
