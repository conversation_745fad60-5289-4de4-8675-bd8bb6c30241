import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { COLORS } from '@core/constants/theme';

export type NotificationType = 'basic' | 'premium';

interface NotificationConfig {
    title: string;
    body: string;
    data?: any;
    type?: NotificationType;
}

class NotificationService {
    private static instance: NotificationService;
    private isConfigured: boolean = false;

    private constructor() { }

    static getInstance(): NotificationService {
        if (!NotificationService.instance) {
            NotificationService.instance = new NotificationService();
        }
        return NotificationService.instance;
    }

    async configure() {
        if (this.isConfigured) return;

        await Notifications.setNotificationHandler({
            handleNotification: async () => ({
                shouldShowAlert: true,
                shouldPlaySound: true,
                shouldSetBadge: true,
            }),
        });

        if (Platform.OS === 'android') {
            await Notifications.setNotificationChannelAsync('default', {
                name: 'default',
                importance: Notifications.AndroidImportance.MAX,
                vibrationPattern: [0, 250, 250, 250],
                lightColor: COLORS.primary,
            });
        }

        this.isConfigured = true;
    }

    async requestPermissions() {
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;

        if (existingStatus !== 'granted') {
            const { status } = await Notifications.requestPermissionsAsync();
            finalStatus = status;
        }

        return finalStatus === 'granted';
    }

    async sendNotification(config: NotificationConfig) {
        if (!this.isConfigured) {
            await this.configure();
        }

        const hasPermission = await this.requestPermissions();
        if (!hasPermission) {
            throw new Error('Notification permissions not granted');
        }

        await Notifications.scheduleNotificationAsync({
            content: {
                title: config.title,
                body: config.body,
                data: config.data || {},
            },
            trigger: null, // Show immediately
        });
    }

    async cancelNotification(notificationId: string) {
        await Notifications.cancelScheduledNotificationAsync(notificationId);
    }

    // Hủy tất cả thông báo
    async cancelAllNotifications() {
        await Notifications.cancelAllScheduledNotificationsAsync();
    }

    // Lấy danh sách thông báo đã lên lịch
    async getScheduledNotifications() {
        return await Notifications.getAllScheduledNotificationsAsync();
    }
}

export { NotificationService }; 