// import { api } from '@core/client/axios';
// import { AUTH_ENDPOINTS } from '@features/auth/endpoints/auth';
// import AsyncStorage from '@react-native-async-storage/async-storage';

// // Types
// export interface LoginPayload {
//   TenDangNhap: string;
//   MatKhau: string;
// }

// export interface TokenResponse {
//   AccessToken: string;
//   RefreshToken: string;
//   ExpiresIn: number;
//   TokenType: string;
// }

// export interface LoginResponse extends TokenResponse {
//   userInfo: UserInfo;
// }

// export interface UserInfo {
//   UserId: string;
//   TenDangNhap: string;
//   DonViID: string;
//   TenDonVi: string;
// }

// // Helpers
// export const decodeJWT = (token: string): UserInfo => {
//   try {
//     const base64Url = token.split('.')[1];
//     const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
//     const jsonPayload = decodeURIComponent(atob(base64).split('').map(function (c) {
//       return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
//     }).join(''));

//     return JSON.parse(jsonPayload);
//   } catch (error) {
//     console.error('Error decoding JWT:', error);
//     return {} as UserInfo;
//   }
// };

// // Service functions
// export const authService = {
//   login: async (payload: LoginPayload): Promise<LoginResponse> => {
//     console.log('Login data:', payload);

//     // Sử dụng body thay vì query parameters
//     const { data } = await api.post<TokenResponse>(AUTH_ENDPOINTS.LOGIN, payload);
//     console.log('Login Response:', data);

//     // Store tokens
//     await AsyncStorage.setItem('accessToken', data.AccessToken);
//     await AsyncStorage.setItem('refreshToken', data.RefreshToken);
//     await AsyncStorage.setItem('tokenExpiresIn', data.ExpiresIn.toString());
//     await AsyncStorage.setItem('tokenType', data.TokenType);

//     // Set default authorization header for future requests
//     api.defaults.headers.common['Authorization'] = `${data.TokenType} ${data.AccessToken}`;

//     // Decode token to get user info
//     const userInfo = decodeJWT(data.AccessToken);
//     console.log('✅ Login thành công:', userInfo);

//     return {
//       AccessToken: data.AccessToken,
//       RefreshToken: data.RefreshToken,
//       ExpiresIn: data.ExpiresIn,
//       TokenType: data.TokenType,
//       userInfo
//     };
//   },

//   logout: async (): Promise<void> => {
//     // Remove tokens from storage
//     await AsyncStorage.removeItem('accessToken');
//     await AsyncStorage.removeItem('refreshToken');
//     await AsyncStorage.removeItem('tokenExpiresIn');
//     await AsyncStorage.removeItem('tokenType');

//     // Clear authorization header
//     delete api.defaults.headers.common['Authorization'];
//   },

//   refreshToken: async (): Promise<string | null> => {
//     try {
//       const refreshToken = await AsyncStorage.getItem('refreshToken');
//       if (!refreshToken) return null;

//       // Call refresh token endpoint
//       const { data } = await api.post<TokenResponse>(AUTH_ENDPOINTS.REFRESH_TOKEN, {
//         refreshToken
//       });

//       // Update tokens in storage
//       await AsyncStorage.setItem('accessToken', data.AccessToken);
//       await AsyncStorage.setItem('refreshToken', data.RefreshToken);
//       await AsyncStorage.setItem('tokenExpiresIn', data.ExpiresIn.toString());

//       // Update auth header
//       api.defaults.headers.common['Authorization'] = `${data.TokenType} ${data.AccessToken}`;

//       return data.AccessToken;
//     } catch (error) {
//       console.error('Failed to refresh token:', error);
//       return null;
//     }
//   },

//   getProfile: async (): Promise<UserInfo | null> => {
//     try {
//       const token = await AsyncStorage.getItem('accessToken');
//       if (!token) return null;

//       return decodeJWT(token);
//     } catch (error) {
//       console.error('Failed to get profile:', error);
//       return null;
//     }
//   }
// };
import { api } from '@core/client/axios';
import { AUTH_ENDPOINTS } from '@features/auth/endpoints/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Buffer } from 'buffer';

// Types
export interface LoginPayload {
  TenDangNhap: string;
  MatKhau: string;
}

export interface TokenResponse {
  AccessToken: string;
  RefreshToken: string;
  ExpiresIn: number;
  TokenType: string;
}

export interface LoginResponse extends TokenResponse {
  userInfo: UserInfo;
}

export interface UserInfo {
  UserId: string;
  TenDangNhap: string;
  DonViID: string;
  TenDonVi: string;
}

// Helper: Decode JWT payload
export const decodeJWT = (token: string): UserInfo => {
  try {
    const base64Payload = token.split('.')[1];
    const jsonPayload = Buffer.from(base64Payload, 'base64').toString('utf-8');
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('❌ Lỗi decode JWT:', error);
    return {} as UserInfo;
  }
};

// Helper: Set Authorization header
const setAuthHeader = (type: string, token: string) => {
  api.defaults.headers.common['Authorization'] = `${type} ${token}`;
};

// Helper: Save tokens
const saveTokens = async (data: TokenResponse) => {
  await Promise.all([
    AsyncStorage.setItem('accessToken', data.AccessToken),
    AsyncStorage.setItem('refreshToken', data.RefreshToken),
    AsyncStorage.setItem('tokenExpiresIn', data.ExpiresIn.toString()),
    AsyncStorage.setItem('tokenType', data.TokenType),
  ]);
  setAuthHeader(data.TokenType, data.AccessToken);
};

// Service
export const authService = {
  login: async (payload: LoginPayload): Promise<LoginResponse> => {
    const { data } = await api.post<TokenResponse>(AUTH_ENDPOINTS.LOGIN, payload);
    await saveTokens(data);

    const userInfo = decodeJWT(data.AccessToken);
    console.log('✅ Login thành công:', userInfo);

    return { ...data, userInfo };
  },

  logout: async (): Promise<void> => {
    await Promise.all([
      AsyncStorage.removeItem('accessToken'),
      AsyncStorage.removeItem('refreshToken'),
      AsyncStorage.removeItem('tokenExpiresIn'),
      AsyncStorage.removeItem('tokenType'),
    ]);

    if (api.defaults.headers.common['Authorization']) {
      delete api.defaults.headers.common['Authorization'];
    }
  },

  refreshToken: async (): Promise<string | null> => {
    try {
      const refreshToken = await AsyncStorage.getItem('refreshToken');
      if (!refreshToken) return null;

      const { data } = await api.post<TokenResponse>(AUTH_ENDPOINTS.REFRESH_TOKEN, { refreshToken });
      await saveTokens(data);
      return data.AccessToken;
    } catch (error) {
      console.error('❌ Failed to refresh token:', error);
      return null;
    }
  },

  getProfile: async (): Promise<UserInfo | null> => {
    try {
      const token = await AsyncStorage.getItem('accessToken');
      return token ? decodeJWT(token) : null;
    } catch (error) {
      console.error('❌ Failed to get profile:', error);
      return null;
    }
  },
};