import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useWebSocket } from '../hooks/useWebSocket';
import { WebSocketMessage } from '../types';

const WebSocketTestScreen: React.FC = () => {
  const [serverUrl, setServerUrl] = useState('ws://192.168.2.38:8765');
  const [customMessage, setCustomMessage] = useState('');
  const [autoConnect, setAutoConnect] = useState(false);

  const {
    state,
    connect,
    disconnect,
    send,
    sendPing,
    clearHistory,
    isConnected,
    isConnecting,
    error,
    lastMessage,
    messageHistory,
  } = useWebSocket(
    {
      url: serverUrl,
      reconnectInterval: 1000,
      maxReconnectAttempts: 5,
    },
    {
      onOpen: () => {
        console.log('WebSocket connected successfully');
      },
      onClose: () => {
        console.log('WebSocket disconnected');
      },
      onError: error => {
        console.error('WebSocket error:', error);
        Alert.alert('Lỗi kết nối', 'Không thể kết nối đến máy chủ WebSocket');
      },
      onMessage: message => {
        console.log('Received message:', message);
      },
    },
  );

  const handleConnect = async () => {
    try {
      await connect();
    } catch (error) {
      Alert.alert('Lỗi', 'Không thể kết nối đến máy chủ WebSocket');
    }
  };

  const handleDisconnect = () => {
    disconnect();
  };

  const handleSendPing = () => {
    const success = sendPing();
    if (!success) {
      Alert.alert('Lỗi', 'Không thể gửi tin nhắn ping');
    }
  };

  const handleSendCustomMessage = () => {
    if (!customMessage.trim()) {
      Alert.alert('Lỗi', 'Vui lòng nhập tin nhắn');
      return;
    }

    const message: WebSocketMessage = {
      action: 'custom',
      data: customMessage,
      timestamp: Date.now(),
    };

    const success = send(message);
    if (success) {
      setCustomMessage('');
    } else {
      Alert.alert('Lỗi', 'Không thể gửi tin nhắn');
    }
  };

  const handleFillInput = () => {
    if (!customMessage.trim()) {
      Alert.alert(
        'Lỗi',
        'Vui lòng nhập ID input và giá trị (định dạng: id:value)',
      );
      return;
    }

    const parts = customMessage.split(':');
    if (parts.length !== 2) {
      Alert.alert('Lỗi', 'Vui lòng sử dụng định dạng: input_id:value');
      return;
    }

    const inputId = parts[0].trim();
    const value = parts[1].trim();

    const message: WebSocketMessage = {
      action: 'fill_input',
      data: {
        input_id: inputId,
        value: value,
      },
      timestamp: Date.now(),
    };

    const success = send(message);
    if (success) {
      setCustomMessage('');
    } else {
      Alert.alert('Lỗi', 'Không thể gửi tin nhắn fill_input');
    }
  };

  const handleGetInputs = () => {
    const message: WebSocketMessage = {
      action: 'get_inputs',
      data: {},
      timestamp: Date.now(),
    };

    const success = send(message);
    if (!success) {
      Alert.alert('Lỗi', 'Không thể gửi tin nhắn get_inputs');
    }
  };

  const handleGetStatus = () => {
    const message: WebSocketMessage = {
      action: 'get_status',
      data: {},
      timestamp: Date.now(),
    };

    const success = send(message);
    if (!success) {
      Alert.alert('Lỗi', 'Không thể gửi tin nhắn get_status');
    }
  };

  const handleExportData = () => {
    const message: WebSocketMessage = {
      action: 'export_data',
      data: {},
      timestamp: Date.now(),
    };

    const success = send(message);
    if (!success) {
      Alert.alert('Lỗi', 'Không thể gửi tin nhắn export_data');
    }
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getConnectionStatusColor = () => {
    if (isConnecting) return '#FFA500';
    if (isConnected) return '#4CAF50';
    return '#F44336';
  };

  const getConnectionStatusText = () => {
    if (isConnecting) return 'Đang kết nối...';
    if (isConnected) return 'Đã kết nối';
    return 'Chưa kết nối';
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Kiểm tra WebSocket</Text>
          <View style={styles.statusContainer}>
            <View
              style={[
                styles.statusIndicator,
                { backgroundColor: getConnectionStatusColor() },
              ]}
            />
            <Text style={styles.statusText}>{getConnectionStatusText()}</Text>
          </View>
        </View>

        {/* Server Configuration */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Cấu hình máy chủ</Text>
          <TextInput
            style={styles.input}
            value={serverUrl}
            onChangeText={setServerUrl}
            placeholder='URL WebSocket'
            placeholderTextColor='#666'
          />
        </View>

        {/* Connection Controls */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Kết nối</Text>
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[
                styles.button,
                styles.connectButton,
                isConnected && styles.disabledButton,
              ]}
              onPress={handleConnect}
              disabled={isConnected || isConnecting}
            >
              {isConnecting ? (
                <ActivityIndicator color='#fff' size='small' />
              ) : (
                <Text style={styles.buttonText}>Kết nối</Text>
              )}
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.button,
                styles.disconnectButton,
                !isConnected && styles.disabledButton,
              ]}
              onPress={handleDisconnect}
              disabled={!isConnected}
            >
              <Text style={styles.buttonText}>Ngắt kết nối</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Error Display */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Lỗi</Text>
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error || 'Không có lỗi'}</Text>
          </View>
        </View>

        {/* Message Controls */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Gửi tin nhắn</Text>
          <TouchableOpacity
            style={[
              styles.button,
              styles.pingButton,
              !isConnected && styles.disabledButton,
            ]}
            onPress={handleSendPing}
            disabled={!isConnected}
          >
            <Text style={styles.buttonText}>Ping</Text>
          </TouchableOpacity>

          <View style={styles.messageInputContainer}>
            <TextInput
              style={styles.input}
              value={customMessage}
              onChangeText={setCustomMessage}
              placeholder='Định dạng 1: input_id:value (cho nút Fill Input) | Định dạng 2: Tin nhắn JSON (cho nút Send)'
              placeholderTextColor='#666'
              multiline
              numberOfLines={3}
              returnKeyType='done'
              autoCapitalize='none'
              autoCorrect={false}
            />
            <TouchableOpacity
              style={[
                styles.button,
                styles.sendButton,
                !isConnected && styles.disabledButton,
              ]}
              onPress={handleSendCustomMessage}
              disabled={!isConnected}
            >
              <Text style={styles.buttonText}>Gửi</Text>
            </TouchableOpacity>
          </View>

          {/* Additional WebSocket Actions */}
          <View style={styles.actionButtonsContainer}>
            <TouchableOpacity
              style={[
                styles.button,
                styles.fillButton,
                !isConnected && styles.disabledButton,
              ]}
              onPress={handleFillInput}
              disabled={!isConnected}
            >
              <Text style={styles.buttonText}>Điền Input</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.button,
                styles.actionButton,
                !isConnected && styles.disabledButton,
              ]}
              onPress={handleGetInputs}
              disabled={!isConnected}
            >
              <Text style={styles.buttonText}>Lấy Inputs</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.button,
                styles.actionButton,
                !isConnected && styles.disabledButton,
              ]}
              onPress={handleGetStatus}
              disabled={!isConnected}
            >
              <Text style={styles.buttonText}>Trạng thái</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.button,
                styles.actionButton,
                !isConnected && styles.disabledButton,
              ]}
              onPress={handleExportData}
              disabled={!isConnected}
            >
              <Text style={styles.buttonText}>Xuất dữ liệu</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Last Message */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tin nhắn cuối</Text>
          <View style={styles.messageContainer}>
            <Text style={styles.messageText}>
              {lastMessage
                ? JSON.stringify(lastMessage, null, 2)
                : 'Chưa có tin nhắn'}
            </Text>
          </View>
        </View>

        {/* Message History */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Lịch sử tin nhắn</Text>
          <View style={styles.historyHeader}>
            <TouchableOpacity style={styles.clearButton} onPress={clearHistory}>
              <Text style={styles.clearButtonText}>Xóa</Text>
            </TouchableOpacity>
          </View>

          {messageHistory.length === 0 ? (
            <View style={styles.emptyHistory}>
              <Text style={styles.emptyText}>No messages yet</Text>
            </View>
          ) : (
            <ScrollView style={styles.historyContainer} nestedScrollEnabled>
              {messageHistory
                .slice()
                .reverse()
                .map((message, index) => (
                  <View key={index} style={styles.historyItem}>
                    <Text style={styles.historyAction}>
                      Action: {message.action}
                    </Text>
                    {message.data && (
                      <Text style={styles.historyData}>
                        Data: {JSON.stringify(message.data)}
                      </Text>
                    )}
                    {message.timestamp && (
                      <Text style={styles.historyTimestamp}>
                        {formatTimestamp(message.timestamp)}
                      </Text>
                    )}
                  </View>
                ))}
            </ScrollView>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    color: '#666',
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  button: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  connectButton: {
    backgroundColor: '#4CAF50',
  },
  disconnectButton: {
    backgroundColor: '#F44336',
  },
  pingButton: {
    backgroundColor: '#2196F3',
    marginBottom: 12,
  },
  sendButton: {
    backgroundColor: '#9C27B0',
    paddingHorizontal: 20,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#F44336',
  },
  errorText: {
    color: '#c62828',
    fontSize: 14,
  },
  messageInputContainer: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'flex-end',
  },
  messageInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
    minHeight: 44,
    maxHeight: 100,
  },
  messageContainer: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  messageText: {
    fontSize: 14,
    color: '#666',
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#ff9800',
    borderRadius: 6,
  },
  clearButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  emptyHistory: {
    padding: 24,
    alignItems: 'center',
  },
  emptyText: {
    color: '#999',
    fontSize: 16,
  },
  historyContainer: {
    maxHeight: 300,
  },
  historyItem: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#4CAF50',
  },
  historyAction: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  historyData: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  historyTimestamp: {
    fontSize: 10,
    color: '#999',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 12,
  },
  actionButton: {
    backgroundColor: '#17a2b8',
    flex: 1,
    minWidth: 80,
  },
  fillButton: {
    backgroundColor: '#9C27B0',
    paddingHorizontal: 20,
  },
});

export default WebSocketTestScreen;
