import { useState, useCallback } from 'react';
import { ScanService } from '../services/scanService';
import { ScannedDocument } from '@core/database/repositories/documentRepository';

/**
 * Custom hook for scan document operations
 */
export const useScanService = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);
    const scanService = ScanService.getInstance();

    /**
     * Save a scanned document to the database
     */
    const saveDocument = useCallback(async (
        imageUri: string,
        title: string,
        description?: string,
        projectId?: string,
        userId?: string
    ) => {
        setLoading(true);
        setError(null);

        try {
            const docId = await scanService.saveScannedDocument(
                imageUri,
                title,
                description,
                projectId,
                userId
            );
            setLoading(false);
            return docId;
        } catch (err) {
            const error = err instanceof Error ? err : new Error('Unknown error occurred');
            setError(error);
            setLoading(false);
            throw error;
        }
    }, []);

    /**
     * Get all documents from the database
     */
    const getDocuments = useCallback(async (
        options?: { projectId?: string, userId?: string }
    ): Promise<ScannedDocument[]> => {
        setLoading(true);
        setError(null);

        try {
            const docs = await scanService.getAllDocuments(options);
            setLoading(false);
            return docs;
        } catch (err) {
            const error = err instanceof Error ? err : new Error('Unknown error occurred');
            setError(error);
            setLoading(false);
            throw error;
        }
    }, []);

    /**
     * Get a document by ID
     */
    const getDocumentById = useCallback(async (id: string): Promise<ScannedDocument | null> => {
        setLoading(true);
        setError(null);

        try {
            const doc = await scanService.getDocumentById(id);
            setLoading(false);
            return doc;
        } catch (err) {
            const error = err instanceof Error ? err : new Error('Unknown error occurred');
            setError(error);
            setLoading(false);
            throw error;
        }
    }, []);

    /**
     * Delete a document by ID
     */
    const deleteDocument = useCallback(async (id: string): Promise<void> => {
        setLoading(true);
        setError(null);

        try {
            await scanService.deleteDocument(id);
            setLoading(false);
        } catch (err) {
            const error = err instanceof Error ? err : new Error('Unknown error occurred');
            setError(error);
            setLoading(false);
            throw error;
        }
    }, []);

    return {
        loading,
        error,
        saveDocument,
        getDocuments,
        getDocumentById,
        deleteDocument
    };
}; 