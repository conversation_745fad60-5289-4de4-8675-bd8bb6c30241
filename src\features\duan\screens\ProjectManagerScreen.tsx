import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  StatusBar,
  TextInput,
  RefreshControl,
  SectionList,
  Animated,
  Platform,
  Dimensions,
  Image,
  Easing,
  Alert,
} from 'react-native';

import * as Haptics from 'expo-haptics';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useDuAnQuery } from '@features/duan/hooks/useDuAnQuery';
import { DuAn } from '@features/duan/services/duanService';
import { MMKV } from 'react-native-mmkv';
import { useProfile } from '@features/auth/hooks/useAuth';
import { projectStorage } from '@core/storage';
const { width, height, scale } = Dimensions.get('window');

// <PERSON><PERSON>u cho các dự án theo chữ cái đầu - modernized colors
const PROJECT_COLORS = [
  '#4361ee', // A
  '#4cc9f0', // B
  '#f72585', // C
  '#480ca8', // D
  '#f77f00', // E
  '#7209b7', // F
  '#4cc9f0', // G
  '#f72585', // H
  '#4895ef', // I
  '#4361ee', // J
  '#b5179e', // K
  '#4895ef', // L
  '#3f37c9', // M
  '#f72585', // N
  '#560bad', // O
  '#4cc9f0', // P
  '#fcbf49', // Q
  '#7209b7', // R
  '#4895ef', // S
  '#3a0ca3', // T
  '#b5179e', // U
  '#4895ef', // V
  '#f77f00', // W
  '#4361ee', // X
  '#7209b7', // Y
  '#3a0ca3', // Z
];

const MAX_RECENT_PROJECTS = 3;

const ProjectManagerScreen = () => {
  const router = useRouter();
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(
    null,
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [recentProjects, setRecentProjects] = useState<DuAn[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [showAllProjects, setShowAllProjects] = useState(false);

  // Animations
  const fadeAnim = useState(new Animated.Value(0))[0];
  const scaleAnim = useState(new Animated.Value(0.95))[0];
  const searchAnim = useState(new Animated.Value(0))[0];
  const scrollYAnim = useState(new Animated.Value(0))[0];

  // Lấy thông tin người dùng đăng nhập
  const { data: userProfile, isLoading: isLoadingProfile } = useProfile();

  // Query params - lấy donViId từ profile người dùng
  const donViId = userProfile?.DonViID;

  // Fetch projects
  const {
    data: projectsResponse,
    isLoading: isLoadingProjects,
    error: projectsError,
    refetch: refetchProjects,
  } = useDuAnQuery({
    donViId,
    enabled: !!donViId,
  });

  // Header animation
  const headerHeight = scrollYAnim.interpolate({
    inputRange: [0, 120],
    outputRange: [200, 70],
    extrapolate: 'clamp',
  });

  const headerOpacity = scrollYAnim.interpolate({
    inputRange: [0, 80, 120],
    outputRange: [1, 0.5, 0],
    extrapolate: 'clamp',
  });

  const headerTitleOpacity = scrollYAnim.interpolate({
    inputRange: [0, 80, 120],
    outputRange: [0, 0.5, 1],
    extrapolate: 'clamp',
  });

  // Lấy projectId đã lưu khi component mount
  useEffect(() => {
    const savedProjectId = projectStorage.getDefaultProject();
    if (savedProjectId) {
      setSelectedProjectId(savedProjectId);
    }

    // Lấy danh sách dự án gần đây
    const recentProjectsList = projectStorage.getRecentProjects();
    if (recentProjectsList.length > 0) {
      setRecentProjects(recentProjectsList);
    }

    // Animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(searchAnim, {
        toValue: 1,
        duration: 800,
        delay: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Lọc và tối ưu hoá danh sách dự án
  const { filteredProjects, sectionedProjects } = useMemo(() => {
    const allProjects = projectsResponse?.Items || [];

    // Lọc theo từ khoá tìm kiếm
    const filtered = allProjects.filter(
      project =>
        project.TenDuAn?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (project.MaDuAn &&
          project.MaDuAn.toLowerCase().includes(searchQuery.toLowerCase())),
    );

    // Nếu đang tìm kiếm, không phân chia phần mới dùng gần đây
    if (searchQuery) {
      return {
        filteredProjects: filtered,
        sectionedProjects: [
          {
            title: '',
            data: filtered,
          },
        ],
      };
    }

    // Lấy danh sách các dự án đã chọn gần đây
    const recentProjectIds = recentProjects.map(p => p.DuAnId);
    const recentProjectsData = allProjects.filter(p =>
      recentProjectIds.includes(p.DuAnId),
    );

    // Lấy các dự án khác
    const otherProjects = allProjects.filter(
      p => !recentProjectIds.includes(p.DuAnId),
    );

    // Chỉ hiển thị tối đa 10 dự án nếu không chọn xem tất cả
    const displayedOtherProjects = showAllProjects
      ? otherProjects
      : otherProjects.slice(0, 10);

    // Tạo dữ liệu phân đoạn
    const sectioned = [];

    if (recentProjectsData.length > 0) {
      sectioned.push({
        title: 'Đã sử dụng gần đây',
        data: recentProjectsData,
      });
    }

    sectioned.push({
      title: 'Tất cả dự án',
      data: displayedOtherProjects,
    });

    return {
      filteredProjects: filtered,
      sectionedProjects: sectioned,
    };
  }, [projectsResponse, searchQuery, recentProjects, showAllProjects]);

  // Cập nhật danh sách dự án đã sử dụng gần đây
  const updateRecentProjects = useCallback((project: DuAn) => {
    const updated = projectStorage.addRecentProject(
      project,
      MAX_RECENT_PROJECTS,
    );
    setRecentProjects(updated);
  }, []);

  // Lưu projectId khi người dùng chọn
  const handleSelectProject = useCallback(
    (project: DuAn) => {
      if (!project || !project.DuAnId) {
        console.error('Không thể chọn dự án: ID không hợp lệ');
        return;
      }

      setSelectedProjectId(project.DuAnId);
      projectStorage.setDefaultProject(project.DuAnId);
      updateRecentProjects(project);

      // Note: We could add haptic feedback here if expo-haptics is available
    },
    [updateRecentProjects],
  );

  // Xử lý làm mới danh sách
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetchProjects();
    setRefreshing(false);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, [refetchProjects]);

  // Lấy màu dựa vào tên dự án
  const getProjectColor = useCallback((projectName: string) => {
    if (!projectName) return PROJECT_COLORS[0];

    const firstChar = projectName.charAt(0).toUpperCase();
    const index = firstChar.charCodeAt(0) - 'A'.charCodeAt(0);

    if (index >= 0 && index < 26) {
      return PROJECT_COLORS[index];
    }
    return PROJECT_COLORS[0];
  }, []);

  // Lấy chữ cái đầu của tên dự án
  const getProjectInitial = useCallback((projectName: string) => {
    if (!projectName) return '?';
    return projectName.charAt(0).toUpperCase();
  }, []);

  // Render mỗi item dự án
  const renderProjectItem = useCallback(
    ({ item, index, section }: { item: DuAn; index: number; section: any }) => {
      const isSelected = item.DuAnId === selectedProjectId;
      const projectColor = getProjectColor(item.TenDuAn || '');
      const animationDelay = index * 80;

      // Tạo animation cho từng item
      const itemAnimatedStyle = {
        opacity: fadeAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0, 1],
        }),
        transform: [
          {
            translateY: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [20, 0],
            }),
          },
          {
            scale: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0.9, 1],
            }),
          },
        ],
      };

      return (
        <Animated.View style={[styles.projectItemContainer, itemAnimatedStyle]}>
          <TouchableOpacity
            style={[styles.projectItem, isSelected && styles.selectedItem]}
            onPress={() => handleSelectProject(item)}
            activeOpacity={0.7}
            onLongPress={() => {
              // Hiển thị tên đầy đủ khi nhấn giữ
              Alert.alert(
                'Tên dự án đầy đủ',
                item.TenDuAn,
                [{ text: 'Đóng', style: 'cancel' }],
                { cancelable: true },
              );
            }}
          >
            <View
              style={[styles.projectIcon, { backgroundColor: projectColor }]}
            >
              <Text style={styles.projectInitial}>
                {getProjectInitial(item.TenDuAn || '')}
              </Text>
            </View>
            <View style={styles.projectContent}>
              <Text style={styles.projectTitle} numberOfLines={2}>
                {item.TenDuAn}
              </Text>

              {/* Hiển thị chỉ báo với tooltip khi tên quá dài */}
              {item.TenDuAn && item.TenDuAn.length > 40 && (
                <TouchableOpacity
                  style={styles.viewMoreButton}
                  onPress={() => {
                    Alert.alert(
                      'Tên dự án đầy đủ',
                      item.TenDuAn,
                      [{ text: 'Đóng', style: 'cancel' }],
                      { cancelable: true },
                    );
                  }}
                >
                  <Text style={styles.viewMoreText}>Xem đầy đủ</Text>
                </TouchableOpacity>
              )}

              {item.MaDuAn ? (
                <Text style={styles.projectCode}>Mã dự án: {item.MaDuAn}</Text>
              ) : null}
            </View>
            {isSelected && (
              <View style={styles.checkmarkContainer}>
                <View
                  style={[
                    styles.checkmarkCircle,
                    { backgroundColor: projectColor },
                  ]}
                >
                  <Ionicons name='checkmark' size={16} color='#fff' />
                </View>
              </View>
            )}
          </TouchableOpacity>
        </Animated.View>
      );
    },
    [
      selectedProjectId,
      getProjectColor,
      getProjectInitial,
      handleSelectProject,
      fadeAnim,
    ],
  );

  // Render header cho mỗi section
  const renderSectionHeader = useCallback(
    ({ section }: { section: { title: string } }) => {
      if (!section.title) return null;

      return (
        <Animated.View
          style={[
            styles.sectionHeader,
            {
              opacity: fadeAnim,
              transform: [
                {
                  translateY: fadeAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [20, 0],
                  }),
                },
              ],
            },
          ]}
        >
          <View style={styles.sectionHeaderContent}>
            <Text style={styles.sectionHeaderText}>{section.title}</Text>

            {section.title === 'Đã sử dụng gần đây' && (
              <MaterialCommunityIcons
                name='clock-time-four-outline'
                size={18}
                color='#666'
              />
            )}

            {section.title === 'Tất cả dự án' && (
              <MaterialCommunityIcons
                name='folder-multiple-outline'
                size={18}
                color='#666'
              />
            )}
          </View>
        </Animated.View>
      );
    },
    [fadeAnim],
  );

  // Render footer cho phần "Tất cả dự án"
  const renderSectionFooter = useCallback(
    ({ section }: { section: { title: string; data: any[] } }) => {
      const allProjects = projectsResponse?.Items || [];
      const displayedCount = section.data.length;
      const totalCount = allProjects.length;

      if (
        section.title === 'Tất cả dự án' &&
        !searchQuery &&
        !showAllProjects &&
        totalCount > 10
      ) {
        return (
          <TouchableOpacity
            style={styles.showMoreButton}
            onPress={() => setShowAllProjects(true)}
          >
            <Text style={styles.showMoreText}>
              Xem thêm {totalCount - displayedCount} dự án
            </Text>
            <Ionicons name='chevron-down' size={16} color='#4361ee' />
          </TouchableOpacity>
        );
      }
      return null;
    },
    [projectsResponse, searchQuery, showAllProjects],
  );

  // Hiển thị danh sách trống
  const renderEmptyComponent = useCallback(() => {
    return (
      <View style={styles.emptyContainer}>
        <View style={styles.emptyIconContainer}>
          <MaterialCommunityIcons
            name='folder-open-outline'
            size={80}
            color='#ddd'
          />
        </View>
        <Text style={styles.emptyText}>
          {searchQuery ? 'Không tìm thấy dự án phù hợp' : 'Không có dự án nào'}
        </Text>
        <Text style={styles.emptySubtext}>
          {searchQuery
            ? 'Vui lòng thử từ khóa khác.'
            : 'Hãy liên hệ quản trị viên để thêm dự án mới.'}
        </Text>
        {searchQuery && (
          <TouchableOpacity
            style={styles.clearSearchButton}
            onPress={() => setSearchQuery('')}
          >
            <Text style={styles.clearSearchText}>Xóa tìm kiếm</Text>
          </TouchableOpacity>
        )}
        {!searchQuery && (
          <TouchableOpacity
            style={styles.addNewButton}
            onPress={() => {
              Alert.alert(
                'Thông báo',
                'Chức năng thêm dự án sẽ được cập nhật trong phiên bản tới!',
              );
            }}
          >
            <Ionicons
              name='add-circle-outline'
              size={20}
              color='white'
              style={styles.addNewIcon}
            />
            <Text style={styles.addNewText}>Thêm dự án mới</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }, [searchQuery, filteredProjects]);

  // Hiển thị ListFooterComponent
  const renderListFooter = useCallback(() => {
    return <View style={styles.listFooter} />;
  }, []);

  // Render skeleton loading
  const renderSkeletonLoading = useCallback(() => {
    const skeletons = Array(7).fill(0);

    return (
      <View style={styles.skeletonContainer}>
        {skeletons.map((_, index) => (
          <Animated.View
            key={index}
            style={[
              styles.skeletonItem,
              {
                opacity: fadeAnim,
                transform: [
                  {
                    translateY: fadeAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [index * 10 + 20, 0],
                    }),
                  },
                ],
              },
            ]}
          >
            <View style={styles.skeletonIcon} />
            <View style={styles.skeletonContent}>
              <View style={styles.skeletonTitle} />
              <View style={styles.skeletonSubtitle} />
            </View>
          </Animated.View>
        ))}
      </View>
    );
  }, [fadeAnim]);

  // Hiển thị header với animation khi scroll
  const renderAnimatedHeader = () => {
    return (
      <Animated.View
        style={[
          styles.animatedHeaderContainer,
          {
            height: headerHeight,
          },
        ]}
      >
        {/* Header background */}
        <Animated.View
          style={[
            styles.headerBackground,
            {
              opacity: headerOpacity,
            },
          ]}
        >
          <View style={styles.headerGradient}>
            <View style={styles.headerContent}>
              <Text style={styles.bannerTitle}>Quản lý dự án</Text>
              <Text style={styles.bannerDescription}>
                Chọn dự án làm mặc định để sử dụng khi quét tài liệu
              </Text>
              <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>
                    {projectsResponse?.TotalCount || 0}
                  </Text>
                  <Text style={styles.statLabel}>Tổng dự án</Text>
                </View>
                {recentProjects.length > 0 && (
                  <View style={styles.statItem}>
                    <Text style={styles.statNumber}>
                      {recentProjects.length}
                    </Text>
                    <Text style={styles.statLabel}>Đã dùng gần đây</Text>
                  </View>
                )}
              </View>
            </View>
            <View style={styles.bannerImageContainer}>
              <MaterialCommunityIcons
                name='office-building'
                size={48}
                color='#fff'
              />
            </View>
          </View>
        </Animated.View>

        {/* Header title khi scroll */}
        <Animated.View
          style={[
            styles.floatingHeader,
            {
              opacity: headerTitleOpacity,
            },
          ]}
        >
          <Text style={styles.floatingHeaderTitle}>Quản lý dự án</Text>
          {selectedProjectId &&
            projectsResponse?.Items?.find(p => p.DuAnId === selectedProjectId)
              ?.TenDuAn && (
              <Text style={styles.selectedProjectName} numberOfLines={1}>
                Đã chọn:{' '}
                {
                  projectsResponse?.Items?.find(
                    p => p.DuAnId === selectedProjectId,
                  )?.TenDuAn
                }
              </Text>
            )}
        </Animated.View>
      </Animated.View>
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar barStyle='dark-content' backgroundColor='#fff' />

      {/* Fixed Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name='arrow-back' size={24} color='#333' />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Quản lý dự án</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }}
          >
            <Ionicons name='add' size={20} color='#4361ee' />
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.refreshButton,
              isLoadingProjects && styles.refreshingButton,
            ]}
            onPress={() => refetchProjects()}
            disabled={isLoadingProjects}
          >
            <Ionicons
              name={isLoadingProjects ? 'sync' : 'refresh'}
              size={22}
              color={isLoadingProjects ? '#999' : '#4361ee'}
            />
          </TouchableOpacity>
        </View>
      </View>

      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        {/* Search Box */}
        <Animated.View
          style={[
            styles.searchContainer,
            {
              opacity: searchAnim,
              transform: [
                {
                  translateY: searchAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [10, 0],
                  }),
                },
              ],
            },
          ]}
        >
          <View style={styles.searchInputContainer}>
            <Ionicons
              name='search'
              size={20}
              color='#999'
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder='Tìm kiếm dự án...'
              placeholderTextColor='#999'
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={() => setSearchQuery('')}
              >
                <Ionicons name='close-circle' size={18} color='#999' />
              </TouchableOpacity>
            )}
          </View>
          {searchQuery.length > 0 && filteredProjects.length > 0 && (
            <Text style={styles.searchResults}>
              Tìm thấy {filteredProjects.length} dự án
            </Text>
          )}
        </Animated.View>

        {/* Content */}
        {isLoadingProjects && !refreshing ? (
          renderSkeletonLoading()
        ) : projectsError ? (
          <View style={styles.errorContainer}>
            <MaterialCommunityIcons
              name='alert-circle-outline'
              size={80}
              color='#f44336'
            />
            <Text style={styles.errorText}>
              Không thể tải danh sách dự án. Vui lòng thử lại sau.
            </Text>
            <Text style={styles.errorDetail}>
              {(projectsError as Error).message}
            </Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => refetchProjects()}
            >
              <Text style={styles.retryButtonText}>Thử lại</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <SectionList
            sections={sectionedProjects}
            keyExtractor={item => item.DuAnId}
            renderItem={renderProjectItem}
            renderSectionHeader={renderSectionHeader}
            renderSectionFooter={renderSectionFooter}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={['#4361ee']}
                tintColor='#4361ee'
              />
            }
            ListHeaderComponent={renderAnimatedHeader}
            ListEmptyComponent={renderEmptyComponent}
            ListFooterComponent={renderListFooter}
            stickySectionHeadersEnabled={false}
            removeClippedSubviews={Platform.OS === 'android'}
            initialNumToRender={8}
            maxToRenderPerBatch={10}
            windowSize={10}
            onScroll={Animated.event(
              [{ nativeEvent: { contentOffset: { y: scrollYAnim } } }],
              { useNativeDriver: false },
            )}
            scrollEventThrottle={16}
          />
        )}
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eaedf2',
    zIndex: 10,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f6fa',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f6fa',
    marginRight: 8,
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f6fa',
  },
  refreshingButton: {
    backgroundColor: '#eaecf5',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  animatedHeaderContainer: {
    overflow: 'hidden',
    marginBottom: 16,
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  headerGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4361ee',
    borderRadius: 16,
    padding: 20,
    height: '100%',
    overflow: 'hidden',
  },
  headerContent: {
    flex: 1,
  },
  bannerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#fff',
    marginBottom: 8,
  },
  bannerDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.85)',
    lineHeight: 20,
  },
  bannerImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    marginLeft: 16,
  },
  floatingHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  floatingHeaderTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  selectedProjectName: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
    maxWidth: width * 0.7,
  },
  searchContainer: {
    marginBottom: 16,
    marginTop: 8,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: '#333',
  },
  clearButton: {
    padding: 4,
  },
  sectionHeader: {
    paddingVertical: 10,
    paddingHorizontal: 4,
    marginTop: 8,
    marginBottom: 12,
  },
  sectionHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  sectionHeaderText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#555',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  skeletonContainer: {
    flex: 1,
    paddingTop: 200,
  },
  skeletonItem: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  skeletonIcon: {
    width: 50,
    height: 50,
    borderRadius: 12,
    backgroundColor: '#f0f2f5',
    marginRight: 16,
  },
  skeletonContent: {
    flex: 1,
  },
  skeletonTitle: {
    height: 18,
    width: '80%',
    backgroundColor: '#f0f2f5',
    borderRadius: 4,
    marginBottom: 8,
  },
  skeletonSubtitle: {
    height: 14,
    width: '50%',
    backgroundColor: '#f0f2f5',
    borderRadius: 4,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#555',
    marginTop: 16,
    marginBottom: 8,
  },
  errorDetail: {
    textAlign: 'center',
    fontSize: 14,
    color: '#f44336',
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#4361ee',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    marginTop: 40,
  },
  emptyIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  clearSearchButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#f0f2f5',
    borderRadius: 24,
  },
  clearSearchText: {
    color: '#4361ee',
    fontWeight: '500',
  },
  listContent: {
    paddingBottom: 24,
    flexGrow: 1,
  },
  listFooter: {
    height: 40,
  },
  projectItemContainer: {
    marginBottom: 12,
  },
  projectItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
    borderLeftWidth: 5,
    borderLeftColor: '#f0f2f5',
  },
  selectedItem: {
    borderWidth: 0,
    borderLeftWidth: 5,
    borderLeftColor: '#4361ee',
    backgroundColor: '#F5F7FF',
    shadowColor: '#4361ee',
    shadowOpacity: 0.2,
    shadowRadius: 10,
    elevation: 6,
  },
  projectIcon: {
    width: 50,
    height: 50,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  projectInitial: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  projectContent: {
    flex: 1,
    paddingRight: 8,
  },
  projectTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
    lineHeight: 20,
  },
  projectCode: {
    fontSize: 13,
    color: '#666',
  },
  checkmarkContainer: {
    justifyContent: 'center',
    marginLeft: 8,
  },
  checkmarkCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  showMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    marginBottom: 12,
  },
  showMoreText: {
    color: '#4361ee',
    fontWeight: '500',
    marginRight: 4,
  },
  viewMoreButton: {
    alignSelf: 'flex-start',
    backgroundColor: '#f0f5ff',
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginTop: 2,
    marginBottom: 4,
  },
  viewMoreText: {
    fontSize: 11,
    color: '#4361ee',
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    marginTop: 10,
  },
  statItem: {
    marginRight: 16,
    alignItems: 'center',
    flexDirection: 'row',
  },
  statNumber: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    marginRight: 4,
  },
  statLabel: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
  },
  searchResults: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
    marginLeft: 4,
  },
  addNewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4361ee',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginTop: 12,
  },
  addNewIcon: {
    marginRight: 8,
  },
  addNewText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 15,
  },
});

export default ProjectManagerScreen;
