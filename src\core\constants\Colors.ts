import { Colors } from 'react-native-ui-lib';

// Cấu hình colors cho React Native UI Lib
Colors.loadSchemes({
  light: {
    // Design tokens cho background
    $backgroundDefault: '#FFFFFF',
    $backgroundElevated: '#F8F9FA',
    $backgroundElevatedLight: '#F0F2F5',
    $backgroundNeutral: '#F5F6F6',
    $backgroundNeutralLight: '#E8ECF0',
    $backgroundNeutralMedium: '#D3D7D5',
    $backgroundNeutralHeavy: '#676A68',
    $backgroundPrimary: '#23154d',
    $backgroundPrimaryLight: '#4A2B8A',
    $backgroundPrimaryMedium: '#1E3AAF',
    $backgroundPrimaryHeavy: '#0F1A3D',
    $backgroundSuccess: '#4CAF50',
    $backgroundSuccessLight: '#81C784',
    $backgroundWarning: '#FFC50D',
    $backgroundWarningLight: '#FFE48D',
    $backgroundDanger: '#FC3D2F',
    $backgroundDangerLight: '#FD7267',
    $backgroundDisabled: '#E8ECF0',

    // Design tokens cho button
    $buttonPrimary: '#1E3AAF',        // Màu nút chính
    $buttonPrimaryLight: '#4A5FD1',   // Màu nút chính nhẹ
    $buttonSecondary: '#4CAF50',      // Màu nút phụ
    $buttonSecondaryLight: '#66BB6A', // Màu nút phụ nhẹ
    $buttonSuccess: '#4CAF50',        // Màu nút thành công
    $buttonWarning: '#FFC50D',        // Màu nút cảnh báo
    $buttonDanger: '#FC3D2F',         // Màu nút nguy hiểm
    $buttonDisabled: '#E8ECF0',       // Màu nút vô hiệu hóa
    $buttonBorder: '#FFFFFF50',       // Màu viền nút

    // Design tokens cho text
    $textDefault: '#1E1E1E',
    $textNeutral: '#676A68',
    $textNeutralLight: '#A6ACB1',
    $textPrimary: '#23154d',
    $textSuccess: '#4CAF50',
    $textWarning: '#E89900',
    $textDanger: '#FC3D2F',
    $textDisabled: '#A6ACB1',
    $textInverted: '#FFFFFF',

    // Design tokens cho icon
    $iconDefault: '#676A68',
    $iconNeutral: '#A6ACB1',
    $iconPrimary: '#23154d',
    $iconSuccess: '#4CAF50',
    $iconWarning: '#E89900',
    $iconDanger: '#FC3D2F',
    $iconDisabled: '#A6ACB1',
    $iconInverted: '#FFFFFF',

    // Design tokens cho outline
    $outlineDefault: '#D3D7D5',
    $outlineNeutral: '#E8ECF0',
    $outlinePrimary: '#23154d',
    $outlineSuccess: '#4CAF50',
    $outlineWarning: '#FFC50D',
    $outlineDanger: '#FC3D2F',
    $outlineDisabled: '#E8ECF0',

    // Custom colors
    tintColor: '#E6B9A6',
  },
  dark: {
    // Design tokens cho background
    $backgroundDefault: '#000000',
    $backgroundElevated: '#1E2830',
    $backgroundElevatedLight: '#20303C',
    $backgroundNeutral: '#4D5963',
    $backgroundNeutralLight: '#6E7881',
    $backgroundNeutralMedium: '#A6ACB1',
    $backgroundNeutralHeavy: '#D2D6D8',
    $backgroundPrimary: '#4633E9',
    $backgroundPrimaryLight: '#5A48F5',
    $backgroundPrimaryMedium: '#8579FF',
    $backgroundPrimaryHeavy: '#3220CD',
    $backgroundSuccess: '#45C3A4',
    $backgroundSuccessLight: '#85DEC8',
    $backgroundWarning: '#FFD54E',
    $backgroundWarningLight: '#FFE48D',
    $backgroundDanger: '#FD7267',
    $backgroundDangerLight: '#FEA6A0',
    $backgroundDisabled: '#4D5963',

    // Design tokens cho button
    $buttonPrimary: '#4633E9',        // Màu nút chính (dark)
    $buttonPrimaryLight: '#5A48F5',   // Màu nút chính nhẹ (dark)
    $buttonSecondary: '#45C3A4',      // Màu nút phụ (dark)
    $buttonSecondaryLight: '#66D4B8', // Màu nút phụ nhẹ (dark)
    $buttonSuccess: '#45C3A4',        // Màu nút thành công (dark)
    $buttonWarning: '#FFD54E',        // Màu nút cảnh báo (dark)
    $buttonDanger: '#FD7267',         // Màu nút nguy hiểm (dark)
    $buttonDisabled: '#4D5963',       // Màu nút vô hiệu hóa (dark)
    $buttonBorder: '#FFFFFF20',       // Màu viền nút (dark)

    // Design tokens cho text
    $textDefault: '#FFFFFF',
    $textNeutral: '#E8ECF0',
    $textNeutralLight: '#D2D6D8',
    $textPrimary: '#B2ABFF',
    $textSuccess: '#45C3A4',
    $textWarning: '#FFD54E',
    $textDanger: '#FD7267',
    $textDisabled: '#6E7881',
    $textInverted: '#1E1E1E',

    // Design tokens cho icon
    $iconDefault: '#E8ECF0',
    $iconNeutral: '#D2D6D8',
    $iconPrimary: '#B2ABFF',
    $iconSuccess: '#45C3A4',
    $iconWarning: '#FFD54E',
    $iconDanger: '#FD7267',
    $iconDisabled: '#6E7881',
    $iconInverted: '#1E1E1E',

    // Design tokens cho outline
    $outlineDefault: '#4D5963',
    $outlineNeutral: '#6E7881',
    $outlinePrimary: '#4633E9',
    $outlineSuccess: '#45C3A4',
    $outlineWarning: '#FFD54E',
    $outlineDanger: '#FD7267',
    $outlineDisabled: '#4D5963',

    // Custom colors
    tintColor: '#E6B9A6',
  },
});

// Load custom colors
Colors.loadColors({
  // Legacy colors để tương thích ngược
  gray100: '#F5F6F6',
  gray500: '#D3D7D5',
  gray900: '#676A68',
  black: '#1E1E1E',
  white: '#FFFFFF',
  grey: '#373A40',
  blue: '#405D72',

  // Button colors (legacy - sẽ được thay thế bằng design tokens)
  buttonPrimary: '#1E3AAF',
  buttonSecondary: '#4CAF50',
  buttonBorder: '#FFFFFF50',
});

export default Colors;
