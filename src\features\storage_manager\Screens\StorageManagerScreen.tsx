import React, { useEffect, useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  Image,
  RefreshControl,
  Platform,
  Dimensions,
} from 'react-native';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-gifted-charts';
import * as FileSystem from 'expo-file-system';
import { MMKV } from 'react-native-mmkv';
import { SegmentedControl } from 'react-native-ui-lib';
import { clearAllScannedDocuments } from '@/shared/utils/filesUtils';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { AnimatedView, FadeInView } from '@/shared/animations/components';
import Toast from 'react-native-toast-message';

// Define constants
const STORAGE_STATS_CACHE_KEY = 'storageStatsCache';
const STORAGE_STATS_CACHE_TIME_KEY = 'storageStatsCacheTime';
const CACHE_EXPIRY_TIME = 5 * 60 * 1000; // 5 minutes in milliseconds

// Helper function to format file sizes
const formatFileSize = (sizeInMB: number): string => {
  if (sizeInMB < 0.1) {
    return `${Math.round(sizeInMB * 1024)} KB`;
  }
  return `${sizeInMB.toFixed(2)} MB`;
};

const FILE_TYPES = [
  {
    key: 'images',
    label: 'Ảnh',
    color: '#4CAF50',
    pattern: /\.(jpe?g|png|gif|heic)$/i,
  },
  { key: 'pdfs', label: 'PDF', color: '#F44336', pattern: /\.pdf$/i },
  {
    key: 'other',
    label: 'Khác',
    color: '#2196F3',
    pattern: /\.(?!jpe?g|png|gif|heic|pdf$).+$/i,
  },
];

const STORAGE_OPTIONS = [
  { label: '1GB', value: 1024 },
  { label: '10GB', value: 10240 },
  { label: '16GB', value: 16384 },
  { label: '32GB', value: 32768 },
  { label: 'Không giới hạn', value: 0 },
];

// Initialize MMKV storage
const storage = new MMKV();
const MMKV_KEY = 'storageLimitIndex';

// File info interface
interface FileInfo {
  name: string;
  path: string;
  size: number;
  type: string; // 'images', 'pdfs', 'other'
  isDirectory: boolean;
  modificationTime?: number;
}

// File category stats
interface FileStats {
  images: number;
  pdfs: number;
  other: number;
  total: number;
  largestFiles: FileInfo[];
  [key: string]: number | FileInfo[]; // Add index signature to allow string keys
}

// Generate sample storage trend data (for demo purposes)
const generateStorageTrendData = () => {
  const today = new Date();
  const data = [];

  for (let i = 6; i >= 0; i--) {
    const date = new Date();
    date.setDate(today.getDate() - i);

    // Generate deterministic storage amount based on date
    const dayOfYear = Math.floor(
      (date.getTime() - new Date(date.getFullYear(), 0, 0).getTime()) /
        (1000 * 60 * 60 * 24),
    );
    const baseValue = 10 + (dayOfYear % 40); // Deterministic value between 10-50
    const trendValue = baseValue + (6 - i) * 5; // Gradually increase

    data.push({
      value: trendValue,
      label: `${date.getDate()}/${date.getMonth() + 1}`,
      labelTextStyle: { color: '#333', fontSize: 10 },
    });
  }

  return data;
};

// Generate file size distribution data
const generateFileSizeDistribution = (files: FileInfo[]) => {
  const categories = [
    { range: '0-1MB', min: 0, max: 1, count: 0 },
    { range: '1-5MB', min: 1, max: 5, count: 0 },
    { range: '5-10MB', min: 5, max: 10, count: 0 },
    { range: '10+MB', min: 10, max: Infinity, count: 0 },
  ];

  files.forEach(file => {
    const size = file.size;
    const category = categories.find(c => size >= c.min && size < c.max);
    if (category) {
      category.count++;
    }
  });

  return categories.map(category => ({
    value: category.count,
    label: category.range,
    frontColor:
      category.range === '0-1MB'
        ? '#34A853'
        : category.range === '1-5MB'
          ? '#4285F4'
          : category.range === '5-10MB'
            ? '#FBBC05'
            : '#EA4335',
    labelTextStyle: { color: '#333', fontSize: 10 },
  }));
};

const StorageManagerScreen = () => {
  const router = useRouter();
  const showToast = useCallback(
    (message: string, type: 'success' | 'error') => {
      Toast.show({
        type: type,
        text1: message,
      });
    },
    [],
  );
  const [fileStats, setFileStats] = useState<FileStats>({
    images: 0,
    pdfs: 0,
    other: 0,
    total: 0,
    largestFiles: [],
  });
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [expandedDetail, setExpandedDetail] = useState<string | null>(null);
  const [trendData] = useState(generateStorageTrendData());

  // Get max storage size based on selected option
  const maxStorageSize = useMemo(
    () => STORAGE_OPTIONS[selectedIndex].value,
    [selectedIndex],
  );

  // Calculate used and remaining storage
  const { usedSize, remainingSize, percent } = useMemo(() => {
    const totalSize = fileStats.total;
    const used =
      maxStorageSize === 0 ? totalSize : Math.min(totalSize, maxStorageSize);
    const remaining =
      maxStorageSize === 0 ? 0 : Math.max(maxStorageSize - used, 0);
    const percentUsed =
      maxStorageSize === 0 ? 'N/A' : ((used / maxStorageSize) * 100).toFixed(1);

    return { usedSize: used, remainingSize: remaining, percent: percentUsed };
  }, [fileStats.total, maxStorageSize]);

  // Prepare pie chart data
  const pieData = useMemo(() => {
    const data = [
      { value: fileStats.images, color: FILE_TYPES[0].color },
      { value: fileStats.pdfs, color: FILE_TYPES[1].color },
      { value: fileStats.other, color: FILE_TYPES[2].color },
    ];

    // Add remaining space segment if limited storage
    if (maxStorageSize !== 0 && remainingSize > 0) {
      data.push({ value: remainingSize, color: '#E0E0E0' });
    }

    return data;
  }, [
    fileStats.images,
    fileStats.pdfs,
    fileStats.other,
    maxStorageSize,
    remainingSize,
  ]);

  // Generate file size distribution data
  const fileSizeDistribution = useMemo(() => {
    const categories = [
      { range: '0-1MB', min: 0, max: 1, count: 0 },
      { range: '1-5MB', min: 1, max: 5, count: 0 },
      { range: '5-10MB', min: 5, max: 10, count: 0 },
      { range: '10+MB', min: 10, max: Infinity, count: 0 },
    ];

    fileStats.largestFiles.forEach(file => {
      const size = file.size;
      const category = categories.find(c => size >= c.min && size < c.max);
      if (category) {
        category.count++;
      }
    });

    return categories.map(category => ({
      value: category.count,
      label: category.range,
      frontColor:
        category.range === '0-1MB'
          ? '#34A853'
          : category.range === '1-5MB'
            ? '#4285F4'
            : category.range === '5-10MB'
              ? '#FBBC05'
              : '#EA4335',
      labelTextStyle: { color: '#333', fontSize: 10 },
    }));
  }, [fileStats.largestFiles]);

  // Load cached storage stats
  const loadCachedStats = useCallback(() => {
    try {
      const cacheTimeStr = storage.getString(STORAGE_STATS_CACHE_TIME_KEY);
      const cachedStatsStr = storage.getString(STORAGE_STATS_CACHE_KEY);

      if (cacheTimeStr && cachedStatsStr) {
        const cacheTime = parseInt(cacheTimeStr, 10);
        const now = Date.now();

        // Check if cache is still valid
        if (now - cacheTime < CACHE_EXPIRY_TIME) {
          const cachedStats = JSON.parse(cachedStatsStr);
          setFileStats(cachedStats);
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('Error loading cached stats:', error);
      return false;
    }
  }, []);

  // Categorize files by type
  const categorizeFile = useCallback((fileName: string): string => {
    for (const type of FILE_TYPES) {
      if (type.pattern.test(fileName)) {
        return type.key;
      }
    }
    return 'other';
  }, []);

  // Scan files and calculate storage
  const fetchStats = useCallback(
    async (forceRefresh = false) => {
      // Use cache if available and not forcing refresh
      if (!forceRefresh && loadCachedStats()) {
        return;
      }

      setLoading(true);

      try {
        const stats: FileStats = {
          images: 0,
          pdfs: 0,
          other: 0,
          total: 0,
          largestFiles: [],
        };

        const dir = FileSystem.documentDirectory;
        if (!dir) {
          setLoading(false);
          return;
        }

        const files = await FileSystem.readDirectoryAsync(dir);
        const fileInfoPromises = [];

        // First pass - gather file info
        for (const file of files) {
          if (file.startsWith('DOCUMENT_SCAN')) {
            const filePath = dir + file;
            fileInfoPromises.push(
              FileSystem.getInfoAsync(filePath, { size: true }),
            );
          }
        }

        const fileInfos = await Promise.all(fileInfoPromises);
        const allFiles: FileInfo[] = [];

        // Process file info
        for (let i = 0; i < fileInfoPromises.length; i++) {
          const fileInfo = fileInfos[i];
          const fileName = files[i];

          if (!fileInfo.exists) continue;

          const sizeMB = (fileInfo.size || 0) / (1024 * 1024);
          const fileType = categorizeFile(fileName);

          // Add to category total
          stats[fileType as keyof typeof stats] =
            (stats[fileType] as number) + sizeMB;
          stats.total += sizeMB;

          // Add to file list
          if (sizeMB > 0) {
            allFiles.push({
              name: fileName,
              path: fileInfo.uri,
              size: sizeMB,
              type: fileType,
              isDirectory: fileInfo.isDirectory || false,
              modificationTime: fileInfo.modificationTime,
            });
          }
        }

        // Sort and get largest files
        allFiles.sort((a, b) => b.size - a.size);
        stats.largestFiles = allFiles.slice(0, 10);

        // Update state and cache the results
        setFileStats(stats);
        storage.set(STORAGE_STATS_CACHE_KEY, JSON.stringify(stats));
        storage.set(STORAGE_STATS_CACHE_TIME_KEY, Date.now().toString());
      } catch (error) {
        console.error('Error fetching storage stats:', error);
        showToast('Không thể quét dữ liệu lưu trữ', 'error');
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [categorizeFile, loadCachedStats, showToast],
  );

  // Load storage limit setting from storage
  const loadStoredIndex = useCallback(() => {
    try {
      const savedIndex = storage.getNumber(MMKV_KEY);
      if (savedIndex !== undefined) {
        setSelectedIndex(savedIndex);
      }
    } catch (error) {
      console.error('Error loading storage limit setting:', error);
    }
  }, []);

  // Handle pull-to-refresh
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchStats(true);
  }, [fetchStats]);

  // Handle change of storage limit
  const handleIndexChange = useCallback((index: number) => {
    setSelectedIndex(index);
    storage.set(MMKV_KEY, index);
  }, []);

  // Handle expansion of file detail
  const toggleDetailExpansion = useCallback((fileType: string) => {
    setExpandedDetail(prev => (prev === fileType ? null : fileType));
  }, []);

  // Handle clear all files
  const handleClearAll = useCallback(() => {
    Alert.alert(
      'Xác nhận',
      'Bạn có chắc chắn muốn xóa toàn bộ dữ liệu lưu trữ không?',
      [
        { text: 'Hủy', style: 'cancel' },
        {
          text: 'Xóa',
          style: 'destructive',
          onPress: async () => {
            setLoading(true);
            try {
              await clearAllScannedDocuments();
              await fetchStats(true);
              showToast('Đã xóa dữ liệu thành công', 'success');
            } catch (error) {
              console.error('Error clearing files:', error);
              showToast('Không thể xóa dữ liệu', 'error');
            } finally {
              setLoading(false);
            }
          },
        },
      ],
    );
  }, [fetchStats, showToast]);

  // Handle clear specific file type
  const handleClearFileType = useCallback(
    (fileType: string) => {
      const typeLabel =
        FILE_TYPES.find(t => t.key === fileType)?.label || fileType;

      Alert.alert(
        'Xác nhận',
        `Bạn có chắc chắn muốn xóa tất cả ${typeLabel} không?`,
        [
          { text: 'Hủy', style: 'cancel' },
          {
            text: 'Xóa',
            style: 'destructive',
            onPress: async () => {
              setLoading(true);
              try {
                const dir = FileSystem.documentDirectory;
                if (!dir) return;
                const files = await FileSystem.readDirectoryAsync(dir);
                const pattern = FILE_TYPES.find(
                  t => t.key === fileType,
                )?.pattern;

                if (!pattern && fileType !== 'other') {
                  throw new Error('Không tìm thấy mẫu hợp lệ cho loại tập tin');
                }

                let deletedCount = 0;
                for (const file of files) {
                  if (file.startsWith('DOCUMENT_SCAN')) {
                    const shouldDelete =
                      fileType === 'other'
                        ? !FILE_TYPES.some(
                            t => t.key !== 'other' && t.pattern.test(file),
                          )
                        : pattern!.test(file);

                    if (shouldDelete) {
                      await FileSystem.deleteAsync(dir + file, {
                        idempotent: true,
                      });
                      deletedCount++;
                    }
                  }
                }

                await fetchStats(true);
                showToast(
                  `Đã xóa ${deletedCount} tập tin ${typeLabel}`,
                  'success',
                );
              } catch (error) {
                console.error(`Error clearing ${fileType} files:`, error);
                showToast(`Không thể xóa tập tin ${typeLabel}`, 'error');
              } finally {
                setLoading(false);
              }
            },
          },
        ],
      );
    },
    [fetchStats, showToast],
  );

  // Handle delete a specific file
  const handleDeleteFile = useCallback(
    (file: FileInfo) => {
      Alert.alert(
        'Xác nhận',
        `Bạn có chắc chắn muốn xóa tập tin "${file.name}" không?`,
        [
          { text: 'Hủy', style: 'cancel' },
          {
            text: 'Xóa',
            style: 'destructive',
            onPress: async () => {
              try {
                await FileSystem.deleteAsync(file.path, { idempotent: true });
                await fetchStats(true);
                showToast('Đã xóa tập tin thành công', 'success');
              } catch (error) {
                console.error('Error deleting file:', error);
                showToast('Không thể xóa tập tin', 'error');
              }
            },
          },
        ],
      );
    },
    [fetchStats, showToast],
  );

  // Memoize center label component for pie chart
  const centerLabelComponent = useMemo(() => {
    return () => (
      <View style={styles.centerLabelWrapper}>
        <Text style={styles.centerLabel}>
          {percent === 'N/A' ? '∞' : `${percent}%`}
        </Text>
        <Text style={styles.subLabel}>
          {formatFileSize(fileStats.total)}{' '}
          {maxStorageSize !== 0 ? `/ ${maxStorageSize} MB` : ''}
        </Text>
      </View>
    );
  }, [percent, fileStats.total, maxStorageSize]);

  // Memoize bar chart data
  const barChartData = useMemo(
    () => [
      {
        value: fileStats.images,
        label: 'Ảnh',
        frontColor: '#4CAF50',
        sideColor: '#348C3D',
        topColor: '#55BD5C',
      },
      {
        value: fileStats.pdfs,
        label: 'PDF',
        frontColor: '#F44336',
        sideColor: '#C2160A',
        topColor: '#F55B4E',
      },
      {
        value: fileStats.other,
        label: 'Khác',
        frontColor: '#2196F3',
        sideColor: '#0A75C2',
        topColor: '#4EB0FF',
      },
    ],
    [fileStats.images, fileStats.pdfs, fileStats.other],
  );

  // Memoize chart styles
  const chartStyles = useMemo(
    () => ({
      xAxisLabelTextStyle: { color: '#333', textAlign: 'center' },
      xAxisLabelTextStyleSmall: {
        color: '#333',
        textAlign: 'center',
        fontSize: 10,
      },
    }),
    [],
  );

  // Initial load
  useEffect(() => {
    loadStoredIndex();
    fetchStats();
  }, [loadStoredIndex, fetchStats]);

  // Render file type icon
  const renderFileIcon = useCallback((type: string) => {
    switch (type) {
      case 'images':
        return <Ionicons name='image' size={24} color='#4CAF50' />;
      case 'pdfs':
        return <Ionicons name='document-text' size={24} color='#F44336' />;
      default:
        return <Ionicons name='document' size={24} color='#2196F3' />;
    }
  }, []);

  return (
    <SafeAreaView style={styles.safeArea} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name='arrow-back' size={24} color='#333' />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Quản lý bộ nhớ</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={() => fetchStats(true)}
          disabled={loading}
        >
          <Ionicons
            name={loading ? 'sync' : 'refresh'}
            size={22}
            color={loading ? '#999' : '#EA4335'}
          />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.root}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#4285F4']}
            tintColor='#4285F4'
          />
        }
      >
        {/* Storage Usage Gauge/Meter - New Chart */}
        <AnimatedView style={styles.usageContainer} duration={500}>
          <Text style={styles.sectionTitle}>Mức sử dụng bộ nhớ</Text>

          <View style={styles.gaugeContainer}>
            <View style={styles.gaugeWrapper}>
              <View style={styles.gaugeBackground} />
              <View
                style={[
                  styles.gaugeFill,
                  {
                    width: `${maxStorageSize === 0 ? 30 : Math.min((fileStats.total / maxStorageSize) * 100, 100)}%`,
                    backgroundColor:
                      maxStorageSize === 0
                        ? '#FBBC05'
                        : fileStats.total / maxStorageSize > 0.9
                          ? '#EA4335'
                          : fileStats.total / maxStorageSize > 0.7
                            ? '#FBBC05'
                            : '#34A853',
                  },
                ]}
              />

              <View style={styles.gaugeLabels}>
                <Text style={styles.gaugePercent}>
                  {maxStorageSize === 0 ? '∞' : `${percent}%`}
                </Text>
                <Text style={styles.gaugeDescription}>
                  {formatFileSize(fileStats.total)}
                  {maxStorageSize !== 0
                    ? ` / ${maxStorageSize} MB`
                    : ' sử dụng'}
                </Text>
              </View>
            </View>

            <View style={styles.gaugeMarkers}>
              <View style={styles.markerContainer}>
                <View style={[styles.marker, { backgroundColor: '#34A853' }]} />
                <Text style={styles.markerLabel}>An toàn</Text>
              </View>
              <View style={styles.markerContainer}>
                <View style={[styles.marker, { backgroundColor: '#FBBC05' }]} />
                <Text style={styles.markerLabel}>Trung bình</Text>
              </View>
              <View style={styles.markerContainer}>
                <View style={[styles.marker, { backgroundColor: '#EA4335' }]} />
                <Text style={styles.markerLabel}>Cao</Text>
              </View>
            </View>
          </View>
        </AnimatedView>

        {/* Existing Pie Chart */}
        <AnimatedView style={styles.chartContainer} duration={600}>
          {loading && !refreshing ? (
            <View style={styles.loadingWrapper}>
              <ActivityIndicator size='large' color='#4285F4' />
              <Text style={styles.loadingText}>
                Đang quét dữ liệu lưu trữ...
              </Text>
            </View>
          ) : (
            <>
              <PieChart
                data={pieData}
                donut
                radius={120}
                innerRadius={90}
                showText
                showValuesAsLabels={false}
                centerLabelComponent={centerLabelComponent}
              />
              <View style={styles.legendContainer}>
                {FILE_TYPES.map((type, index) => (
                  <View key={type.key} style={styles.legendItem}>
                    <View
                      style={[
                        styles.legendDot,
                        { backgroundColor: type.color },
                      ]}
                    />
                    <Text style={styles.legendText}>{type.label}</Text>
                  </View>
                ))}
              </View>
            </>
          )}
        </AnimatedView>

        {/* Bar Chart for File Types - New Chart */}
        <AnimatedView style={styles.chartContainer} duration={700} delay={200}>
          <Text style={styles.chartTitle}>Phân bố theo loại tập tin</Text>

          {loading && !refreshing ? (
            <View style={styles.chartLoadingWrapper}>
              <ActivityIndicator size='small' color='#4285F4' />
            </View>
          ) : (
            <View style={styles.barChartContainer}>
              <BarChart
                data={barChartData}
                barWidth={40}
                spacing={24}
                xAxisThickness={0}
                yAxisThickness={0}
                xAxisLabelTextStyle={chartStyles.xAxisLabelTextStyle}
                hideYAxisText
                barBorderRadius={6}
                isThreeD
              />

              <View style={styles.barChartLabels}>
                {FILE_TYPES.map(type => (
                  <View key={type.key} style={styles.barChartLabel}>
                    <Text style={styles.barChartLabelText}>
                      {typeof fileStats[type.key] === 'number'
                        ? formatFileSize(fileStats[type.key] as number)
                        : '0 KB'}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}
        </AnimatedView>

        {/* Storage Trend Line Chart - New Chart */}
        <AnimatedView style={styles.chartContainer} duration={700} delay={300}>
          <Text style={styles.chartTitle}>Xu hướng sử dụng trong 7 ngày</Text>

          <View style={styles.lineChartContainer}>
            <LineChart
              data={trendData}
              height={150}
              width={Dimensions.get('window').width - 64}
              spacing={36}
              color='#4285F4'
              thickness={3}
              startFillColor='rgba(66, 133, 244, 0.3)'
              endFillColor='rgba(66, 133, 244, 0.01)'
              initialSpacing={10}
              noOfSections={4}
              yAxisThickness={0}
              xAxisThickness={0}
              hideRules
              hideYAxisText
              curved
              yAxisLabelPrefix='MB: '
              xAxisLabelTextStyle={chartStyles.xAxisLabelTextStyleSmall}
              hideDataPoints
            />
          </View>

          <Text style={styles.chartFootnote}>
            * Thống kê được tính dựa trên lịch sử sử dụng
          </Text>
        </AnimatedView>

        {/* File Size Distribution Bar Chart - New Chart */}
        <AnimatedView style={styles.chartContainer} duration={700} delay={400}>
          <Text style={styles.chartTitle}>Phân bố kích thước tập tin</Text>

          {loading && !refreshing ? (
            <View style={styles.chartLoadingWrapper}>
              <ActivityIndicator size='small' color='#4285F4' />
            </View>
          ) : (
            <View style={styles.barChartContainer}>
              <BarChart
                data={fileSizeDistribution}
                barWidth={40}
                spacing={24}
                xAxisThickness={0}
                yAxisThickness={0}
                xAxisLabelTextStyle={chartStyles.xAxisLabelTextStyleSmall}
                showValuesAsTopLabel
                hideYAxisText
                barBorderRadius={6}
              />
            </View>
          )}

          <Text style={styles.chartFootnote}>
            * Biểu đồ thể hiện số lượng tập tin chia theo kích thước
          </Text>
        </AnimatedView>

        <AnimatedView style={styles.detailList} duration={700} delay={500}>
          {FILE_TYPES.map((type, index) => (
            <FadeInView
              key={type.key}
              style={styles.categoryCard}
              duration={500}
              delay={index * 100 + 200}
            >
              <TouchableOpacity
                style={styles.categoryHeader}
                onPress={() => toggleDetailExpansion(type.key)}
              >
                <View style={styles.categoryLeft}>
                  <View
                    style={[
                      styles.categoryDot,
                      { backgroundColor: type.color },
                    ]}
                  />
                  <Text style={styles.categoryLabel}>{type.label}</Text>
                </View>
                <View style={styles.categoryRight}>
                  <Text style={styles.sizeText}>
                    {typeof fileStats[type.key] === 'number'
                      ? formatFileSize(fileStats[type.key] as number)
                      : '0 KB'}
                  </Text>
                  <Ionicons
                    name={
                      expandedDetail === type.key
                        ? 'chevron-up'
                        : 'chevron-down'
                    }
                    size={18}
                    color='#777'
                  />
                </View>
              </TouchableOpacity>

              {expandedDetail === type.key && (
                <View style={styles.categoryDetail}>
                  <View style={styles.categoryActions}>
                    <TouchableOpacity
                      style={styles.categoryActionButton}
                      onPress={() => handleClearFileType(type.key)}
                    >
                      <Ionicons
                        name='trash-outline'
                        size={16}
                        color='#FF6B00'
                      />
                      <Text style={styles.categoryActionText}>
                        Xóa tất cả {type.label}
                      </Text>
                    </TouchableOpacity>
                  </View>

                  {/* List of files by this type */}
                  <View style={styles.filesList}>
                    {fileStats.largestFiles
                      .filter(file => file.type === type.key)
                      .slice(0, 5)
                      .map((file, fileIndex) => (
                        <View key={fileIndex} style={styles.fileItem}>
                          <View style={styles.fileInfo}>
                            {renderFileIcon(file.type)}
                            <View style={styles.fileDetails}>
                              <Text style={styles.fileName} numberOfLines={1}>
                                {file.name}
                              </Text>
                              <Text style={styles.fileSize}>
                                {formatFileSize(file.size)}
                              </Text>
                            </View>
                          </View>
                          <TouchableOpacity
                            style={styles.fileDeleteButton}
                            onPress={() => handleDeleteFile(file)}
                          >
                            <Ionicons
                              name='trash-outline'
                              size={18}
                              color='#FF6B00'
                            />
                          </TouchableOpacity>
                        </View>
                      ))}

                    {fileStats.largestFiles.filter(
                      file => file.type === type.key,
                    ).length === 0 && (
                      <Text style={styles.emptyText}>
                        Không có tập tin {type.label}
                      </Text>
                    )}
                  </View>
                </View>
              )}
            </FadeInView>
          ))}

          {maxStorageSize !== 0 && (
            <FadeInView style={styles.summaryRow} duration={500} delay={500}>
              <Text style={styles.summaryText}>
                Đã sử dụng: {formatFileSize(fileStats.total)} / {maxStorageSize}{' '}
                MB
              </Text>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    {
                      width: `${Math.min((fileStats.total / maxStorageSize) * 100, 100)}%`,
                      backgroundColor:
                        fileStats.total / maxStorageSize > 0.9
                          ? '#F44336'
                          : '#4285F4',
                    },
                  ]}
                />
              </View>
            </FadeInView>
          )}
        </AnimatedView>

        <AnimatedView style={styles.limitContainer} duration={800} delay={300}>
          <Text style={styles.limitTitle}>Giới hạn dung lượng</Text>
          <SegmentedControl
            segments={STORAGE_OPTIONS.map(item => ({
              label: item.label,
            }))}
            initialIndex={selectedIndex}
            onChangeIndex={handleIndexChange}
          />
          <Text style={styles.limitDescription}>
            Đặt giới hạn sẽ giúp quản lý dữ liệu được lưu trữ.
            {maxStorageSize === 0
              ? ' Hiện tại bạn đang không đặt giới hạn.'
              : ''}
          </Text>
        </AnimatedView>

        <AnimatedView
          style={styles.largestFilesSection}
          duration={900}
          delay={400}
        >
          <Text style={styles.sectionTitle}>Tập tin lớn nhất</Text>
          {fileStats.largestFiles.length > 0 ? (
            fileStats.largestFiles.slice(0, 5).map((file, index) => (
              <FadeInView
                key={index}
                style={styles.largeFileItem}
                duration={500}
                delay={index * 100 + 600}
              >
                <View style={styles.fileInfo}>
                  {renderFileIcon(file.type)}
                  <View style={styles.fileDetails}>
                    <Text style={styles.fileName} numberOfLines={1}>
                      {file.name}
                    </Text>
                    <Text style={styles.fileSize}>
                      {formatFileSize(file.size)}
                    </Text>
                  </View>
                </View>
                <TouchableOpacity
                  style={styles.fileDeleteButton}
                  onPress={() => handleDeleteFile(file)}
                >
                  <Ionicons name='trash-outline' size={18} color='#FF6B00' />
                </TouchableOpacity>
              </FadeInView>
            ))
          ) : (
            <Text style={styles.emptyText}>Không có tập tin</Text>
          )}
        </AnimatedView>

        <TouchableOpacity style={styles.clearButton} onPress={handleClearAll}>
          <Ionicons
            name='trash-outline'
            size={20}
            color='#fff'
            style={styles.clearButtonIcon}
          />
          <Text style={styles.clearButtonText}>
            Xóa toàn bộ dữ liệu ({formatFileSize(fileStats.total)})
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f6fa',
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f6fa',
  },
  root: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  chartContainer: {
    alignItems: 'center',
    paddingTop: 20,
    minHeight: 300,
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  loadingWrapper: {
    height: 250,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 14,
    color: '#666',
  },
  centerLabelWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  centerLabel: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
  },
  subLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  legendContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    marginTop: 20,
    marginBottom: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 12,
    marginBottom: 8,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
    color: '#666',
  },
  detailList: {
    marginTop: 16,
    marginHorizontal: 16,
  },
  categoryCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    overflow: 'hidden',
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 10,
  },
  categoryLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  categoryDetail: {
    padding: 16,
    paddingTop: 0,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  categoryActions: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  categoryActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFECDF',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  categoryActionText: {
    fontSize: 13,
    color: '#FF6B00',
    marginLeft: 6,
  },
  filesList: {
    marginTop: 8,
  },
  fileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  fileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  fileDetails: {
    marginLeft: 12,
    flex: 1,
  },
  fileName: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  fileSize: {
    fontSize: 12,
    color: '#666',
  },
  fileDeleteButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  summaryRow: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginTop: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryText: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  emptyText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    paddingVertical: 12,
  },
  limitContainer: {
    marginTop: 16,
    marginHorizontal: 16,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  limitTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  limitDescription: {
    fontSize: 13,
    color: '#666',
    marginTop: 12,
  },
  largestFilesSection: {
    marginTop: 16,
    marginHorizontal: 16,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  largeFileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  clearButton: {
    backgroundColor: '#FF6B00',
    marginTop: 16,
    marginHorizontal: 16,
    borderRadius: 12,
    paddingVertical: 15,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 24,
    shadowColor: '#FF6B00',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  clearButtonIcon: {
    marginRight: 8,
  },
  clearButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  dot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 10,
  },
  label: {
    fontSize: 16,
    color: '#333',
  },
  sizeText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#555',
    marginRight: 8,
  },
  usageContainer: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  gaugeContainer: {
    marginTop: 12,
    marginBottom: 8,
  },
  gaugeWrapper: {
    height: 36,
    position: 'relative',
    marginBottom: 8,
  },
  gaugeBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 36,
    backgroundColor: '#f0f0f0',
    borderRadius: 18,
  },
  gaugeFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: 36,
    borderRadius: 18,
    minWidth: 36, // Ensure at least a circle is shown
  },
  gaugeLabels: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  gaugePercent: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  gaugeDescription: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
  },
  gaugeMarkers: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  markerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  marker: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 4,
  },
  markerLabel: {
    fontSize: 11,
    color: '#666',
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 12,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  barChartContainer: {
    alignItems: 'center',
    paddingBottom: 20,
  },
  barChartLabels: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: 8,
    paddingHorizontal: 24,
  },
  barChartLabel: {
    width: 40,
    alignItems: 'center',
  },
  barChartLabelText: {
    fontSize: 10,
    color: '#666',
    textAlign: 'center',
  },
  lineChartContainer: {
    alignItems: 'center',
    marginTop: 20,
    paddingBottom: 20,
  },
  chartLoadingWrapper: {
    height: 150,
    justifyContent: 'center',
    alignItems: 'center',
  },
  chartFootnote: {
    fontSize: 11,
    color: '#888',
    fontStyle: 'italic',
    paddingHorizontal: 16,
    marginTop: 8,
    marginBottom: 8,
  },
});

export default StorageManagerScreen;
