import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  TextInput,
  Switch,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { MMKV } from 'react-native-mmkv';
import * as ImagePicker from 'expo-image-picker';
import { useLogout } from '@features/auth/hooks/useAuth';
import { ROUTES } from '@core/constants/routes';
import Toast from 'react-native-toast-message';
import { useTheme, useThemeMode } from '@/core/theme/theme';

// Local storage để lưu trữ thông tin người dùng
const storage = new MMKV();
const USER_INFO_KEY = 'userInfoSettings';

// Fake user data for demo
const defaultUserInfo = {
  displayName: 'Người dùng',
  email: '<EMAIL>',
  avatar: null,
  position: 'Quản trị viên',
  department: 'Phòng CNTT',
  allowDataCollection: true,
};

type IconName =
  | 'arrow-back'
  | 'person-circle'
  | 'mail'
  | 'business'
  | 'briefcase'
  | 'shield-checkmark'
  | 'camera'
  | 'log-out'
  | 'moon'
  | 'sunny';

const UserProfileScreen = () => {
  const router = useRouter();
  const showToast = (message: string, type: 'success' | 'error') => {
    Toast.show({
      type: type,
      text1: message,
    });
  };
  const { mutate: logout, isPending: isLoggingOut } = useLogout();
  const { colors } = useTheme();
  const { theme, toggleTheme } = useThemeMode();

  // Lấy thông tin người dùng từ storage hoặc sử dụng thông tin mẫu
  const savedUserInfo = storage.getString(USER_INFO_KEY);
  const [userInfo, setUserInfo] = useState(
    savedUserInfo ? JSON.parse(savedUserInfo) : defaultUserInfo,
  );

  // Cập nhật thông tin người dùng
  const updateUserInfo = (key: string, value: any) => {
    const updatedUserInfo = { ...userInfo, [key]: value };
    setUserInfo(updatedUserInfo);
    storage.set(USER_INFO_KEY, JSON.stringify(updatedUserInfo));
    showToast('Thông tin đã được cập nhật', 'success');
  };

  // Xử lý chọn ảnh đại diện
  const handleChangeAvatar = async () => {
    // Xin quyền truy cập vào thư viện ảnh
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert(
        'Cần quyền truy cập',
        'Vui lòng cho phép ứng dụng truy cập vào thư viện ảnh để chọn ảnh đại diện.',
        [{ text: 'Đã hiểu' }],
      );
      return;
    }

    // Mở thư viện ảnh để chọn
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.7,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      updateUserInfo('avatar', result.assets[0].uri);
    }
  };

  // Xử lý đăng xuất
  const handleLogout = () => {
    Alert.alert('Đăng xuất', 'Bạn có chắc muốn đăng xuất khỏi ứng dụng?', [
      {
        text: 'Hủy',
        style: 'cancel',
      },
      {
        text: 'Đăng xuất',
        style: 'destructive',
        onPress: () => {
          logout();
          // Nếu không dùng React Query, có thể điều hướng thủ công
          // router.replace(ROUTES.SIGN_IN);
        },
      },
    ]);
  };

  // Xử lý lưu thông tin
  const handleSaveChanges = () => {
    showToast('Đã lưu thông tin người dùng', 'success');
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background }]}
      edges={['top']}
    >
      <View
        style={[
          styles.header,
          { backgroundColor: colors.surface, borderBottomColor: colors.border },
        ]}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons
            name={'arrow-back' as IconName}
            size={24}
            color={colors.textPrimary}
          />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.textPrimary }]}>
          Hồ sơ người dùng
        </Text>
        <TouchableOpacity
          style={[styles.saveButton, { backgroundColor: colors.primary }]}
          onPress={handleSaveChanges}
        >
          <Text style={styles.saveButtonText}>Lưu</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          {/* Avatar section */}
          <View style={styles.avatarSection}>
            <View
              style={[
                styles.avatarContainer,
                { backgroundColor: colors.border },
              ]}
            >
              {userInfo.avatar ? (
                <Image
                  source={{ uri: userInfo.avatar }}
                  style={styles.avatar}
                />
              ) : (
                <Ionicons
                  name={'person-circle' as IconName}
                  size={110}
                  color={colors.primary}
                />
              )}
              <TouchableOpacity
                style={[
                  styles.avatarEditButton,
                  { backgroundColor: colors.primary },
                ]}
                onPress={handleChangeAvatar}
              >
                <Ionicons name={'camera' as IconName} size={20} color='#fff' />
              </TouchableOpacity>
            </View>
            <TouchableOpacity
              style={styles.changeAvatarButton}
              onPress={handleChangeAvatar}
            >
              <Text
                style={[styles.changeAvatarText, { color: colors.primary }]}
              >
                Thay đổi ảnh đại diện
              </Text>
            </TouchableOpacity>
          </View>

          {/* Info fields */}
          <View
            style={[
              styles.infoSection,
              {
                backgroundColor: colors.surface,
                shadowColor: colors.textPrimary,
              },
            ]}
          >
            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Ionicons
                  name={'person-circle' as IconName}
                  size={24}
                  color={colors.primary}
                />
              </View>
              <View style={styles.infoInputContainer}>
                <Text
                  style={[styles.infoLabel, { color: colors.textSecondary }]}
                >
                  Tên hiển thị
                </Text>
                <TextInput
                  style={[
                    styles.infoInput,
                    {
                      color: colors.textPrimary,
                      borderBottomColor: colors.border,
                    },
                  ]}
                  value={userInfo.displayName}
                  onChangeText={text => updateUserInfo('displayName', text)}
                  placeholder='Nhập tên hiển thị'
                  placeholderTextColor={colors.textDisabled}
                />
              </View>
            </View>

            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Ionicons
                  name={'mail' as IconName}
                  size={24}
                  color={colors.primary}
                />
              </View>
              <View style={styles.infoInputContainer}>
                <Text
                  style={[styles.infoLabel, { color: colors.textSecondary }]}
                >
                  Email
                </Text>
                <TextInput
                  style={[
                    styles.infoInput,
                    {
                      color: colors.textPrimary,
                      borderBottomColor: colors.border,
                    },
                  ]}
                  value={userInfo.email}
                  onChangeText={text => updateUserInfo('email', text)}
                  placeholder='Nhập email'
                  placeholderTextColor={colors.textDisabled}
                  keyboardType='email-address'
                />
              </View>
            </View>

            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Ionicons
                  name={'briefcase' as IconName}
                  size={24}
                  color={colors.primary}
                />
              </View>
              <View style={styles.infoInputContainer}>
                <Text
                  style={[styles.infoLabel, { color: colors.textSecondary }]}
                >
                  Chức vụ
                </Text>
                <TextInput
                  style={[
                    styles.infoInput,
                    {
                      color: colors.textPrimary,
                      borderBottomColor: colors.border,
                    },
                  ]}
                  value={userInfo.position}
                  onChangeText={text => updateUserInfo('position', text)}
                  placeholder='Nhập chức vụ'
                  placeholderTextColor={colors.textDisabled}
                />
              </View>
            </View>

            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Ionicons
                  name={'business' as IconName}
                  size={24}
                  color={colors.primary}
                />
              </View>
              <View style={styles.infoInputContainer}>
                <Text
                  style={[styles.infoLabel, { color: colors.textSecondary }]}
                >
                  Phòng ban
                </Text>
                <TextInput
                  style={[
                    styles.infoInput,
                    {
                      color: colors.textPrimary,
                      borderBottomColor: colors.border,
                    },
                  ]}
                  value={userInfo.department}
                  onChangeText={text => updateUserInfo('department', text)}
                  placeholder='Nhập phòng ban'
                  placeholderTextColor={colors.textDisabled}
                />
              </View>
            </View>
          </View>

          {/* Privacy section */}
          <View
            style={[
              styles.privacySection,
              {
                backgroundColor: colors.surface,
                shadowColor: colors.textPrimary,
              },
            ]}
          >
            <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
              Quyền riêng tư
            </Text>

            <View style={styles.switchItem}>
              <View style={styles.switchTextContainer}>
                <Ionicons
                  name={'shield-checkmark' as IconName}
                  size={24}
                  color={colors.primary}
                />
                <Text
                  style={[styles.switchText, { color: colors.textPrimary }]}
                >
                  Cho phép thu thập dữ liệu
                </Text>
              </View>
              <Switch
                value={userInfo.allowDataCollection}
                onValueChange={value =>
                  updateUserInfo('allowDataCollection', value)
                }
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={'#fff'}
              />
            </View>
          </View>

          {/* Logout button */}
          <TouchableOpacity
            style={styles.logoutButton}
            onPress={handleLogout}
            disabled={isLoggingOut}
          >
            <Ionicons
              name={'log-out' as IconName}
              size={20}
              color='#fff'
              style={styles.logoutIcon}
            />
            <Text style={styles.logoutText}>
              {isLoggingOut ? 'Đang xử lý...' : 'Đăng xuất'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  backButton: {
    padding: 4,
  },
  rightPlaceholder: {
    width: 32,
  },
  saveButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  saveButtonText: {
    color: '#fff',
    fontWeight: '500',
    fontSize: 14,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  avatarSection: {
    alignItems: 'center',
    marginVertical: 10,
  },
  avatarContainer: {
    width: 110,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarEditButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  changeAvatarButton: {
    paddingVertical: 8,
  },
  changeAvatarText: {
    fontWeight: '500',
  },
  infoSection: {
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  infoIconContainer: {
    width: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoInputContainer: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  infoInput: {
    fontSize: 16,
    borderBottomWidth: 1,
    paddingVertical: 4,
  },
  privacySection: {
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  switchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  switchTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchText: {
    marginLeft: 12,
    fontSize: 16,
  },
  logoutButton: {
    backgroundColor: '#f44336',
    borderRadius: 10,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 20,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  logoutIcon: {
    marginRight: 8,
  },
  logoutText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default UserProfileScreen;
