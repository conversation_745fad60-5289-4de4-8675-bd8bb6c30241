import { Session, useSession } from '@features/auth/context/ctx';
import { Redirect, Stack, useRouter } from 'expo-router';
import { Platform, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Text, View } from 'react-native-ui-lib';
import React from 'react';
import UpdateNotification from '@features/updates/components/UpdateNotification';

export default function AppLayout() {
  const { isLoading, session }: Session = useSession();
  const router = useRouter();

  // Only require authentication within the (app) group's layout as users
  // need to be able to access the (auth) group and sign in again.
  if (isLoading) {
    return <Text>Loading...</Text>;
  }

  if (!session) {
    return <Redirect href='/(auth)/sign-in' />;
  }

  // Xử lý quay lại App Hub
  const goBackToAppHub = () => {
    router.push('/(app-hub)');
  };

  return (
    <>
      <UpdateNotification />
      <View style={styles.container}>
        <Stack
          screenOptions={{
            headerShown: false,
            animation: Platform.OS === 'ios' ? 'default' : 'fade',
            animationDuration: 200,
            contentStyle: { backgroundColor: '#111' },
            gestureEnabled: true,
            gestureDirection: 'horizontal',
            fullScreenGestureEnabled: true,
          }}
        >
          <Stack.Screen
            name='(tabs)'
            options={{
              gestureEnabled: false,
            }}
          />
          <Stack.Screen
            name='scan/index'
            options={{
              headerTitle: 'Quét tài liệu',
              headerShown: false,
              animation: 'slide_from_bottom',
            }}
          />
          <Stack.Screen
            name='scan/[id]/index'
            options={{
              headerTitle: 'Chi tiết tài liệu',
              headerShown: false,
              animation: 'slide_from_right',
            }}
          />
          <Stack.Screen
            name='settings/index'
            options={{
              headerTitle: 'Cài đặt',
              headerShown: false,
              headerBackTitle: 'Quay lại',
              animation: 'slide_from_right',
            }}
          />
          <Stack.Screen
            name='settings/project-manager/index'
            options={{
              headerTitle: 'Quản lý dự án',
              headerShown: false,
              animation: 'slide_from_right',
            }}
          />
          <Stack.Screen
            name='settings/ai-models/index'
            options={{
              headerTitle: 'Mô hình AI',
              headerShown: false,
              animation: 'slide_from_right',
            }}
          />
          <Stack.Screen
            name='settings/document-type/index'
            options={{
              headerTitle: 'Loại tài liệu',
              headerShown: false,
              animation: 'slide_from_right',
            }}
          />
          <Stack.Screen
            name='settings/project-document-settings/index'
            options={{
              headerTitle: 'Cài đặt dự án & văn bản',
              headerShown: false,
              animation: 'slide_from_right',
            }}
          />
          <Stack.Screen
            name='settings/storage_manager/index'
            options={{
              headerTitle: 'Cài đặt',
              headerShown: false,
              animation: 'slide_from_right',
            }}
          />
          <Stack.Screen
            name='settings/notifications/index'
            options={{
              headerTitle: 'Thông báo',
              headerShown: false,
              animation: 'slide_from_right',
            }}
          />
          <Stack.Screen
            name='settings/appearance/index'
            options={{
              headerTitle: 'Giao diện',
              headerShown: false,
              animation: 'slide_from_right',
            }}
          />
          <Stack.Screen
            name='settings/language/index'
            options={{
              headerTitle: 'Ngôn ngữ',
              headerShown: false,
              animation: 'slide_from_right',
            }}
          />
          <Stack.Screen
            name='settings/about/index'
            options={{
              headerTitle: 'Thông tin',
              headerShown: false,
              animation: 'slide_from_right',
            }}
          />
          <Stack.Screen
            name='settings/user-profile/index'
            options={{
              headerTitle: 'Hồ sơ người dùng',
              headerShown: false,
              animation: 'slide_from_right',
            }}
          />
          <Stack.Screen
            name='websocket-test'
            options={{
              headerTitle: 'WebSocket Test',
              headerShown: false,
              animation: 'slide_from_right',
            }}
          />
          <Stack.Screen
            name='qr-scanner'
            options={{
              headerTitle: 'vv',
              headerShown: false,
              animation: 'slide_from_bottom',
            }}
          />
          <Stack.Screen
            name='citizen-detail'
            options={{
              headerShown: false,
              animation: 'slide_from_right',
            }}
          />
        </Stack>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#111',
  },
  appBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.buttonPrimary,
    paddingVertical: 10,
    paddingHorizontal: 15,
    justifyContent: 'space-between',
  },
  backButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  appInfo: {
    flex: 1,
    alignItems: 'center',
  },
  appBadgeText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  appBadgeSubtext: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 10,
  },
  infoButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
