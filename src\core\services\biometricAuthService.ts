import * as LocalAuthentication from 'expo-local-authentication';

export type BiometricType = 'fingerprint' | 'faceid' | 'none';

export interface BiometricAuthOptions {
    promptMessage: string;
    cancelLabel?: string;
    fallbackLabel?: string;
}

/**
 * Service quản lý xác thực sinh trắc học (FaceID/TouchID)
 */
class BiometricAuthService {
    /**
     * Kiểm tra thiết bị có hỗ trợ sinh trắc học không
     */
    async isBiometricAvailable(): Promise<boolean> {
        const isCompatible = await LocalAuthentication.hasHardwareAsync();
        return isCompatible;
    }

    /**
     * Kiểm tra người dùng đã đăng ký dữ liệu sinh trắc học chưa
     */
    async isEnrolled(): Promise<boolean> {
        const isEnrolled = await LocalAuthentication.isEnrolledAsync();
        return isEnrolled;
    }

    /**
     * <PERSON><PERSON><PERSON> lo<PERSON> sinh trắc học đượ<PERSON> hỗ trợ
     */
    async getBiometricType(): Promise<BiometricType> {
        try {
            const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();

            if (supportedTypes.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION)) {
                return 'faceid';
            } else if (supportedTypes.includes(LocalAuthentication.AuthenticationType.FINGERPRINT)) {
                return 'fingerprint';
            }

            return 'none';
        } catch (error) {
            console.error('Error getting biometric type:', error);
            return 'none';
        }
    }

    /**
     * Xác thực sinh trắc học
     */
    async authenticate(options: BiometricAuthOptions): Promise<boolean> {
        try {
            const isAvailable = await this.isBiometricAvailable();
            const isEnrolled = await this.isEnrolled();

            if (!isAvailable || !isEnrolled) {
                return false;
            }

            const result = await LocalAuthentication.authenticateAsync({
                promptMessage: options.promptMessage,
                cancelLabel: options.cancelLabel || 'Hủy',
                fallbackLabel: options.fallbackLabel || 'Sử dụng mật khẩu',
                disableDeviceFallback: false,
            });

            return result.success;
        } catch (error) {
            console.error('Biometric authentication error:', error);
            return false;
        }
    }
}

export const biometricAuthService = new BiometricAuthService();
export default biometricAuthService; 