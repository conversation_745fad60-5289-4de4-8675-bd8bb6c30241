import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-ui-lib';
import { Ionicons } from '@expo/vector-icons';

interface StatCardProps {
  title: string;
  value: string;
  icon: string;
  trend: string;
}

export default function StatCard({ title, value, icon, trend }: StatCardProps) {
  const isPositive = trend.startsWith('+');

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <Ionicons name={icon as any} size={24} color="#007AFF" />
      </View>
      <Text style={styles.value}>{value}</Text>
      <Text style={styles.title}>{title}</Text>
      <View style={styles.trendContainer}>
        <Ionicons
          name={isPositive ? 'arrow-up' : 'arrow-down'}
          size={12}
          color={isPositive ? '#34C759' : '#FF3B30'}
        />
        <Text
          style={[
            styles.trend,
            { color: isPositive ? '#34C759' : '#FF3B30' },
          ]}
        >
          {trend}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    width: '30%',
    minWidth: 120,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  value: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 4,
  },
  title: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trend: {
    fontSize: 12,
    marginLeft: 4,
  },
}); 