import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
} from 'react';
import { Alert } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import * as FileSystem from 'expo-file-system';
import { useRouter } from 'expo-router';
import { ROUTES } from '@core/constants/routes';
import DocumentScanner, {
  ScanDocumentResponse,
} from 'react-native-document-scanner-plugin';
import {
  ScanSessionRepository,
  ScanSession as DBSession,
} from '@core/database/repositories/scanSessionRepository';
import ScanService, { ScanOptions } from '../services/scanService';
import { StorageManager } from '../services/memoryManager';

interface ScanContextType {
  scanSessions: ScanSession[];
  currentSession: ScanSession | null;
  sessionCount: number;
  totalStorageUsed: number;
  isNetworkConnected: boolean;
  isLoading: boolean;
  addScanSession: (
    images: string[],
    meta?: { title?: string },
  ) => Promise<void>;
  deleteScanSession: (id: string) => Promise<void>;
  updateScanSessionTitle: (id: string, title: string) => Promise<void>;
  startScan: (options?: ScanOptions) => Promise<void>;
  removeImagesFromCurrentSession: (indexes: number[]) => Promise<void>;
  updateCurrentSessionImages: (images: string[]) => Promise<void>;
  cleanupOldSessions: () => Promise<boolean>;
  loadSessionsFromDatabase: () => Promise<void>;
}

const ScanContext = createContext<ScanContextType | undefined>(undefined);

export const ScanProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [scanSessions, setScanSessions] = useState<ScanSession[]>([]);
  const [currentSession, setCurrentSession] = useState<ScanSession | null>(
    null,
  );
  const [sessionCount, setSessionCount] = useState<number>(0);
  const [totalStorageUsed, setTotalStorageUsed] = useState<number>(0);
  const [isNetworkConnected, setIsNetworkConnected] = useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const router = useRouter();

  const scanService = ScanService.getInstance();
  const storageManager = StorageManager.getInstance();
  const sessionRepository = new ScanSessionRepository();

  // Load sessions from database on mount
  useEffect(() => {
    loadSessionsFromDatabase();
  }, []);

  // Kiểm tra kết nối mạng
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsNetworkConnected(state.isConnected || false);
    });
    return () => unsubscribe();
  }, []);

  // Load sessions from database
  const loadSessionsFromDatabase = useCallback(async () => {
    try {
      setIsLoading(true);
      const dbSessions = await sessionRepository.getAllSessions();

      // Convert DB sessions to app sessions
      const appSessions: ScanSession[] = dbSessions.map(dbSession => ({
        id: dbSession.id,
        title: dbSession.title,
        images: ScanSessionRepository.stringToImages(dbSession.images),
        createdAt: dbSession.createdAt,
        siteName: dbSession.siteName,
        isSent: dbSession.isSent,
        size: dbSession.size,
      }));

      setScanSessions(appSessions);
      setSessionCount(appSessions.length);

      // Calculate total storage used
      const totalSize = appSessions.reduce(
        (sum, session) => sum + (session.size || 0),
        0,
      );
      setTotalStorageUsed(totalSize);
    } catch (error) {
      console.error('Error loading sessions from database:', error);
    } finally {
      setIsLoading(false);
    }
  }, [sessionRepository]);

  // Save session to database
  const saveSessionToDatabase = useCallback(
    async (session: ScanSession): Promise<string> => {
      const dbSession: Omit<DBSession, 'id' | 'createdAt' | 'updatedAt'> = {
        title: session.title,
        images: ScanSessionRepository.imagesToString(session.images),
        siteName: session.siteName,
        isSent: session.isSent,
        size: session.size,
        userId: undefined, // TODO: Add user ID when auth is implemented
      };

      return await sessionRepository.add(dbSession);
    },
    [sessionRepository],
  );

  // Update session in database
  const updateSessionInDatabase = useCallback(
    async (id: string, updates: Partial<ScanSession>) => {
      const dbUpdates: Partial<DBSession> = {};

      if (updates.title !== undefined) dbUpdates.title = updates.title;
      if (updates.images !== undefined)
        dbUpdates.images = ScanSessionRepository.imagesToString(updates.images);
      if (updates.siteName !== undefined) dbUpdates.siteName = updates.siteName;
      if (updates.isSent !== undefined) dbUpdates.isSent = updates.isSent;
      if (updates.size !== undefined) dbUpdates.size = updates.size;

      await sessionRepository.update(id, dbUpdates);
    },
    [sessionRepository],
  );

  const addScanSession = useCallback(
    async (images: string[], meta?: { title?: string }) => {
      const now = Date.now();
      const newSession: ScanSession = {
        id: now.toString(), // Temporary ID
        images,
        title: meta?.title || `Phiên quét ${new Date(now).toLocaleString()}`,
        createdAt: now,
        isSent: false,
        size: 0, // Sẽ cập nhật sau
      };

      // Tính tổng kích thước của session
      const sizes = await Promise.all(
        images.map(async uri => {
          try {
            const info = await FileSystem.getInfoAsync(uri);
            return info.exists ? info.size || 0 : 0;
          } catch (error) {
            console.error('Error getting file size:', error);
            return 0;
          }
        }),
      );

      const totalSize = sizes.reduce((sum, size) => sum + size, 0);
      const sessionWithSize = { ...newSession, size: totalSize };

      try {
        // Save to database first
        const dbId = await saveSessionToDatabase(sessionWithSize);

        // Update session with database ID
        const finalSession = { ...sessionWithSize, id: dbId };

        // Update state
        setScanSessions(prev => [finalSession, ...prev]);
        setCurrentSession(finalSession);
        setSessionCount(prev => prev + 1);
        setTotalStorageUsed(prev => prev + totalSize);
      } catch (error) {
        console.error('Error saving session to database:', error);
        Alert.alert('Lỗi', 'Không thể lưu phiên quét. Vui lòng thử lại.');
      }
    },
    [saveSessionToDatabase],
  );

  const deleteScanSession = useCallback(
    async (id: string) => {
      // Lấy session cần xóa
      const sessionToDelete = scanSessions.find(session => session.id === id);
      if (!sessionToDelete) return;

      try {
        // Xóa các file trong session
        await Promise.all(
          sessionToDelete.images.map(async uri => {
            try {
              await FileSystem.deleteAsync(uri, { idempotent: true });
            } catch (error) {
              console.warn('Could not delete file:', uri, error);
            }
          }),
        );

        // Xóa từ database
        await sessionRepository.delete(id);

        // Cập nhật state
        setScanSessions(prev => prev.filter(session => session.id !== id));
        if (currentSession?.id === id) {
          setCurrentSession(null);
        }
        setSessionCount(prev => prev - 1);
        setTotalStorageUsed(prev => prev - (sessionToDelete.size || 0));

        // Cập nhật metadata cache
        await storageManager.updateMetadataCache(true);
      } catch (error) {
        console.error('Error deleting session:', error);
        Alert.alert('Lỗi', 'Không thể xóa phiên quét. Vui lòng thử lại.');
      }
    },
    [currentSession, scanSessions, storageManager, sessionRepository],
  );

  const updateScanSessionTitle = useCallback(
    async (id: string, title: string) => {
      try {
        // Update in database
        await updateSessionInDatabase(id, { title });

        // Update state
        setScanSessions(prev =>
          prev.map(session =>
            session.id === id ? { ...session, title } : session,
          ),
        );

        if (currentSession?.id === id) {
          setCurrentSession(prev => (prev ? { ...prev, title } : null));
        }
      } catch (error) {
        console.error('Error updating session title:', error);
        Alert.alert('Lỗi', 'Không thể cập nhật tên phiên quét.');
      }
    },
    [currentSession, updateSessionInDatabase],
  );

  const startScan = useCallback(
    async (options?: ScanOptions) => {
      try {
        // Kiểm tra bộ nhớ
        const hasEnoughStorage = await storageManager.ensureStorageAvailable();
        if (!hasEnoughStorage) {
          Alert.alert(
            'Thiếu bộ nhớ',
            'Bộ nhớ của thiết bị không đủ để quét thêm. Hãy xóa một số phiên quét cũ để tiếp tục.',
            [{ text: 'OK' }],
          );
          return;
        }

        const result = await scanService.startScan(options);
        if (result) {
          addScanSession(result.images, {
            title: `Phiên quét ${new Date().toLocaleString()}`,
          });
        }
      } catch (error) {
        console.error('Scan error:', error);
        Alert.alert('Lỗi', 'Không thể quét tài liệu. Vui lòng thử lại.');
      }
    },
    [addScanSession, scanService, storageManager],
  );

  const cleanupOldSessions = useCallback(async () => {
    try {
      // Xóa file tạm
      await scanService.cleanupTempFiles();

      // Xóa phiên quét cũ hơn 7 ngày từ database
      const deletedCount = await sessionRepository.deleteOldSessions(7);

      // Reload sessions from database
      await loadSessionsFromDatabase();

      // Dọn dẹp thêm cache
      await storageManager.cleanupByAge(7);

      console.log(`Deleted ${deletedCount} old sessions`);
      return true;
    } catch (error) {
      console.error('Cleanup error:', error);
      return false;
    }
  }, [
    scanService,
    storageManager,
    sessionRepository,
    loadSessionsFromDatabase,
  ]);

  const removeImagesFromCurrentSession = useCallback(
    async (indexes: number[]) => {
      if (!currentSession) return;

      const newImages = currentSession.images.filter(
        (_, index) => !indexes.includes(index),
      );

      // Tính lại kích thước
      const sizes = await Promise.all(
        newImages.map(async uri => {
          try {
            const info = await FileSystem.getInfoAsync(uri);
            return info.exists ? info.size || 0 : 0;
          } catch (error) {
            return 0;
          }
        }),
      );

      const totalSize = sizes.reduce((sum, size) => sum + size, 0);

      const updatedSession = {
        ...currentSession,
        images: newImages,
        size: totalSize,
      };

      try {
        // Update in database
        await updateSessionInDatabase(currentSession.id, {
          images: newImages,
          size: totalSize,
        });

        // Update state
        setCurrentSession(updatedSession);
        setScanSessions(prev =>
          prev.map(session =>
            session.id === currentSession.id ? updatedSession : session,
          ),
        );
      } catch (error) {
        console.error('Error updating session images:', error);
        Alert.alert('Lỗi', 'Không thể cập nhật phiên quét.');
      }
    },
    [currentSession, updateSessionInDatabase],
  );

  const updateCurrentSessionImages = useCallback(
    async (images: string[]) => {
      if (!currentSession) return;

      const updatedImages = [...currentSession.images, ...images];

      // Tính lại kích thước
      const sizes = await Promise.all(
        images.map(async uri => {
          try {
            const info = await FileSystem.getInfoAsync(uri);
            return info.exists ? info.size || 0 : 0;
          } catch (error) {
            return 0;
          }
        }),
      );

      const additionalSize = sizes.reduce((sum, size) => sum + size, 0);
      const totalSize = (currentSession.size || 0) + additionalSize;

      const updatedSession = {
        ...currentSession,
        images: updatedImages,
        size: totalSize,
      };

      try {
        // Update in database
        await updateSessionInDatabase(currentSession.id, {
          images: updatedImages,
          size: totalSize,
        });

        // Update state
        setCurrentSession(updatedSession);
        setScanSessions(prev =>
          prev.map(session =>
            session.id === currentSession.id ? updatedSession : session,
          ),
        );
      } catch (error) {
        console.error('Error updating session images:', error);
        Alert.alert('Lỗi', 'Không thể cập nhật phiên quét.');
      }
    },
    [currentSession, updateSessionInDatabase],
  );

  return (
    <ScanContext.Provider
      value={{
        scanSessions,
        currentSession,
        sessionCount,
        totalStorageUsed,
        isNetworkConnected,
        isLoading,
        addScanSession,
        deleteScanSession,
        updateScanSessionTitle,
        startScan,
        cleanupOldSessions,
        removeImagesFromCurrentSession,
        updateCurrentSessionImages,
        loadSessionsFromDatabase,
      }}
    >
      {children}
    </ScanContext.Provider>
  );
};

export const useScan = () => {
  const context = useContext(ScanContext);
  if (!context) {
    throw new Error('useScan must be used within a ScanProvider');
  }
  return context;
};
