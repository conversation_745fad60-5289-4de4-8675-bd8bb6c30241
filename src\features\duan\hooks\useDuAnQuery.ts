import { useQuery } from '@tanstack/react-query';
import { duanKeys } from '@core/api/keys/duanKeys';
import DuAnService, { DuAnResponse } from '../services/duanService';

interface UseDuAnQueryOptions {
    donViId?: string;
    enabled?: boolean;
}

/**
 * Custom hook for fetching dự án (project) data
 * @param options Query options including donViId filter
 * @returns Query result with dự án data
 */
export const useDuAnQuery = (options: UseDuAnQueryOptions = {}) => {
    const { donViId, enabled = true } = options;
    const duAnService = DuAnService.getInstance();

    return useQuery({
        queryKey: duanKeys.list({ donViId }),
        queryFn: () => duAnService.getAllDuAn(donViId),
        enabled,
        select: (data: DuAnResponse) => data,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
    });
}; 