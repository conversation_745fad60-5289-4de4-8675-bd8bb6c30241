import React from 'react';
import { View, StyleSheet } from 'react-native';
import { COLORS } from '@core/constants/theme';
import Button from '@shared/components/atoms/Button';
import Typography from '@shared/components/atoms/Typography';
import { useNotification } from '@shared/hooks/useNotification';
import NotificationManager from '@shared/components/molecules/NotificationManager';

const NotificationTestScreen: React.FC = () => {
  const { sendNotification } = useNotification();

  const handleTestBasicNotification = async () => {
    try {
      await sendNotification(
        'Thông báo cơ bản',
        '<PERSON><PERSON><PERSON> là thông báo miễn phí',
        'basic',
      );
    } catch (error) {
      console.error('Failed to send basic notification:', error);
    }
  };

  const handleTestPremiumNotification = async () => {
    try {
      await sendNotification(
        'Thông báo Premium',
        'Đ<PERSON><PERSON> là thông báo có phí',
        'premium',
      );
    } catch (error) {
      console.error('Failed to send premium notification:', error);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Typography variant='h4'>Test Thông Báo</Typography>
        <Typography variant='body2' color={COLORS.textSecondary}>
          Gửi thông báo test để kiểm tra chức năng
        </Typography>
      </View>

      <View style={styles.buttonContainer}>
        <Button
          title='Gửi thông báo cơ bản'
          onPress={handleTestBasicNotification}
          style={styles.button}
        />
        <Button
          title='Gửi thông báo Premium'
          onPress={handleTestPremiumNotification}
          variant='secondary'
          style={styles.button}
        />
      </View>

      <View style={styles.notificationList}>
        <Typography variant='h6' style={styles.sectionTitle}>
          Danh sách thông báo
        </Typography>
        <NotificationManager />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  buttonContainer: {
    gap: 12,
    marginBottom: 24,
  },
  button: {
    width: '100%',
  },
  notificationList: {
    flex: 1,
  },
  sectionTitle: {
    marginBottom: 16,
  },
});

export default NotificationTestScreen;
