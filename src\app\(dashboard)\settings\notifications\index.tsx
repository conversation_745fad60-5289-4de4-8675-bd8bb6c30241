import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { MMKV } from 'react-native-mmkv';
import Toast from 'react-native-toast-message';
import HeaderComponent from '@/shared/components/molecules/Header';
import { COLORS } from '@/core/constants/theme';
import { useTheme } from '@/core/theme/theme';

// Local storage để lưu trữ cài đặt thông báo
const storage = new MMKV();
const NOTIFICATION_SETTINGS_KEY = 'notificationSettings';

// Fake notification settings for demo
const defaultNotificationSettings = {
  enableAllNotifications: true,
  newProject: true,
  documentUploaded: true,
  documentShared: true,
  appUpdates: true,
  emailNotifications: false,
  pushNotifications: true,
  soundEnabled: true,
  vibrationEnabled: true,
};

type IconName =
  | 'arrow-back'
  | 'notifications'
  | 'document-text'
  | 'share-social'
  | 'business'
  | 'refresh'
  | 'mail'
  | 'phone-portrait'
  | 'volume-high'
  | 'phone-portrait-outline'
  | 'save';

const NotificationsScreen = () => {
  const router = useRouter();
  const { colors } = useTheme();
  const showToast = (message: string, type: 'success' | 'error') => {
    Toast.show({
      type: type,
      text1: message,
    });
  };

  // Lấy cài đặt thông báo từ storage hoặc sử dụng cài đặt mẫu
  const savedSettings = storage.getString(NOTIFICATION_SETTINGS_KEY);
  const [settings, setSettings] = useState(
    savedSettings ? JSON.parse(savedSettings) : defaultNotificationSettings,
  );
  const [originalSettings, setOriginalSettings] = useState(settings);
  const [hasChanges, setHasChanges] = useState(false);

  // Kiểm tra thay đổi khi settings thay đổi
  useEffect(() => {
    const settingsChanged =
      JSON.stringify(settings) !== JSON.stringify(originalSettings);
    setHasChanges(settingsChanged);
  }, [settings, originalSettings]);

  // Cập nhật cài đặt thông báo
  const updateSettings = (key: string, value: boolean) => {
    // Nếu đây là toggle chính (enableAllNotifications), cập nhật tất cả các thông báo
    if (key === 'enableAllNotifications') {
      const updatedSettings = {
        ...settings,
        enableAllNotifications: value,
        newProject: value,
        documentUploaded: value,
        documentShared: value,
        appUpdates: value,
      };
      setSettings(updatedSettings);
      // Không lưu trực tiếp vào storage, chỉ lưu khi người dùng nhấn "Lưu thay đổi"
    } else {
      const updatedSettings = { ...settings, [key]: value };

      // Kiểm tra xem tất cả các loại thông báo có bật không để cập nhật enableAllNotifications
      if (
        key === 'newProject' ||
        key === 'documentUploaded' ||
        key === 'documentShared' ||
        key === 'appUpdates'
      ) {
        const allEnabled =
          (key === 'newProject' ? value : settings.newProject) &&
          (key === 'documentUploaded' ? value : settings.documentUploaded) &&
          (key === 'documentShared' ? value : settings.documentShared) &&
          (key === 'appUpdates' ? value : settings.appUpdates);

        updatedSettings.enableAllNotifications = allEnabled;
      }

      setSettings(updatedSettings);
      // Không lưu trực tiếp vào storage, chỉ lưu khi người dùng nhấn "Lưu thay đổi"
    }
  };

  // Lưu tất cả cài đặt
  const saveAllSettings = () => {
    storage.set(NOTIFICATION_SETTINGS_KEY, JSON.stringify(settings));
    setOriginalSettings(settings);
    setHasChanges(false);
    showToast('Đã lưu cài đặt thông báo', 'success');
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <HeaderComponent title='Thông báo' />

      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          {/* Main toggle for all notifications */}
          <View style={styles.mainToggleSection}>
            <View style={styles.mainToggleContainer}>
              <View style={styles.toggleIconContainer}>
                <Ionicons
                  name={'notifications' as IconName}
                  size={24}
                  color={COLORS.primary}
                />
              </View>
              <View style={styles.toggleTextContainer}>
                <Text style={styles.mainToggleTitle}>Bật tất cả thông báo</Text>
                <Text style={styles.mainToggleDescription}>
                  Bật hoặc tắt tất cả thông báo trong ứng dụng
                </Text>
              </View>
              <Switch
                value={settings.enableAllNotifications}
                onValueChange={value =>
                  updateSettings('enableAllNotifications', value)
                }
                trackColor={{ false: '#ccc', true: COLORS.primary }}
                thumbColor={'#fff'}
              />
            </View>
          </View>

          {/* Notification types section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Loại thông báo</Text>

            <View style={styles.switchItem}>
              <View style={styles.switchTextContainer}>
                <Ionicons
                  name={'business' as IconName}
                  size={22}
                  color={COLORS.primary}
                />
                <Text style={styles.switchText}>Dự án mới</Text>
              </View>
              <Switch
                value={settings.newProject}
                onValueChange={value => updateSettings('newProject', value)}
                trackColor={{ false: '#ccc', true: COLORS.primary }}
                thumbColor={'#fff'}
                disabled={!settings.enableAllNotifications}
              />
            </View>

            <View style={styles.switchItem}>
              <View style={styles.switchTextContainer}>
                <Ionicons
                  name={'document-text' as IconName}
                  size={22}
                  color={COLORS.primary}
                />
                <Text style={styles.switchText}>Tài liệu đã tải lên</Text>
              </View>
              <Switch
                value={settings.documentUploaded}
                onValueChange={value =>
                  updateSettings('documentUploaded', value)
                }
                trackColor={{ false: '#ccc', true: COLORS.primary }}
                thumbColor={'#fff'}
                disabled={!settings.enableAllNotifications}
              />
            </View>

            <View style={styles.switchItem}>
              <View style={styles.switchTextContainer}>
                <Ionicons
                  name={'share-social' as IconName}
                  size={22}
                  color={COLORS.primary}
                />
                <Text style={styles.switchText}>Tài liệu được chia sẻ</Text>
              </View>
              <Switch
                value={settings.documentShared}
                onValueChange={value => updateSettings('documentShared', value)}
                trackColor={{ false: '#ccc', true: COLORS.primary }}
                thumbColor={'#fff'}
                disabled={!settings.enableAllNotifications}
              />
            </View>

            <View style={styles.switchItem}>
              <View style={styles.switchTextContainer}>
                <Ionicons
                  name={'refresh' as IconName}
                  size={22}
                  color={COLORS.primary}
                />
                <Text style={styles.switchText}>Cập nhật ứng dụng</Text>
              </View>
              <Switch
                value={settings.appUpdates}
                onValueChange={value => updateSettings('appUpdates', value)}
                trackColor={{ false: '#ccc', true: COLORS.primary }}
                thumbColor={'#fff'}
                disabled={!settings.enableAllNotifications}
              />
            </View>
          </View>

          {/* Notification channels section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Kênh thông báo</Text>

            <View style={styles.switchItem}>
              <View style={styles.switchTextContainer}>
                <Ionicons
                  name={'mail' as IconName}
                  size={22}
                  color={COLORS.primary}
                />
                <Text style={styles.switchText}>Email</Text>
              </View>
              <Switch
                value={settings.emailNotifications}
                onValueChange={value =>
                  updateSettings('emailNotifications', value)
                }
                trackColor={{ false: '#ccc', true: COLORS.primary }}
                thumbColor={'#fff'}
              />
            </View>

            <View style={styles.switchItem}>
              <View style={styles.switchTextContainer}>
                <Ionicons
                  name={'phone-portrait' as IconName}
                  size={22}
                  color={COLORS.primary}
                />
                <Text style={styles.switchText}>Thông báo đẩy</Text>
              </View>
              <Switch
                value={settings.pushNotifications}
                onValueChange={value =>
                  updateSettings('pushNotifications', value)
                }
                trackColor={{ false: '#ccc', true: COLORS.primary }}
                thumbColor={'#fff'}
              />
            </View>
          </View>

          {/* Sound and vibration section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Âm thanh & Rung</Text>

            <View style={styles.switchItem}>
              <View style={styles.switchTextContainer}>
                <Ionicons
                  name={'volume-high' as IconName}
                  size={22}
                  color={COLORS.primary}
                />
                <Text style={styles.switchText}>Âm thanh thông báo</Text>
              </View>
              <Switch
                value={settings.soundEnabled}
                onValueChange={value => updateSettings('soundEnabled', value)}
                trackColor={{ false: '#ccc', true: COLORS.primary }}
                thumbColor={'#fff'}
              />
            </View>

            <View style={styles.switchItem}>
              <View style={styles.switchTextContainer}>
                <Ionicons
                  name={'phone-portrait-outline' as IconName}
                  size={22}
                  color={COLORS.primary}
                />
                <Text style={styles.switchText}>Rung khi có thông báo</Text>
              </View>
              <Switch
                value={settings.vibrationEnabled}
                onValueChange={value =>
                  updateSettings('vibrationEnabled', value)
                }
                trackColor={{ false: '#ccc', true: COLORS.primary }}
                thumbColor={'#fff'}
              />
            </View>
          </View>

          {/* Save button at the bottom */}
          {hasChanges && (
            <TouchableOpacity
              style={styles.saveAllButton}
              onPress={saveAllSettings}
            >
              <Ionicons
                name={'save' as IconName}
                size={20}
                color='#fff'
                style={styles.saveAllButtonIcon}
              />
              <Text style={styles.saveAllButtonText}>Lưu thay đổi</Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  backButton: {
    padding: 4,
  },
  rightPlaceholder: {
    width: 32,
  },
  saveButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    // backgroundColor: '#FF6B00',
    borderRadius: 16,
  },
  saveButtonDisabled: {
    backgroundColor: '#f0f0f0',
  },
  saveButtonText: {
    color: '#fff',
    fontWeight: '500',
    fontSize: 14,
  },
  saveButtonTextDisabled: {
    color: '#999',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  mainToggleSection: {
    backgroundColor: '#fff',
    borderRadius: 10,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  mainToggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  toggleIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F1EEEDFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  toggleTextContainer: {
    flex: 1,
  },
  mainToggleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  mainToggleDescription: {
    fontSize: 13,
    color: '#666',
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    color: '#333',
  },
  switchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  switchTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  switchText: {
    marginLeft: 12,
    fontSize: 15,
    color: '#333',
  },
  saveAllButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 16,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  saveAllButtonIcon: {
    marginRight: 8,
  },
  saveAllButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default NotificationsScreen;
