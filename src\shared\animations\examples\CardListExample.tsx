import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { AnimatedView, StaggeredList, AnimatedButton } from '../components';
import { useStaggeredAnimation } from '../index';

const { width } = Dimensions.get('window');

interface CardProps {
  title: string;
  description: string;
  color: string;
  icon: string;
  index: number;
  onPress: () => void;
}

// Dữ liệu mẫu
const CARDS = [
  {
    id: '1',
    title: 'Dự án A',
    description: 'Quản lý tài liệu dự án A',
    color: '#4361ee',
    icon: 'business',
  },
  {
    id: '2',
    title: 'Dự án B',
    description: 'Quản lý tài liệu dự án B',
    color: '#f72585',
    icon: 'cube',
  },
  {
    id: '3',
    title: 'Dự án C',
    description: 'Quản lý tài liệu dự án C',
    color: '#4cc9f0',
    icon: 'folder',
  },
  {
    id: '4',
    title: 'Dự án D',
    description: 'Quản lý tài liệu dự án D',
    color: '#7209b7',
    icon: 'document',
  },
  {
    id: '5',
    title: 'Dự án E',
    description: 'Quản lý tài liệu dự án E',
    color: '#f77f00',
    icon: 'analytics',
  },
];

// Component thẻ đơn lẻ
const Card = ({
  title,
  description,
  color,
  icon,
  index,
  onPress,
}: CardProps) => {
  // Lấy animation style từ hook
  const { getAnimationStyle } = useStaggeredAnimation(CARDS.length, 100, 500);

  return (
    <AnimatedView
      style={[styles.card, { borderLeftColor: color }]}
      duration={600}
      delay={index * 100}
      slideValue={50}
    >
      <TouchableOpacity
        style={styles.cardContent}
        onPress={onPress}
        activeOpacity={0.8}
      >
        <View style={[styles.iconContainer, { backgroundColor: color }]}>
          <Ionicons name={icon as any} size={24} color='#fff' />
        </View>
        <View style={styles.cardTextContainer}>
          <Text style={styles.cardTitle}>{title}</Text>
          <Text style={styles.cardDescription}>{description}</Text>
        </View>
        <Ionicons name='chevron-forward' size={20} color='#999' />
      </TouchableOpacity>
    </AnimatedView>
  );
};

// Component Button với animation
const AnimatedActionButton = ({
  title,
  icon,
  onPress,
}: {
  title: string;
  icon: string;
  onPress: () => void;
}) => {
  return (
    <AnimatedButton style={styles.actionButton} onPress={onPress}>
      <Ionicons name={icon as any} size={20} color='#fff' />
      <Text style={styles.actionButtonText}>{title}</Text>
    </AnimatedButton>
  );
};

// Component danh sách thẻ với animation
const CardListExample = () => {
  const handleCardPress = (id: string) => {
    console.log(`Card pressed: ${id}`);
  };

  const handleActionPress = (action: string) => {
    console.log(`Action pressed: ${action}`);
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar barStyle='dark-content' backgroundColor='#fff' />

      {/* Header có animation */}
      <AnimatedView style={styles.header} duration={800} delay={0}>
        <Text style={styles.headerTitle}>Danh sách dự án</Text>
        <Text style={styles.headerSubtitle}>
          Chọn một dự án để xem chi tiết
        </Text>
      </AnimatedView>

      {/* Nút tác vụ nhanh có animation */}
      <View style={styles.actionsContainer}>
        <AnimatedActionButton
          title='Tạo mới'
          icon='add-circle'
          onPress={() => handleActionPress('create')}
        />
        <AnimatedActionButton
          title='Tìm kiếm'
          icon='search'
          onPress={() => handleActionPress('search')}
        />
        <AnimatedActionButton
          title='Lọc'
          icon='filter'
          onPress={() => handleActionPress('filter')}
        />
      </View>

      {/* Danh sách thẻ có animation */}
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {CARDS.map((card, index) => (
          <Card
            key={card.id}
            title={card.title}
            description={card.description}
            color={card.color}
            icon={card.icon}
            index={index}
            onPress={() => handleCardPress(card.id)}
          />
        ))}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eaedf2',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#333',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eaedf2',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4361ee',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderLeftWidth: 6,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  cardTextContainer: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  cardDescription: {
    fontSize: 14,
    color: '#666',
  },
});

export default CardListExample;
