import { BaseStorage } from './baseStorage';
import { STORAGE_KEYS } from './constants';

/**
 * Type định nghĩa cấu trúc loại văn bản
 * <PERSON><PERSON> hợp với DocumentType từ service
 */
export interface DocumentTypeData {
    NghiepVuID: string;
    NghiepVu: string;
}

/**
 * Service quản lý lưu trữ loại văn bản
 */
export class DocumentStorage extends BaseStorage {
    /**
     * Lưu loại văn bản mặc định
     * @param documentTypeId ID loại văn bản mặc định
     */
    setDefaultDocumentType(documentTypeId: string): boolean {
        if (!documentTypeId) return false;
        this.setString(STORAGE_KEYS.DOCUMENT.DEFAULT_TYPE, documentTypeId);
        return true;
    }

    /**
     * Lấy ID loại văn bản mặc định
     */
    getDefaultDocumentType(): string | undefined {
        return this.getString(STORAGE_KEYS.DOCUMENT.DEFAULT_TYPE);
    }

    /**
     * Xóa loại văn bản mặc định
     */
    clearDefaultDocumentType(): void {
        this.deleteKey(STORAGE_KEYS.DOCUMENT.DEFAULT_TYPE);
    }

    /**
     * Lưu danh sách loại văn bản đã sử dụng gần đây
     * @param documentTypes Danh sách loại văn bản
     */
    setRecentDocumentTypes(documentTypes: DocumentTypeData[]): void {
        if (!Array.isArray(documentTypes)) {
            console.error('Provided document types is not an array');
            return;
        }
        this.setObject(STORAGE_KEYS.DOCUMENT.RECENT_TYPES, documentTypes);
    }

    /**
     * Lấy danh sách loại văn bản đã sử dụng gần đây
     */
    getRecentDocumentTypes(): DocumentTypeData[] {
        return this.getObject<DocumentTypeData[]>(STORAGE_KEYS.DOCUMENT.RECENT_TYPES) || [];
    }

    /**
     * Thêm loại văn bản vào danh sách đã sử dụng gần đây
     * @param documentType Loại văn bản cần thêm vào
     * @param maxItems Số lượng tối đa loại văn bản lưu trữ
     */
    addRecentDocumentType(documentType: DocumentTypeData, maxItems: number = 3): DocumentTypeData[] {
        if (!documentType || !documentType.NghiepVuID) return this.getRecentDocumentTypes();

        const recentDocumentTypes = this.getRecentDocumentTypes();

        // Lọc bỏ loại văn bản hiện tại nếu đã có trong danh sách
        const filtered = recentDocumentTypes.filter(dt => dt.NghiepVuID !== documentType.NghiepVuID);

        // Thêm loại văn bản mới vào đầu danh sách và giới hạn số lượng
        const updated = [documentType, ...filtered].slice(0, maxItems);

        // Lưu danh sách mới
        this.setRecentDocumentTypes(updated);

        return updated;
    }

    /**
     * Xóa toàn bộ dữ liệu loại văn bản
     */
    clearAllDocumentTypeData(): void {
        this.deleteKey(STORAGE_KEYS.DOCUMENT.DEFAULT_TYPE);
        this.deleteKey(STORAGE_KEYS.DOCUMENT.RECENT_TYPES);
    }
}

// Export singleton instance
export const documentStorage = new DocumentStorage(); 