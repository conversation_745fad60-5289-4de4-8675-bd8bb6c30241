import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SectionList,
  StyleSheet,
  Image,
} from 'react-native';
import { useRouter } from 'expo-router';
import { <PERSON><PERSON><PERSON>, Wizard } from 'react-native-ui-lib';
import { ROUTES } from '@core/constants/routes';

const groupedFeatures = [
  {
    title: 'Quản lý',
    data: [
      {
        id: 'view-data',
        name: '<PERSON>em dữ liệu',
        icon: { uri: 'https://img.icons8.com/color/96/view-file.png' },
      },
      {
        id: 'manage-records',
        name: '<PERSON><PERSON><PERSON><PERSON> <PERSON>ý hồ sơ',
        icon: {
          uri: 'https://img.icons8.com/color/96/folder-invoices--v1.png',
        },
      },
    ],
  },
  {
    title: '<PERSON><PERSON> thống',
    data: [
      {
        id: 'settings',
        name: '<PERSON><PERSON><PERSON> đặt hệ thống',
        icon: { uri: 'https://img.icons8.com/color/96/settings--v1.png' },
      },
      {
        id: 'notifications',
        name: 'Thông báo',
        icon: {
          uri: 'https://img.icons8.com/color/96/appointment-reminders--v1.png',
        },
      },
    ],
  },
  {
    title: 'Hỗ trợ',
    data: [
      {
        id: 'user-guide',
        name: 'Hướng dẫn sử dụng',
        icon: { uri: 'https://img.icons8.com/color/96/help.png' },
      },
      {
        id: 'support',
        name: 'Hỗ trợ kỹ thuật',
        icon: { uri: 'https://img.icons8.com/color/96/customer-support.png' },
      },
    ],
  },
];

export default function FeatureSelectionScreen() {
  const router = useRouter();
  const [selectedFeatures, setSelectedFeatures] = useState<Set<string>>(
    new Set(),
  );
  const [activeStep, setActiveStep] = useState(1);

  const toggleSelect = useCallback((featureId: string) => {
    setSelectedFeatures(prev => {
      const newSet = new Set(prev);
      newSet.has(featureId) ? newSet.delete(featureId) : newSet.add(featureId);
      return newSet;
    });
  }, []);

  const renderItem = useCallback(
    ({ item }: { item: (typeof groupedFeatures)[0]['data'][0] }) => {
      const isSelected = selectedFeatures.has(item.id);
      return (
        <TouchableOpacity
          style={[styles.listItem, isSelected && styles.listItemSelected]}
          onPress={() => toggleSelect(item.id)}
          activeOpacity={0.85}
        >
          <View style={styles.iconBox}>
            <Image source={item.icon} style={styles.icon} />
          </View>
          <Text style={styles.itemText}>{item.name}</Text>
          {isSelected && (
            <View style={styles.checkMark}>
              <Text style={styles.checkText}>✓</Text>
            </View>
          )}
        </TouchableOpacity>
      );
    },
    [selectedFeatures, toggleSelect],
  );

  const actionBarActions = useMemo(
    () => [
      { label: 'Quay lại', onPress: () => console.log('Back') },
      {
        label: 'Tiếp tục',
        onPress: () => {
          const selectedArray = Array.from(selectedFeatures);
          console.log('Selected Features:', selectedArray);
          router.replace(ROUTES.HOME);
        },
        disabled: selectedFeatures.size === 0,
      },
    ],
    [selectedFeatures],
  );

  return (
    <View style={styles.container}>
      <Wizard activeIndex={activeStep} containerStyle={styles.wizard}>
        <Wizard.Step label='Chọn ứng dụng' state={Wizard.States.ENABLED} />
        <Wizard.Step label='Chọn chức năng' state={Wizard.States.ENABLED} />
      </Wizard>
      <SectionList
        sections={groupedFeatures}
        keyExtractor={item => item.id}
        renderItem={renderItem}
        renderSectionHeader={({ section: { title } }) => (
          <Text style={styles.sectionHeader}>{title}</Text>
        )}
        contentContainerStyle={styles.listContent}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
        stickySectionHeadersEnabled={false}
        initialNumToRender={10}
      />

      <ActionBar actions={actionBarActions} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: '15%',
    paddingHorizontal: 20,
    backgroundColor: '#fff',
  },
  wizard: {
    marginBottom: 25,
    backgroundColor: '',
    paddingHorizontal: '15%',
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4B55E1',
    paddingVertical: 8,
  },
  listContent: {
    paddingBottom: 30,
  },
  separator: {
    height: 12,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f7f7f7',
    padding: 16,
    borderRadius: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: 1 },
    shadowRadius: 1,
    position: 'relative',
  },
  listItemSelected: {
    borderColor: '#4B55E1',
    borderWidth: 2,
    backgroundColor: '#eef0ff',
  },
  iconBox: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: '#eef1ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  icon: {
    width: 28,
    height: 28,
    resizeMode: 'contain',
  },
  itemText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  checkMark: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#4B55E1',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});
