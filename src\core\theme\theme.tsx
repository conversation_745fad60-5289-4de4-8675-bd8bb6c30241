import React, { createContext, useState, useContext, ReactNode } from 'react';
import {
  ThemeManager,
  Colors,
  Typography,
  Spacings,
  BorderRadiuses,
  Shadows,
} from 'react-native-ui-lib';

// Light theme colors
const lightColors = {
  primary: '#1E3AAF',
  secondary: '#4CAF50',
  success: '#4CAF50',
  error: '#F44336',
  warning: '#FFC107',
  info: '#2196F3',
  textPrimary: '#212121',
  textSecondary: '#757575',
  textDisabled: '#9E9E9E',
  background: '#f5f5f5',
  surface: '#ffffff',
  border: '#e0e0e0',
};

// Dark theme colors
const darkColors = {
  primary: '#4A90E2',
  secondary: '#66BB6A',
  success: '#66BB6A',
  error: '#EF5350',
  warning: '#FFD54F',
  info: '#42A5F5',
  textPrimary: '#ffffff',
  textSecondary: '#B0B0B0',
  textDisabled: '#666666',
  background: '#121212',
  surface: '#1E1E1E',
  border: '#333333',
};

// Thiết lập màu sắc chính
Colors.loadColors(lightColors);

// Thiết lập typography
Typography.loadTypographies({
  h1: {
    fontSize: 32,
    fontWeight: '700',
    lineHeight: 40,
    fontFamily: 'sfpd-bold',
  },
  h2: {
    fontSize: 28,
    fontWeight: '600',
    lineHeight: 36,
    fontFamily: 'sfpd-semibold',
  },
  h3: {
    fontSize: 24,
    fontWeight: '600',
    lineHeight: 32,
    fontFamily: 'sfpd-semibold',
  },
  body: {
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 24,
    fontFamily: 'sfpd-regular',
  },
  bodySmall: {
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 20,
    fontFamily: 'sfpd-regular',
  },
  button: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
    fontFamily: 'sfpd-medium',
  },
});

// Thiết lập spacing
Spacings.loadSpacings({
  s1: 4,
  s2: 8,
  s3: 12,
  s4: 16,
  s5: 24,
  s6: 32,
});

// Thiết lập border radius
BorderRadiuses.loadBorders({
  br20: 4,
  br30: 8,
  br40: 12,
});

// Thiết lập theme cho components
ThemeManager.setComponentTheme('Button', {
  backgroundColor: Colors.primary,
  color: Colors.white,
  borderRadius: BorderRadiuses.br20,
  paddingHorizontal: Spacings.s4,
  paddingVertical: Spacings.s2,
});

// Theme context
type ThemeType = 'light' | 'dark';

interface ThemeContextType {
  theme: ThemeType;
  toggleTheme: () => void;
  colors: typeof lightColors;
}

const ThemeContext = createContext<ThemeContextType>({
  theme: 'light',
  toggleTheme: () => { },
  colors: lightColors,
});

export const ThemeProvider = ({ children }: { children: ReactNode }) => {
  const [themeMode, setThemeMode] = useState<ThemeType>('light');

  const toggleTheme = () => {
    const newTheme = themeMode === 'light' ? 'dark' : 'light';
    setThemeMode(newTheme);

    // Update colors based on theme
    const themeColors = newTheme === 'light' ? lightColors : darkColors;
    Colors.loadColors(themeColors);
  };

  const currentColors = themeMode === 'light' ? lightColors : darkColors;

  return (
    <ThemeContext.Provider
      value={{ theme: themeMode, toggleTheme, colors: currentColors }}
    >
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  return {
    colors: Colors,
    typography: Typography,
    spacings: Spacings,
    borderRadiuses: BorderRadiuses,
    shadows: Shadows,
  };
};

export const useThemeMode = () => useContext(ThemeContext);
