import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  ScrollView,
  Image,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import {
  Ionicons,
  MaterialIcons,
  AntDesign,
  FontAwesome5,
} from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Colors } from 'react-native-ui-lib';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/core/theme/theme';

// Interface for subscription package data
interface SubscriptionPackage {
  id: string;
  name: string;
  price: string;
  period: string;
  features: string[];
  color: string;
  popular: boolean;
}

// Interface for active subscription data
interface ActiveSubscription {
  id: string;
  startDate: string;
  endDate: string;
  autoRenew: boolean;
  paymentMethod: string;
}

// Default subscription packages as fallback
const DEFAULT_SUBSCRIPTION_PACKAGES: SubscriptionPackage[] = [
  {
    id: 'basic',
    name: '<PERSON><PERSON> bản',
    price: '49.000',
    period: 'tháng',
    features: [
      '100 tài liệu/tháng',
      'O<PERSON> cơ bản',
      'Lưu trữ đám mây 1GB',
      'Xuất PDF, JPEG',
    ],
    color: '#4F6CFF',
    popular: false,
  },
  {
    id: 'premium',
    name: 'Cao cấp',
    price: '99.000',
    period: 'tháng',
    features: [
      'Không giới hạn tài liệu',
      'OCR nâng cao',
      'Lưu trữ đám mây 5GB',
      'Xuất tất cả định dạng',
      'Hỗ trợ ưu tiên',
    ],
    color: '#FF6B6B',
    popular: true,
  },
  {
    id: 'business',
    name: 'Doanh nghiệp',
    price: '199.000',
    period: 'tháng',
    features: [
      'Không giới hạn tài liệu',
      'OCR nâng cao',
      'Lưu trữ đám mây 20GB',
      'Xuất tất cả định dạng',
      'Quản lý người dùng',
      'Hỗ trợ 24/7',
    ],
    color: '#38D9A9',
    popular: false,
  },
];

// Default active subscription as fallback
const DEFAULT_ACTIVE_SUBSCRIPTION: ActiveSubscription = {
  id: 'premium',
  startDate: '01/06/2023',
  endDate: '01/06/2024',
  autoRenew: true,
  paymentMethod: 'Visa •••• 4242',
};

export default function SubscriptionsScreen() {
  const router = useRouter();
  const { colors } = useTheme();
  const [activeTab, setActiveTab] = useState('packages');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [subscriptionPackages, setSubscriptionPackages] = useState<
    SubscriptionPackage[]
  >(DEFAULT_SUBSCRIPTION_PACKAGES);
  const [activeSubscription, setActiveSubscription] =
    useState<ActiveSubscription | null>(DEFAULT_ACTIVE_SUBSCRIPTION);

  // Hàm mô phỏng việc tải dữ liệu
  const fetchSubscriptionData = () => {
    setIsLoading(true);

    // Giả lập tải dữ liệu trong 500ms
    setTimeout(() => {
      // Sử dụng trực tiếp dữ liệu mặc định
      setSubscriptionPackages(DEFAULT_SUBSCRIPTION_PACKAGES);
      setActiveSubscription(DEFAULT_ACTIVE_SUBSCRIPTION);
      setIsLoading(false);
    }, 500);
  };

  // Load subscription data
  useEffect(() => {
    fetchSubscriptionData();
  }, []);

  // Render subscription package card
  const renderPackageCard = (pkg: SubscriptionPackage) => {
    const isActive = activeSubscription?.id === pkg.id;

    return (
      <TouchableOpacity
        key={pkg.id}
        style={[styles.packageCard, isActive && styles.activePackageCard]}
        activeOpacity={0.9}
        onPress={() => {}}
      >
        {pkg.popular && (
          <View style={styles.popularBadge}>
            <Text style={styles.popularText}>Phổ biến</Text>
          </View>
        )}

        <View
          style={[styles.packageHeader, { backgroundColor: pkg.color + '20' }]}
        >
          <Text style={styles.packageName}>{pkg.name}</Text>
          <View style={styles.priceContainer}>
            <Text style={styles.price}>{pkg.price}đ</Text>
            <Text style={styles.period}>/{pkg.period}</Text>
          </View>
        </View>

        <View style={styles.featuresContainer}>
          {pkg.features.map((feature: string, index: number) => (
            <View key={index} style={styles.featureItem}>
              <Ionicons
                name='checkmark-circle'
                size={18}
                color={pkg.color}
                style={styles.featureIcon}
              />
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </View>

        <TouchableOpacity
          style={[
            styles.actionButton,
            { backgroundColor: isActive ? '#f0f0f0' : pkg.color },
          ]}
        >
          <Text
            style={[
              styles.actionButtonText,
              { color: isActive ? '#333' : '#fff' },
            ]}
          >
            {isActive ? 'Gói hiện tại' : 'Nâng cấp'}
          </Text>
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };

  // Render subscription details
  const renderSubscriptionDetails = () => {
    if (!activeSubscription) {
      return (
        <View style={styles.noSubscriptionContainer}>
          <Ionicons name='alert-circle-outline' size={60} color='#ccc' />
          <Text style={styles.noSubscriptionText}>
            Bạn chưa đăng ký gói dịch vụ nào
          </Text>
          <TouchableOpacity
            style={styles.viewPackagesButton}
            onPress={() => setActiveTab('packages')}
          >
            <Text style={styles.viewPackagesButtonText}>
              Xem các gói dịch vụ
            </Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.subscriptionDetails}>
        <View style={styles.detailsHeader}>
          <View style={styles.packageIconContainer}>
            <Ionicons name='star' size={24} color='#fff' />
          </View>
          <View style={styles.packageInfo}>
            <Text style={styles.packageTitle}>
              Gói{' '}
              {subscriptionPackages.find(
                pkg => pkg.id === activeSubscription.id,
              )?.name || 'Cao cấp'}
            </Text>
            <Text style={styles.packageStatus}>Đang hoạt động</Text>
          </View>
        </View>

        <View style={styles.divider} />

        <View style={styles.detailsContent}>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Ngày bắt đầu</Text>
            <Text style={styles.detailValue}>
              {activeSubscription.startDate}
            </Text>
          </View>

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Ngày kết thúc</Text>
            <Text style={styles.detailValue}>{activeSubscription.endDate}</Text>
          </View>

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Tự động gia hạn</Text>
            <View style={styles.autoRenewContainer}>
              <Text style={styles.detailValue}>
                {activeSubscription.autoRenew ? 'Bật' : 'Tắt'}
              </Text>
              <TouchableOpacity style={styles.toggleButton}>
                <Ionicons
                  name={
                    activeSubscription.autoRenew ? 'toggle' : 'toggle-outline'
                  }
                  size={36}
                  color={
                    activeSubscription.autoRenew ? Colors.buttonPrimary : '#ccc'
                  }
                />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Phương thức thanh toán</Text>
            <View style={styles.paymentContainer}>
              <FontAwesome5 name='cc-visa' size={18} color='#1A1F71' />
              <Text style={styles.detailValue}>
                {activeSubscription.paymentMethod}
              </Text>
              <TouchableOpacity style={styles.changeButton}>
                <Text style={styles.changeText}>Thay đổi</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <TouchableOpacity style={styles.cancelButton}>
          <Text style={styles.cancelText}>Hủy đăng ký</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle='dark-content' />

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'packages' && styles.activeTab]}
          onPress={() => setActiveTab('packages')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'packages' && styles.activeTabText,
            ]}
          >
            Gói dịch vụ
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'subscription' && styles.activeTab]}
          onPress={() => setActiveTab('subscription')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'subscription' && styles.activeTabText,
            ]}
          >
            Đăng ký của tôi
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={fetchSubscriptionData}
            colors={[Colors.buttonPrimary]}
          />
        }
      >
        {error && (
          <View style={styles.errorContainer}>
            <Ionicons name='alert-circle-outline' size={24} color='#FF5A5A' />
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        {activeTab === 'packages' ? (
          <View style={styles.packagesContainer}>
            {subscriptionPackages.map(pkg => renderPackageCard(pkg))}

            <View style={styles.infoSection}>
              <View style={styles.infoIcon}>
                <Ionicons
                  name='information-circle-outline'
                  size={24}
                  color='#666'
                />
              </View>
              <Text style={styles.infoText}>
                Tất cả các gói đăng ký được tính phí theo chu kỳ thanh toán đã
                chọn. Bạn có thể hủy bất kỳ lúc nào trong phần "Đăng ký của
                tôi".
              </Text>
            </View>
          </View>
        ) : (
          renderSubscriptionDetails()
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 8,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: Colors.buttonPrimary,
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: Colors.buttonPrimary,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  packagesContainer: {
    padding: 16,
  },
  packageCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    position: 'relative',
  },
  activePackageCard: {
    borderWidth: 2,
    borderColor: Colors.buttonPrimary,
  },
  popularBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: '#FF6B6B',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    zIndex: 1,
  },
  popularText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  packageHeader: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    backgroundColor: '#f0f7ff',
  },
  packageName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  price: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  period: {
    fontSize: 14,
    color: '#666',
    marginLeft: 2,
  },
  featuresContainer: {
    padding: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  featureIcon: {
    marginRight: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#444',
  },
  actionButton: {
    backgroundColor: Colors.buttonPrimary,
    paddingVertical: 12,
    alignItems: 'center',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 8,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  subscriptionDetails: {
    margin: 16,
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  detailsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  packageIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.buttonPrimary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  packageInfo: {
    flex: 1,
  },
  packageTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  packageStatus: {
    fontSize: 14,
    color: '#4CAF50',
  },
  divider: {
    height: 1,
    backgroundColor: '#eaeaea',
  },
  detailsContent: {
    padding: 16,
  },
  detailItem: {
    marginBottom: 16,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  autoRenewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  toggleButton: {
    padding: 4,
  },
  paymentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  changeButton: {
    marginLeft: 'auto',
  },
  changeText: {
    color: Colors.buttonPrimary,
    fontSize: 14,
    fontWeight: '500',
  },
  cancelButton: {
    padding: 16,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#eaeaea',
  },
  cancelText: {
    color: '#FF5A5A',
    fontSize: 16,
    fontWeight: '500',
  },
  infoSection: {
    flexDirection: 'row',
    backgroundColor: '#f0f5ff',
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
  },
  infoIcon: {
    marginRight: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 13,
    color: '#666',
    lineHeight: 18,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFE5E5',
    padding: 16,
    margin: 16,
    borderRadius: 8,
  },
  errorText: {
    color: '#D32F2F',
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  noSubscriptionContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    marginTop: 40,
  },
  noSubscriptionText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginVertical: 16,
  },
  viewPackagesButton: {
    backgroundColor: Colors.buttonPrimary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 16,
  },
  viewPackagesButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
