import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Button,
  Share,
  Alert,
  Platform,
  ScrollView,
} from 'react-native';
import * as FileSystem from 'expo-file-system';
import { getDatabasePath } from '@core/database';
import { SettingsRepository } from '@core/database/repositories/settingsRepository';
import { useConfigService } from '@features/settings/hooks/useConfigService';

export default function DatabaseDebug() {
  const [dbPath, setDbPath] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [configData, setConfigData] = useState<Record<string, string>>({});
  const configService = useConfigService();

  useEffect(() => {
    const loadDbPath = async () => {
      try {
        const path = await getDatabasePath();
        setDbPath(path);
      } catch (error) {
        console.error('Error getting DB path:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDbPath();
  }, []);

  const shareDbPath = async () => {
    try {
      await Share.share({
        message: `Database path: ${dbPath}`,
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const showSettingsDebug = async () => {
    const settingsRepo = new SettingsRepository();
    const path = await settingsRepo.debug();
    console.log('Settings debug path:', path);
    alert(`Database path: ${path}`);
  };

  const exportDatabase = async () => {
    try {
      setLoading(true);
      // Đường dẫn đến file database
      const dbPath = await getDatabasePath();

      // Kiểm tra xem file có tồn tại không
      const fileInfo = await FileSystem.getInfoAsync(dbPath);
      if (!fileInfo.exists) {
        Alert.alert('Lỗi', 'File database không tồn tại');
        setLoading(false);
        return;
      }

      // Tạo bản sao trong thư mục Documents (có thể chia sẻ)
      const documentsDir = FileSystem.documentDirectory;
      const exportPath = `${documentsDir}export_nts_document_ai_${Date.now()}.db`;

      await FileSystem.copyAsync({
        from: dbPath,
        to: exportPath,
      });

      console.log(`Đã xuất database sang: ${exportPath}`);

      // Chia sẻ file đã xuất
      if (Platform.OS === 'ios') {
        await Share.share({
          url: exportPath,
          message: 'SQLite Database Export',
        });
      } else {
        await Share.share({
          title: 'SQLite Database Export',
          message: exportPath,
        });
      }
    } catch (error) {
      console.error('Error exporting database:', error);
      Alert.alert('Lỗi', 'Không thể xuất file database');
    } finally {
      setLoading(false);
    }
  };

  const executeQuery = async () => {
    try {
      setLoading(true);
      // Đây chỉ là một ví dụ đơn giản, trong thực tế bạn có thể
      // tạo UI cho phép nhập query và hiển thị kết quả
      const settingsRepo = new SettingsRepository();
      const settings = await settingsRepo.getAllSettings();
      Alert.alert('Kết quả query', JSON.stringify(settings, null, 2));
    } catch (error) {
      console.error('Error executing query:', error);
      Alert.alert('Lỗi', 'Không thể thực thi query');
    } finally {
      setLoading(false);
    }
  };

  const testLoadConfig = async () => {
    try {
      setLoading(true);
      const allConfig = await configService.getAllConfig();
      setConfigData(allConfig);
      setLoading(false);
    } catch (error) {
      console.error('Error loading config:', error);
      Alert.alert('Lỗi', 'Không thể tải cấu hình');
      setLoading(false);
    }
  };

  const testSaveProject = async () => {
    try {
      const projectId = `project_${Date.now()}`;
      const projectName = `Test Project ${new Date().toLocaleTimeString()}`;

      await configService.saveProject({
        id: projectId,
        name: projectName,
      });

      Alert.alert(
        'Thành công',
        `Đã lưu dự án:\nID: ${projectId}\nTên: ${projectName}`,
      );
      testLoadConfig();
    } catch (error) {
      console.error('Error saving project:', error);
      Alert.alert('Lỗi', 'Không thể lưu dự án');
    }
  };

  const testSaveDocType = async () => {
    try {
      const docTypeId = `doc_type_${Date.now()}`;
      const docTypeName = `loại tài liệu test ${new Date().toLocaleTimeString()}`;

      await configService.saveDocumentType(docTypeId, docTypeName);

      Alert.alert(
        'Thành công',
        `Đã lưu loại tài liệu:\nID: ${docTypeId}\nTên: ${docTypeName}`,
      );
      testLoadConfig();
    } catch (error) {
      console.error('Error saving document type:', error);
      Alert.alert('Lỗi', 'Không thể lưu loại tài liệu');
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Thông tin Database</Text>

      {loading ? (
        <Text>Đang tải thông tin database...</Text>
      ) : (
        <>
          <Text style={styles.path}>Đường dẫn Database:</Text>
          <Text style={styles.pathValue}>{dbPath}</Text>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Thao tác với Database</Text>
            <View style={styles.buttons}>
              <Button title='Chia sẻ đường dẫn' onPress={shareDbPath} />

              <Button title='Hiển thị debug' onPress={showSettingsDebug} />

              <Button
                title='Xuất database để kiểm tra'
                onPress={exportDatabase}
              />

              <Button title='Thực thi query mẫu' onPress={executeQuery} />
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Test Cài đặt SQLite</Text>
            <View style={styles.buttons}>
              <Button title='Tải cấu hình' onPress={testLoadConfig} />

              <Button title='Lưu dự án test' onPress={testSaveProject} />

              <Button
                title='Lưu loại tài liệu test'
                onPress={testSaveDocType}
              />
            </View>
          </View>

          {Object.keys(configData).length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Dữ liệu cấu hình</Text>
              {Object.entries(configData).map(([key, value]) => (
                <View key={key} style={styles.configItem}>
                  <Text style={styles.configKey}>{key}:</Text>
                  <Text style={styles.configValue}>{value}</Text>
                </View>
              ))}
            </View>
          )}
        </>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  path: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 10,
  },
  pathValue: {
    fontSize: 14,
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 5,
    marginVertical: 10,
  },
  section: {
    marginTop: 20,
    padding: 15,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eee',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  buttons: {
    gap: 10,
  },
  configItem: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingVertical: 8,
  },
  configKey: {
    flex: 1,
    fontWeight: 'bold',
  },
  configValue: {
    flex: 2,
  },
});
