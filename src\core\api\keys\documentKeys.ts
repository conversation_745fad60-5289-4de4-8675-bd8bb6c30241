export const documentKeys = {
    all: ['documents'] as const,
    types: () => [...documentKeys.all, 'types'] as const,
    type: (id: string) => [...documentKeys.types(), id] as const,
    lists: () => [...documentKeys.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...documentKeys.lists(), { filters }] as const,
    details: () => [...documentKeys.all, 'detail'] as const,
    detail: (id: string) => [...documentKeys.details(), id] as const,
}; 