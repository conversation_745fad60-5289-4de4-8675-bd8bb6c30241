import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import Header from '@/shared/components/molecules/Header';

// Fix TypeScript error by properly typing the icon names
type IconName = keyof typeof Ionicons.glyphMap;

const AppearanceScreen = () => {
  const router = useRouter();
  const [darkMode, setDarkMode] = useState(false);
  const [fontSizeIndex, setFontSizeIndex] = useState(1); // 0: Small, 1: Medium, 2: Large
  const [animationsEnabled, setAnimationsEnabled] = useState(true);

  const fontSizes = ['Nhỏ', 'Vừa', 'Lớn'];

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Header title='Tùy chỉnh giao diện' />

      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          {/* Chủ đề */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Chủ đề</Text>

            <View style={styles.switchItem}>
              <View style={styles.switchTextContainer}>
                <Ionicons name={'moon' as IconName} size={22} color='#FBBC05' />
                <Text style={styles.switchText}>Chế độ tối</Text>
              </View>
              <Switch
                value={darkMode}
                onValueChange={setDarkMode}
                trackColor={{ false: '#ccc', true: '#FBBC05' }}
                thumbColor={'#fff'}
              />
            </View>
          </View>

          {/* Kích thước phông chữ */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Kích thước phông chữ</Text>

            <View style={styles.optionsContainer}>
              {fontSizes.map((size, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.optionButton,
                    fontSizeIndex === index && styles.selectedOption,
                  ]}
                  onPress={() => setFontSizeIndex(index)}
                >
                  <Text
                    style={[
                      styles.optionText,
                      fontSizeIndex === index && styles.selectedOptionText,
                    ]}
                  >
                    {size}
                  </Text>
                  {fontSizeIndex === index && (
                    <Ionicons
                      name={'checkmark-circle' as IconName}
                      size={20}
                      color='#FBBC05'
                    />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Hiệu ứng */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Hiệu ứng</Text>

            <View style={styles.switchItem}>
              <View style={styles.switchTextContainer}>
                <Ionicons
                  name={'aperture' as IconName}
                  size={22}
                  color='#FBBC05'
                />
                <Text style={styles.switchText}>Bật hiệu ứng chuyển động</Text>
              </View>
              <Switch
                value={animationsEnabled}
                onValueChange={setAnimationsEnabled}
                trackColor={{ false: '#ccc', true: '#FBBC05' }}
                thumbColor={'#fff'}
              />
            </View>
          </View>

          {/* Preview Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Xem trước</Text>
            <View
              style={[
                styles.previewContainer,
                darkMode ? styles.darkModePreview : styles.lightModePreview,
              ]}
            >
              <Text
                style={[
                  styles.previewTitle,
                  darkMode ? styles.darkModeText : styles.lightModeText,
                  { fontSize: 16 + fontSizeIndex * 2 },
                ]}
              >
                Tài liệu mẫu
              </Text>
              <Text
                style={[
                  styles.previewDescription,
                  darkMode ? styles.darkModeText : styles.lightModeText,
                  { fontSize: 14 + fontSizeIndex * 2 },
                ]}
              >
                Đây là văn bản mẫu để hiển thị giao diện
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  backButton: {
    padding: 4,
  },
  rightPlaceholder: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    color: '#333',
  },
  switchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  switchTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  switchText: {
    marginLeft: 12,
    fontSize: 15,
    color: '#333',
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  optionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  selectedOption: {
    backgroundColor: '#FFF8E1',
    borderColor: '#FBBC05',
  },
  optionText: {
    fontSize: 15,
    color: '#495057',
  },
  selectedOptionText: {
    color: '#FBBC05',
    fontWeight: '500',
  },
  previewContainer: {
    borderRadius: 12,
    padding: 20,
    marginTop: 10,
  },
  lightModePreview: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  darkModePreview: {
    backgroundColor: '#222',
    borderWidth: 1,
    borderColor: '#333',
  },
  lightModeText: {
    color: '#333',
  },
  darkModeText: {
    color: '#fff',
  },
  previewTitle: {
    fontWeight: 'bold',
    marginBottom: 10,
  },
  previewDescription: {
    lineHeight: 22,
  },
});

export default AppearanceScreen;
