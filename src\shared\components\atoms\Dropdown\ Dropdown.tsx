// src/components/UI/Dropdown/Dropdown.tsx
import { useState } from 'react';
import {
  View,
  Text,
  Pressable,
  FlatList,
  Modal,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

type DropdownItem = {
  label: string;
  value: any;
};

interface DropdownProps {
  data: DropdownItem[];
  value?: any;
  placeholder?: string;
  onSelect: (item: DropdownItem) => void;
}

export default function Dropdown({
  data,
  value,
  placeholder = 'Chọn...',
  onSelect,
}: DropdownProps) {
  const [visible, setVisible] = useState(false);

  const selectedItem = data.find(item => item.value === value);

  return (
    <View style={{ marginVertical: 10 }}>
      {/* Ô nhấn mở dropdown */}
      <Pressable onPress={() => setVisible(true)} style={styles.dropdownButton}>
        <Text style={styles.selectedText}>
          {selectedItem ? selectedItem.label : placeholder}
        </Text>
        <Ionicons name='chevron-down' size={20} color='#555' />
      </Pressable>

      {/* Modal danh sách chọn */}
      <Modal visible={visible} transparent animationType='fade'>
        <Pressable
          onPress={() => setVisible(false)}
          style={styles.modalOverlay}
        >
          <View style={styles.modalContent}>
            <FlatList
              data={data}
              keyExtractor={item => item.value.toString()}
              renderItem={({ item }) => (
                <Pressable
                  onPress={() => {
                    onSelect(item);
                    setVisible(false);
                  }}
                  style={styles.item}
                >
                  <Text style={styles.itemText}>{item.label}</Text>
                </Pressable>
              )}
            />
          </View>
        </Pressable>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  dropdownButton: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  selectedText: {
    color: '#333',
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.2)',
    justifyContent: 'center',
  },
  modalContent: {
    marginHorizontal: 20,
    backgroundColor: 'white',
    borderRadius: 8,
    paddingVertical: 10,
    maxHeight: 400,
  },
  item: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  itemText: {
    fontSize: 16,
    color: '#333',
  },
});
