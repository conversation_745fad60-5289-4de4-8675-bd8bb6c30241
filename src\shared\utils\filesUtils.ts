import { MMKV } from 'react-native-mmkv';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';

export const clearAllScannedDocuments = async () => {
  const docDir = FileSystem.documentDirectory;
  if (!docDir) return;
  const files = await FileSystem.readDirectoryAsync(docDir);
  for (const file of files) {
    if (file.startsWith('DOCUMENT_SCAN')) {
      const filePath = docDir + file;
      try {
        await FileSystem.deleteAsync(filePath, { idempotent: true });
        console.log('Đã xóa:', filePath);
      } catch (err) {
        console.error('Lỗi xóa:', err);
      }
    }
  }
};
