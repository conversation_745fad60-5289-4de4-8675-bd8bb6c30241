import { useQuery } from '@tanstack/react-query';
import { AiModel, AiModelListParams, AiModelResponse } from '../types';
import { api } from '@/core/client/axios';


export const useAiModels = (params?: AiModelListParams) => {
    return useQuery<AiModel[]>({
        queryKey: ['ai-models-list'],
        queryFn: async () => {
            const { data } = await api.get<AiModelResponse>('/Models/get_model_NTSoftDocumentAI', {
                params,
            });
            const models = JSON.parse(data.DanhSachModel) as AiModel[];
            return models;
        },
        staleTime: 5 * 60 * 1000,
        gcTime: 30 * 60 * 1000,
        retry: 3,
        refetchOnWindowFocus: false,
    });
}; 