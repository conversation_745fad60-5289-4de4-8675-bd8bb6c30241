import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  Dimensions,
  Platform,
  FlatList,
  Animated,
  Easing,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

import { Ionicons } from '@expo/vector-icons';
import {
  Button,
  View,
  Text,
  Spacings,
  BorderRadiuses,
  TouchableOpacity,
} from 'react-native-ui-lib';
import Svg, {
  Circle,
  Path,
  Defs,
  LinearGradient as SvgLinearGradient,
  Stop,
} from 'react-native-svg';
import LottieView from 'lottie-react-native';
import { ROUTES } from '@/core/constants/routes';

const { width, height } = Dimensions.get('window');
const AnimatedCircle = Animated.createAnimatedComponent(Circle);

// Get Lottie source for each slide
const getLottieSource = (index: number) => {
  switch (index) {
    case 0:
      return require('@assets/lottie/ai_animation_1.json');
    case 1:
      return require('@assets/lottie/ai_animation_1.json');
    case 2:
      return require('@assets/lottie/ai_animation_1.json');
    default:
      return require('@assets/lottie/ai_animation_1.json');
  }
};

const slides = [
  {
    id: '1',
    title: 'Quét tài liệu thông minh',
    description:
      'Chụp và quét mọi loại tài liệu với độ chính xác cao. AI sẽ tự động nhận diện và tối ưu hóa chất lượng hình ảnh.',
    buttonText: 'Tiếp tục',
  },
  {
    id: '2',
    title: 'Trích xuất văn bản AI',
    description:
      'Công nghệ AI tiên tiến giúp trích xuất văn bản từ hình ảnh một cách chính xác và nhanh chóng.',
    buttonText: 'Tiếp tục',
  },
  {
    id: '3',
    title: 'Quản lý tài liệu dễ dàng',
    description:
      'Lưu trữ, tìm kiếm và chia sẻ tài liệu một cách thuận tiện. Bắt đầu trải nghiệm ngay hôm nay!',
    buttonText: 'Bắt đầu',
  },
];

export default function OnboardingScreen() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const router = useRouter();
  const flatListRef = useRef<FlatList>(null);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  const buttonScaleAnim = useRef(new Animated.Value(1)).current;
  const lottieScaleAnim = useRef(new Animated.Value(0.5)).current;

  // Animate content when slide changes
  useEffect(() => {
    // Reset animations
    fadeAnim.setValue(0);
    slideAnim.setValue(50);
    scaleAnim.setValue(0.8);
    lottieScaleAnim.setValue(0.5);

    // Start animations with staggered timing
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 700,
        useNativeDriver: true,
      }),
      Animated.spring(lottieScaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Update progress animation
    Animated.timing(progressAnim, {
      toValue: currentIndex + 1,
      duration: 400,
      useNativeDriver: false,
    }).start();
  }, [currentIndex]);

  const animateButton = () => {
    Animated.sequence([
      Animated.timing(buttonScaleAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.spring(buttonScaleAnim, {
        toValue: 1,
        tension: 300,
        friction: 10,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleNext = () => {
    // Haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // Button animation
    animateButton();

    if (currentIndex < slides.length - 1) {
      setCurrentIndex(currentIndex + 1);
      flatListRef.current?.scrollToIndex({
        index: currentIndex + 1,
        animated: true,
      });
    } else {
      router.replace(ROUTES.AUTH.SIGN_IN);
    }
  };

  const handleSkip = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.replace(ROUTES.AUTH.SIGN_IN);
  };

  const renderItem = ({
    item,
    index,
  }: {
    item: (typeof slides)[0];
    index: number;
  }) => {
    return (
      <View style={styles.slide}>
        {/* White content area */}

        {/* Blue gradient bottom section */}
        <LinearGradient
          colors={['#1E3A8A', '#1E40AF', '#2563EB']}
          style={styles.blueSection}
        >
          {/* Wave Animation */}
          {/* <WaveAnimation style={styles.waveOverlay} /> */}

          <Animated.View
            style={[
              styles.contentContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }, { scale: scaleAnim }],
              },
            ]}
          >
            <Animated.View
              style={{
                height: '60%',
                display: 'flex',
                justifyContent: 'space-between',
                transform: [
                  { scale: lottieScaleAnim },
                  {
                    rotate: fadeAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['10deg', '0deg'],
                    }),
                  },
                ],
                opacity: fadeAnim,
              }}
            >
              <LottieView
                cacheComposition
                autoPlay
                loop
                resizeMode='contain'
                style={{
                  width: 300,
                  height: 300,
                  backgroundColor: 'transparent',
                }}
                source={getLottieSource(currentIndex)}
              />
            </Animated.View>

            <Animated.Text
              style={[
                styles.title,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }],
                },
              ]}
            >
              {item.title}
            </Animated.Text>

            <Animated.Text
              style={[
                styles.description,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }],
                },
              ]}
            >
              {item.description}
            </Animated.Text>

            {/* Pagination dots with animation */}
            <View style={styles.paginationDots}>
              {slides.map((_, dotIndex) => {
                const isActive = dotIndex === currentIndex;
                return (
                  <Animated.View
                    key={dotIndex}
                    style={[
                      styles.dot,
                      {
                        backgroundColor: isActive
                          ? '#ffffff'
                          : 'rgba(255,255,255,0.4)',
                        transform: [
                          {
                            scale: isActive ? 1.2 : 1,
                          },
                        ],
                      },
                    ]}
                  />
                );
              })}
            </View>
          </Animated.View>
        </LinearGradient>
      </View>
    );
  };

  const CircularProgressButton = () => {
    const circumference = 2 * Math.PI * 32; // radius = 32

    const animatedStrokeDashoffset = progressAnim.interpolate({
      inputRange: [0, slides.length],
      outputRange: [circumference, 0],
      extrapolate: 'clamp',
    });

    return (
      <View style={styles.navigationContainer}>
        <View style={styles.progressButtonWrapper}>
          {/* SVG Progress Ring */}
          <Svg width='80' height='80' style={styles.progressRing}>
            {/* Background circle */}
            <Circle
              cx='40'
              cy='40'
              r='32'
              stroke='rgba(255,255,255,0.3)'
              strokeWidth='3'
              fill='none'
            />
            {/* Progress circle */}
            <AnimatedCircle
              cx='40'
              cy='40'
              r='32'
              stroke='#ffffff'
              strokeWidth='3'
              fill='none'
              strokeDasharray={circumference}
              strokeDashoffset={animatedStrokeDashoffset}
              strokeLinecap='round'
              transform='rotate(-90 40 40)'
            />
          </Svg>

          {/* Center Button */}
          <Animated.View style={{ transform: [{ scale: buttonScaleAnim }] }}>
            <TouchableOpacity
              style={[
                styles.centerButton,
                currentIndex === slides.length - 1 && styles.centerButtonFinal,
              ]}
              onPress={handleNext}
            >
              <Ionicons
                name='arrow-forward'
                size={26}
                color={
                  currentIndex === slides.length - 1 ? '#ffffff' : '#1E3A8A'
                }
              />
            </TouchableOpacity>
          </Animated.View>
        </View>
      </View>
    );
  };

  return (
    <View>
      <View style={styles.header}>
        <Button link onPress={handleSkip} style={styles.skipButton}>
          <Text style={styles.skipText}>Bỏ qua</Text>
        </Button>
      </View>
      <FlatList
        ref={flatListRef}
        data={slides}
        renderItem={renderItem}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        scrollEventThrottle={16}
        onScroll={(event: any) => {
          const newIndex = Math.round(
            event.nativeEvent.contentOffset.x / width,
          );
          if (newIndex !== currentIndex) {
            setCurrentIndex(newIndex);
          }
        }}
        onScrollEndDrag={(event: any) => {
          const position = event.nativeEvent.contentOffset.x;
          const index = Math.round(position / width);
          setCurrentIndex(index);
        }}
        keyExtractor={(item: any) => item.id}
        decelerationRate='fast'
        snapToInterval={width}
        snapToAlignment='center'
      />

      <CircularProgressButton />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    paddingHorizontal: Spacings.s5,
    paddingVertical: Spacings.s4,
    position: 'absolute',
    top: Platform.OS === 'ios' ? 44 : 0,
    right: 0,
    zIndex: 10,
    marginTop: Spacings.s3,
  },
  skipButton: {
    // paddingHorizontal: Spacings.s3,
    // paddingVertical: Spacings.s2,
    // borderRadius: BorderRadiuses.br40,
    // backgroundColor: 'rgba(255,255,255,0.9)',
  },
  skipText: {
    fontSize: 14,
    // color: '#666666',
    color: 'white',
    fontWeight: 'bold',
  },
  slide: {
    width,
    height,
    flex: 1,
  },
  whiteContentArea: {
    flex: 0.3,
    backgroundColor: '#ffffff',
    paddingTop: 60,
  },
  blueSection: {
    flex: 1,
    paddingTop: 40,
    paddingHorizontal: 30,
    position: 'relative',
    overflow: 'hidden',
  },
  waveContainer: {
    position: 'absolute',
    top: -50,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  waveSvg: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  waveOverlay: {
    position: 'absolute',
    top: -100,
    left: 0,
    right: 0,
    zIndex: 0,
  },
  imageContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  contentContainer: {
    height: '100%',
    alignItems: 'center',
    justifyContent: 'flex-end',
    zIndex: 2,
    position: 'relative',
    paddingBottom: '40%',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 16,
    textAlign: 'center',
    lineHeight: 34,
  },
  description: {
    fontSize: 16,
    fontWeight: '400',
    color: 'rgba(255,255,255,0.9)',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  paginationDots: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
    shadowColor: '#ffffff',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 5,
  },
  navigationContainer: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 50 : 30,
    left: 0,
    right: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressButtonWrapper: {
    width: 80,
    height: 80,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  progressRing: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  centerButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255,255,255,0.95)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  centerButtonFinal: {
    backgroundColor: '#4F6CFF', // Blue color for final step
    shadowColor: '#4F6CFF',
    shadowOpacity: 0.4,
  },
});
