import React from 'react';
import {
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Platform,
} from 'react-native';
import { View } from 'react-native-ui-lib';
import { Ionicons } from '@expo/vector-icons';

interface OptimizedSearchInputProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  placeholderTextColor?: string;
  style?: object;
  containerStyle?: object;
  autoFocus?: boolean;
  onSubmitEditing?: () => void;
  onClear?: () => void;
  showClearButton?: boolean;
  iconColor?: string;
  iconSize?: number;
}

export const OptimizedSearchInput: React.FC<OptimizedSearchInputProps> = ({
  value,
  onChangeText,
  placeholder = 'Tìm kiếm...',
  placeholderTextColor = '#999',
  style,
  containerStyle,
  autoFocus = false,
  onSubmitEditing,
  onClear,
  showClearButton = true,
  iconColor = '#999',
  iconSize = 20,
}) => {
  const handleClear = () => {
    onChangeText('');
    onClear?.();
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <Ionicons
        name="search"
        size={iconSize}
        color={iconColor}
        style={styles.searchIcon}
      />
      <TextInput
        style={[styles.input, style]}
        placeholder={placeholder}
        placeholderTextColor={placeholderTextColor}
        value={value}
        onChangeText={onChangeText}
        autoFocus={autoFocus}
        onSubmitEditing={onSubmitEditing}
        // Keyboard optimizations
        returnKeyType="search"
        autoCapitalize="none"
        autoCorrect={false}
        clearButtonMode={Platform.OS === 'ios' ? 'while-editing' : 'never'}
        selectTextOnFocus={true}
        blurOnSubmit={false}
      />
      {showClearButton && value.length > 0 && Platform.OS === 'android' && (
        <TouchableOpacity onPress={handleClear} style={styles.clearButton}>
          <Ionicons name="close-circle" size={iconSize} color={iconColor} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    paddingHorizontal: 12,
    paddingVertical: Platform.OS === 'ios' ? 12 : 8,
    marginHorizontal: 16,
    marginVertical: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    paddingVertical: 0, // Remove default padding
  },
  clearButton: {
    marginLeft: 8,
    padding: 4,
  },
});
