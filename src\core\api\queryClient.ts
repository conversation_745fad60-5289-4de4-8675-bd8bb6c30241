// src/api/queryClient.ts
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1, // Số lần retry khi lỗi
      staleTime: 1000 * 60, // 1 phút không refetch lại
      refetchOnWindowFocus: false, // không tự refetch khi focus lại
    },
    mutations: {
      retry: 0, // mutation thường không nên retry
    },
  },
});
