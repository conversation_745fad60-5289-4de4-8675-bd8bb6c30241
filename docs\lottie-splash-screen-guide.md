# Lottie Splash Screen Implementation Guide

## Tổng quan

Hướng dẫn tích hợp <PERSON><PERSON> animations vào splash screen experience sử dụng hybrid approach với Expo SplashScreen.

## ❌ Lý do không thể dùng Lottie trực tiếp trong Native Splash

### Expo SplashScreen Limitations:
- **Chỉ hỗ trợ static images** (PNG, JPG)
- **Không hỗ trợ animations** (Lottie, GIF, video)
- **Native splash screen** hiển thị trước khi JavaScript load
- **Performance focused** - cần hiển thị ngay lập tức

### Platform Constraints:
- **iOS:** Launch Screen chỉ hỗ trợ static assets
- **Android:** Splash Theme chỉ hỗ trợ drawable resources
- **React Native bridge** chưa khởi tạo khi native splash hiển thị

## ✅ Giải pháp: Hybrid Approach

### 1. **Native Splash Screen (Instant)**
```typescript
// app.config.ts
splash: {
  image: './src/assets/images/splash.png',
  resizeMode: 'contain',
  backgroundColor: '#1E3A8A',
}
```

### 2. **Lottie Splash Screen (After JS Load)**
```typescript
// LottieSplashScreen.tsx
<LottieView
  source={require('@assets/lottie/welcome_1.json')}
  autoPlay
  loop={false}
  onAnimationFinish={handleAnimationFinish}
/>
```

## 🎯 Implementation Flow

### Splash Sequence:
```
1. User taps app icon
2. Native splash screen (instant) - Static image
3. JavaScript bundle loads
4. Fonts load
5. Native splash hides
6. Lottie splash screen shows - Animated
7. Lottie animation finishes
8. App content appears
```

### Timeline:
```
0ms    - Native splash appears
500ms  - JS bundle loaded
800ms  - Fonts loaded, native splash hides
800ms  - Lottie splash appears
3000ms - Lottie animation finishes
3000ms - App content shows
```

## 📁 File Structure

```
src/
├── features/splash/components/
│   ├── LottieSplashScreen.tsx    # New Lottie splash
│   └── SplashScreen.tsx          # Old custom splash (can remove)
├── assets/lottie/
│   ├── welcome_1.json            # Main splash animation
│   ├── scan_2.json              # Loading animation
│   └── document_2.json          # Alternative animations
└── app/_layout.tsx               # Updated with hybrid approach
```

## 🎨 LottieSplashScreen Features

### 1. **Gradient Background**
```typescript
<LinearGradient
  colors={['#1E3A8A', '#3B82F6', '#60A5FA']}
  style={styles.container}
/>
```

### 2. **Main Animation**
```typescript
<LottieView
  source={require('@assets/lottie/welcome_1.json')}
  autoPlay
  loop={false}
  onAnimationFinish={handleAnimationFinish}
/>
```

### 3. **Loading Indicator**
```typescript
<LottieView
  source={require('@assets/lottie/scan_2.json')}
  autoPlay
  loop
  style={styles.loadingAnimation}
/>
```

### 4. **Text Content**
- App title với text shadow
- Subtitle với opacity
- Loading text

## 🔧 Configuration Options

### Animation Settings:
```typescript
interface LottieSplashScreenProps {
  onAnimationFinish?: () => void;
  animationSource?: any;
  showLoadingIndicator?: boolean;
  backgroundColor?: string[];
  duration?: number;
}
```

### Available Lottie Files:
- `welcome_1.json` - Main welcome animation
- `welcome_2.json` - Alternative welcome
- `scan_2.json` - Scanning animation
- `document_2.json` - Document animation
- `qr-code.json` - QR code animation

## 🎭 Animation Customization

### 1. **Change Main Animation:**
```typescript
// In LottieSplashScreen.tsx
<LottieView
  source={require('@assets/lottie/document_2.json')} // Change this
  autoPlay
  loop={false}
/>
```

### 2. **Adjust Animation Duration:**
```typescript
const handleAnimationFinish = () => {
  setTimeout(onAnimationFinish, 500); // Adjust delay
};
```

### 3. **Custom Animation Sequence:**
```typescript
// Multiple animations in sequence
const [currentAnimation, setCurrentAnimation] = useState(0);

const animations = [
  require('@assets/lottie/welcome_1.json'),
  require('@assets/lottie/scan_2.json'),
  require('@assets/lottie/document_2.json'),
];
```

## 🚀 Performance Optimization

### 1. **Preload Animations:**
```typescript
// Preload Lottie files
useEffect(() => {
  // Cache animations
  const animations = [
    require('@assets/lottie/welcome_1.json'),
    require('@assets/lottie/scan_2.json'),
  ];
}, []);
```

### 2. **Optimize Animation Files:**
- Keep file size < 100KB
- Use simple animations
- Avoid complex gradients
- Optimize frame rate (24-30fps)

### 3. **Memory Management:**
```typescript
<LottieView
  cacheComposition={true}  // Cache for performance
  hardwareAccelerationAndroid={true}
  renderMode="HARDWARE"
/>
```

## 🎨 Design Guidelines

### 1. **Animation Duration:**
- **Main animation:** 2-3 seconds
- **Loading animation:** Loop continuously
- **Total splash time:** 3-4 seconds max

### 2. **Visual Consistency:**
- Match app brand colors
- Use consistent animation style
- Ensure readability on all devices

### 3. **Responsive Design:**
```typescript
const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  animation: {
    width: width * 0.8,
    height: width * 0.8,
    maxWidth: 400,
    maxHeight: 400,
  },
});
```

## 🔄 Alternative Approaches

### 1. **Pure Lottie Splash (No Native):**
```typescript
// Remove native splash completely
// app.config.ts - comment out splash config
// Use only Lottie from start
```

### 2. **Static + Lottie Overlay:**
```typescript
// Keep native splash visible
// Overlay Lottie animation on top
// More complex but smoother transition
```

### 3. **Progressive Loading:**
```typescript
// Show different animations based on loading state
// Fonts loading -> Animation A
// App ready -> Animation B
```

## 🧪 Testing

### 1. **Development:**
```bash
expo start
# Test Lottie animations in development
```

### 2. **Production Build:**
```bash
eas build --profile preview --platform ios
# Test complete splash sequence on device
```

### 3. **Performance Testing:**
- Monitor animation frame rate
- Check memory usage
- Test on low-end devices
- Verify smooth transitions

## 🐛 Troubleshooting

### 1. **Animation Not Playing:**
- Check Lottie file path
- Verify JSON format
- Ensure autoPlay is true

### 2. **Performance Issues:**
- Reduce animation complexity
- Use hardware acceleration
- Cache compositions

### 3. **Transition Problems:**
- Adjust timing delays
- Check state management
- Verify callback functions

## 📱 Platform Differences

### iOS:
- Smoother animations
- Better hardware acceleration
- Faster JSON parsing

### Android:
- May need performance optimization
- Test on various devices
- Consider renderMode settings

## 🎯 Best Practices

1. **Keep animations simple** and brand-focused
2. **Optimize file sizes** for faster loading
3. **Test on real devices** for performance
4. **Provide fallbacks** for animation failures
5. **Monitor loading times** and adjust accordingly
6. **Use consistent timing** across the app
7. **Consider accessibility** and motion preferences
