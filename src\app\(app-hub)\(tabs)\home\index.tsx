import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Image,
  Dimensions,
  ImageBackground,
  FlatList,
} from 'react-native';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Ionicons, MaterialIcons, FontAwesome5 } from '@expo/vector-icons';
import { Colors } from 'react-native-ui-lib';
import { LinearGradient } from 'expo-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';

interface AppItem {
  id: string;
  name: string;
  description: string;
  icon: any;
  color: string;
  route: string;
  usage?: string;
  badge?: string;
}

// Danh sách các ứng dụng có thể truy cập
const APP_LIST: AppItem[] = [
  {
    id: 'scan-doc',
    name: 'Quét tài liệu',
    description: 'Quét và lưu trữ tài liệu, hóa đơn',
    icon: 'document-text-outline',
    color: '#4A6FFF',
    route: '/qlda',
    usage: '27 lần sử dụng',
  },
  {
    id: 'tdkt',
    name: '<PERSON><PERSON> đua khen thưởng',
    description: 'Quản lý thi đua khen thưởng',
    icon: 'trophy-outline',
    color: '#FF6B6B',
    route: '/qlda/tdkt',
    usage: '12 lần sử dụng',
  },
  {
    id: 'media',
    name: 'Thư viện Media',
    description: 'Lưu trữ và chia sẻ hình ảnh, video',
    icon: 'images-outline',
    color: '#47C1BF',
    route: '/feature-selection',
    usage: '8 lần sử dụng',
  },
  {
    id: 'tasks',
    name: 'Công việc',
    description: 'Quản lý nhiệm vụ và dự án',
    icon: 'checkbox-outline',
    color: '#FFA26B',
    route: '/feature-selection',
    usage: '15 lần sử dụng',
  },
];

// Danh sách thống kê
const STATS = [
  {
    id: 'documents',
    title: 'Tài liệu',
    value: '127',
    icon: 'document' as any,
    color: '#4A6FFF',
    change: '+12%',
  },
  {
    id: 'tasks',
    title: 'Nhiệm vụ',
    value: '24',
    icon: 'checkbox' as any,
    color: '#FFA26B',
    change: '+3%',
  },
  {
    id: 'storage',
    title: 'Dung lượng',
    value: '45%',
    icon: 'cloud-upload' as any,
    color: '#47C1BF',
    change: '-5%',
  },
];

// Danh sách hoạt động
const ACTIVITIES = [
  {
    id: '1',
    title: 'Quét tài liệu mới',
    description: 'Đã quét và lưu 3 tài liệu mới',
    time: '2 giờ trước',
    icon: 'document-text' as any,
    color: '#4A6FFF',
  },
  {
    id: '2',
    title: 'Cập nhật TĐKT',
    description: 'Đã thêm 2 khen thưởng mới',
    time: 'Hôm qua',
    icon: 'trophy' as any,
    color: '#FF6B6B',
  },
  {
    id: '3',
    title: 'Tải lên phương tiện',
    description: 'Đã tải lên 5 ảnh mới',
    time: '2 ngày trước',
    icon: 'images' as any,
    color: '#47C1BF',
  },
];

const { width } = Dimensions.get('window');

export default function AppHubHome() {
  const router = useRouter();
  const [greeting, setGreeting] = useState('');
  const [currentTime, setCurrentTime] = useState('');
  const now = new Date();
  const hour = now.getHours();

  useEffect(() => {
    if (hour < 12) setGreeting('Chào buổi sáng');
    else if (hour < 18) setGreeting('Chào buổi chiều');
    else setGreeting('Chào buổi tối');

    // Format current time
    const formattedTime = now.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
    });
    const formattedDate = now.toLocaleDateString('vi-VN', {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
    });
    setCurrentTime(`${formattedTime} • ${formattedDate}`);
  }, []);

  const navigateToApp = (app: AppItem) => {
    router.push(app.route);
  };

  // Popular Apps Section
  const renderPopularApps = () => (
    <View style={styles.popularAppsSection}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Ứng dụng phổ biến</Text>
        <TouchableOpacity>
          <Text style={styles.seeAllText}>Xem tất cả</Text>
        </TouchableOpacity>
      </View>
      <FlatList
        data={APP_LIST.slice(0, 2)}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 10 }}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.popularAppCard}
            onPress={() => navigateToApp(item)}
          >
            <LinearGradient
              colors={[item.color, `${item.color}80`]}
              style={styles.gradientCard}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <View style={styles.popularAppContent}>
                <View style={styles.popularAppIcon}>
                  <Ionicons name={item.icon} size={30} color='#ffffff' />
                </View>
                <Text style={styles.popularAppName}>{item.name}</Text>
                <Text style={styles.popularAppUsage}>{item.usage}</Text>
              </View>
            </LinearGradient>
          </TouchableOpacity>
        )}
        keyExtractor={item => item.id}
      />
    </View>
  );

  // Stats Section
  const renderStats = () => (
    <View style={styles.statsSection}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Thống kê</Text>
      </View>
      <View style={styles.statsGrid}>
        {STATS.map(stat => (
          <View key={stat.id} style={styles.statCard}>
            <View
              style={[
                styles.statIconContainer,
                { backgroundColor: `${stat.color}15` },
              ]}
            >
              <Ionicons name={stat.icon} size={22} color={stat.color} />
            </View>
            <Text style={styles.statValue}>{stat.value}</Text>
            <View style={styles.statInfoRow}>
              <Text style={styles.statTitle}>{stat.title}</Text>
              <Text
                style={[
                  styles.statChange,
                  { color: stat.change.includes('+') ? '#4CAF50' : '#F44336' },
                ]}
              >
                {stat.change}
              </Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );

  // All Apps Section
  const renderAllApps = () => (
    <View style={styles.allAppsSection}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Tất cả ứng dụng</Text>
      </View>
      <View style={styles.appsGrid}>
        {APP_LIST.map(app => (
          <TouchableOpacity
            key={app.id}
            style={styles.appCard}
            onPress={() => navigateToApp(app)}
          >
            <View
              style={[styles.appIconContainer, { backgroundColor: app.color }]}
            >
              <Ionicons name={app.icon} size={24} color='#fff' />
            </View>
            <View style={styles.appInfo}>
              <Text style={styles.appName}>{app.name}</Text>
              <Text style={styles.appDescription} numberOfLines={1}>
                {app.description}
              </Text>
            </View>
            <Ionicons name='chevron-forward' size={20} color='#888' />
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  // Recent Activities Section
  const renderActivities = () => (
    <View style={styles.activitiesSection}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Hoạt động gần đây</Text>
        <TouchableOpacity>
          <Text style={styles.seeAllText}>Xem tất cả</Text>
        </TouchableOpacity>
      </View>
      {ACTIVITIES.map(activity => (
        <TouchableOpacity key={activity.id} style={styles.activityItem}>
          <View
            style={[
              styles.activityIconContainer,
              { backgroundColor: activity.color },
            ]}
          >
            <Ionicons name={activity.icon} size={20} color='#fff' />
          </View>
          <View style={styles.activityContent}>
            <Text style={styles.activityTitle}>{activity.title}</Text>
            <Text style={styles.activityDescription}>
              {activity.description}
            </Text>
            <Text style={styles.activityTime}>{activity.time}</Text>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style='dark' />

      {/* Hero Header */}
      <View style={styles.heroHeader}>
        <View>
          <Text style={styles.greeting}>{greeting}</Text>
          <Text style={styles.heroTitle}>Trung tâm ứng dụng</Text>
          <Text style={styles.currentTime}>{currentTime}</Text>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.iconButton}>
            <Ionicons name='search-outline' size={22} color='#333' />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton}>
            <Ionicons name='notifications-outline' size={22} color='#333' />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {renderPopularApps()}
        {renderStats()}
        {renderAllApps()}
        {renderActivities()}
        <View style={{ height: 100 }} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  heroHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: 20,
    paddingTop: 15,
    paddingBottom: 20,
  },
  greeting: {
    fontSize: 14,
    color: '#888',
    marginBottom: 4,
  },
  heroTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  currentTime: {
    fontSize: 12,
    color: '#888',
  },
  headerActions: {
    flexDirection: 'row',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  popularAppsSection: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  seeAllText: {
    fontSize: 14,
    color: Colors.buttonPrimary,
  },
  popularAppCard: {
    width: width * 0.75,
    height: 120,
    marginHorizontal: 8,
    borderRadius: 16,
    overflow: 'hidden',
  },
  gradientCard: {
    flex: 1,
    padding: 15,
    justifyContent: 'flex-end',
  },
  popularAppContent: {
    width: '100%',
  },
  popularAppIcon: {
    width: 50,
    height: 50,
    borderRadius: 15,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  popularAppName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  popularAppUsage: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
  },
  statsSection: {
    marginBottom: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  statCard: {
    width: (width - 60) / 3,
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 2,
  },
  statIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  statInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statTitle: {
    fontSize: 12,
    color: '#888',
  },
  statChange: {
    fontSize: 11,
    fontWeight: 'bold',
  },
  allAppsSection: {
    marginBottom: 20,
  },
  appsGrid: {
    paddingHorizontal: 20,
  },
  appCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 12,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
  },
  appIconContainer: {
    width: 45,
    height: 45,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  appInfo: {
    flex: 1,
  },
  appName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  appDescription: {
    fontSize: 12,
    color: '#888',
  },
  activitiesSection: {
    paddingHorizontal: 20,
  },
  activityItem: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
  },
  activityIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  activityDescription: {
    fontSize: 12,
    color: '#666',
    marginBottom: 6,
  },
  activityTime: {
    fontSize: 11,
    color: '#999',
  },
});
