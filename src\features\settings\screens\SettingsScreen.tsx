import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors } from 'react-native-ui-lib';
import { SafeAreaView } from 'react-native-safe-area-context';

// Types
interface Project {
  id: string;
  name: string;
}

interface BusinessType {
  id: string;
  name: string;
}

// Mock data for projects and business types
const PROJECTS: Project[] = [
  { id: '1', name: 'Dự án A' },
  { id: '2', name: 'Dự án B' },
  { id: '3', name: 'Dự án C' },
];

const BUSINESS_TYPES: BusinessType[] = [
  { id: '1', name: '<PERSON><PERSON> to<PERSON>' },
  { id: '2', name: '<PERSON><PERSON><PERSON> sự' },
  { id: '3', name: '<PERSON><PERSON> doanh' },
  { id: '4', name: '<PERSON><PERSON><PERSON><PERSON> lý' },
];

const STORAGE_KEYS = {
  SELECTED_PROJECT: 'selected_project',
  SELECTED_BUSINESS: 'selected_business',
};

export default function SettingsScreen() {
  const router = useRouter();
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [selectedBusiness, setSelectedBusiness] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load saved selections
  useEffect(() => {
    loadSavedSelections();
  }, []);

  const loadSavedSelections = async () => {
    try {
      const savedProject = await AsyncStorage.getItem(
        STORAGE_KEYS.SELECTED_PROJECT,
      );
      const savedBusiness = await AsyncStorage.getItem(
        STORAGE_KEYS.SELECTED_BUSINESS,
      );

      if (savedProject) setSelectedProject(savedProject);
      if (savedBusiness) setSelectedBusiness(savedBusiness);
    } catch (error) {
      console.error('Error loading saved selections:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleProjectSelect = (projectId: string) => {
    setSelectedProject(projectId);
  };

  const handleBusinessSelect = (businessId: string) => {
    setSelectedBusiness(businessId);
  };

  const handleSave = async () => {
    try {
      if (!selectedProject || !selectedBusiness) {
        Alert.alert('Lỗi', 'Vui lòng chọn cả dự án và nghiệp vụ');
        return;
      }

      // Save selections
      await AsyncStorage.setItem(
        STORAGE_KEYS.SELECTED_PROJECT,
        selectedProject,
      );
      await AsyncStorage.setItem(
        STORAGE_KEYS.SELECTED_BUSINESS,
        selectedBusiness,
      );

      // Navigate to scan screen
      router.push('/scan');
    } catch (error) {
      console.error('Error saving selections:', error);
      Alert.alert('Lỗi', 'Không thể lưu thiết lập. Vui lòng thử lại.');
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Đang tải...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style='dark' />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Thiết lập</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Project Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Chọn Dự án</Text>
          <View style={styles.optionsContainer}>
            {PROJECTS.map(project => (
              <TouchableOpacity
                key={project.id}
                style={[
                  styles.optionButton,
                  selectedProject === project.id && styles.selectedOption,
                ]}
                onPress={() => handleProjectSelect(project.id)}
              >
                <Text
                  style={[
                    styles.optionText,
                    selectedProject === project.id && styles.selectedOptionText,
                  ]}
                >
                  {project.name}
                </Text>
                {selectedProject === project.id && (
                  <Ionicons
                    name='checkmark-circle'
                    size={20}
                    color={Colors.buttonPrimary}
                  />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Business Type Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Chọn Nghiệp vụ</Text>
          <View style={styles.optionsContainer}>
            {BUSINESS_TYPES.map(business => (
              <TouchableOpacity
                key={business.id}
                style={[
                  styles.optionButton,
                  selectedBusiness === business.id && styles.selectedOption,
                ]}
                onPress={() => handleBusinessSelect(business.id)}
              >
                <Text
                  style={[
                    styles.optionText,
                    selectedBusiness === business.id &&
                      styles.selectedOptionText,
                  ]}
                >
                  {business.name}
                </Text>
                {selectedBusiness === business.id && (
                  <Ionicons
                    name='checkmark-circle'
                    size={20}
                    color={Colors.buttonPrimary}
                  />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Save Button */}
        <TouchableOpacity
          style={[
            styles.saveButton,
            (!selectedProject || !selectedBusiness) &&
              styles.saveButtonDisabled,
          ]}
          disabled={!selectedProject || !selectedBusiness}
          onPress={handleSave}
        >
          <Text style={styles.saveButtonText}>Lưu thiết lập</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#222',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
  },
  optionsContainer: {
    gap: 10,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  selectedOption: {
    backgroundColor: '#e8f4ff',
    borderColor: Colors.buttonPrimary,
  },
  optionText: {
    fontSize: 15,
    color: '#495057',
  },
  selectedOptionText: {
    color: Colors.buttonPrimary,
    fontWeight: '500',
  },
  saveButton: {
    backgroundColor: Colors.buttonPrimary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 30,
  },
  saveButtonDisabled: {
    backgroundColor: '#ccc',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
