import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const LOGIN_CREDENTIALS_KEY = '@auth:login_credentials';

export interface StoredCredentials {
    username: string;
    password: string;
}

export interface UseStoredCredentialsReturnType {
    credentials: StoredCredentials | null;
    hasStoredCredentials: boolean;
    isLoading: boolean;
    saveCredentials: (username: string, password: string) => Promise<void>;
    removeCredentials: () => Promise<void>;
}

/**
 * Hook quản lý thông tin đăng nhập được lưu trữ
 */
export const useStoredCredentials = (): UseStoredCredentialsReturnType => {
    const [credentials, setCredentials] = useState<StoredCredentials | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true);

    // Tải thông tin đăng nhập khi component mount
    useEffect(() => {
        const loadCredentials = async () => {
            try {
                const credentialsJson = await AsyncStorage.getItem(LOGIN_CREDENTIALS_KEY);
                if (credentialsJson) {
                    const parsedCredentials = JSON.parse(credentialsJson) as StoredCredentials;
                    setCredentials(parsedCredentials);
                }
            } catch (error) {
                console.error('Error loading credentials:', error);
            } finally {
                setIsLoading(false);
            }
        };

        loadCredentials();
    }, []);

    // Lưu thông tin đăng nhập
    const saveCredentials = useCallback(async (username: string, password: string) => {
        try {
            const newCredentials: StoredCredentials = { username, password };
            await AsyncStorage.setItem(LOGIN_CREDENTIALS_KEY, JSON.stringify(newCredentials));
            setCredentials(newCredentials);
        } catch (error) {
            console.error('Error saving credentials:', error);
        }
    }, []);

    // Xóa thông tin đăng nhập
    const removeCredentials = useCallback(async () => {
        try {
            await AsyncStorage.removeItem(LOGIN_CREDENTIALS_KEY);
            setCredentials(null);
        } catch (error) {
            console.error('Error removing credentials:', error);
        }
    }, []);

    return {
        credentials,
        hasStoredCredentials: credentials !== null,
        isLoading,
        saveCredentials,
        removeCredentials,
    };
};

export default useStoredCredentials; 