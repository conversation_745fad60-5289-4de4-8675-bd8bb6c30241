import { api } from '@core/client/axios';
import { API_CONFIG } from '@core/api/config';

export interface DuAn {
    DuAnId: string;
    MaDuAn: string;
    TenDuAn: string;
    DonViId: string;
}

export interface DuAnResponse {
    Items: DuAn[];
    PageNumber: number;
    TotalPages: number;
    TotalCount: number;
    HasPreviousPage: boolean;
    HasNextPage: boolean;
}

/**
 * Service class for Dự án (Project) related API operations
 */
class DuAnService {
    private static instance: DuAnService;

    private constructor() {
        // Private constructor to enforce singleton pattern
    }

    /**
     * Get singleton instance of DuAnService
     */
    public static getInstance(): DuAnService {
        if (!DuAnService.instance) {
            DuAnService.instance = new DuAnService();
        }
        return DuAnService.instance;
    }

    /**
     * Get all dự án with optional filters
     * @param donViId Optional ID of the department to filter by
     */
    async getAllDuAn(donViId?: string): Promise<DuAnResponse> {
        try {
            const params: Record<string, string> = {};
            if (donViId) {
                params.DonViId = donViId;
            }

            const response = await api.get<DuAnResponse>(
                API_CONFIG.ENDPOINTS.DUAN.GET_ALL_DUAN,
                { params }
            );

            return response.data;
        } catch (error) {
            console.error('Error fetching dự án:', error);
            throw error;
        }
    }
}

export default DuAnService; 