import React, { useEffect, useState } from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  StyleSheet,
  Animated,
} from 'react-native';
import { View } from 'react-native-ui-lib';
import { useKeyboardDismiss } from '@/shared/hooks/useKeyboardDismiss';
import { useKeyboardVisible } from '@/shared/hooks/useKeyboardVisible';

interface SmartKeyboardViewProps {
  children: React.ReactNode;
  style?: object;
  behavior?: 'height' | 'position' | 'padding';
  keyboardVerticalOffset?: number;
  enableTouchToDismiss?: boolean;
  animationDuration?: number;
}

export const SmartKeyboardView: React.FC<SmartKeyboardViewProps> = ({
  children,
  style,
  behavior = Platform.OS === 'ios' ? 'padding' : 'height',
  keyboardVerticalOffset = Platform.OS === 'ios' ? 0 : 20,
  enableTouchToDismiss = true,
  animationDuration = 250,
}) => {
  const { dismissKeyboard } = useKeyboardDismiss();
  const isKeyboardVisible = useKeyboardVisible();
  const [animatedValue] = useState(new Animated.Value(0));

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: isKeyboardVisible ? 1 : 0,
      duration: animationDuration,
      useNativeDriver: false,
    }).start();
  }, [isKeyboardVisible, animationDuration, animatedValue]);

  const animatedStyle = {
    transform: [
      {
        translateY: animatedValue.interpolate({
          inputRange: [0, 1],
          outputRange: [0, -20], // Nhẹ nhàng đẩy content lên một chút
        }),
      },
    ],
  };

  const content = (
    <Animated.View style={[styles.container, animatedStyle]}>
      {children}
    </Animated.View>
  );

  if (enableTouchToDismiss) {
    return (
      <KeyboardAvoidingView
        style={[styles.keyboardAvoidingView, style]}
        behavior={behavior}
        keyboardVerticalOffset={keyboardVerticalOffset}
      >
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View style={styles.touchableContainer}>
            {content}
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    );
  }

  return (
    <KeyboardAvoidingView
      style={[styles.keyboardAvoidingView, style]}
      behavior={behavior}
      keyboardVerticalOffset={keyboardVerticalOffset}
    >
      {content}
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  touchableContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
});
