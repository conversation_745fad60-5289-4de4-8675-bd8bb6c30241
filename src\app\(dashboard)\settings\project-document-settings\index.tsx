import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { MMKV } from 'react-native-mmkv';
import { useDuAnQuery } from '@features/duan/hooks/useDuAnQuery';
import { useDocumentTypes } from '@features/document/hooks/useDocumentTypes';
import {
  updateProjectId,
  updateDocumentType,
  getCurrentProjectId,
  getCurrentDocumentType,
} from '@core/api/apiConfigManager';
import { useProfile } from '@features/auth/hooks/useAuth';
import Toast from 'react-native-toast-message';
import { AnimatedView } from '@/shared/animations/components';

// Types
type IconName =
  | 'arrow-back'
  | 'save'
  | 'business'
  | 'document-text'
  | 'search'
  | 'close-circle';

const ProjectDocumentSettingsScreen = () => {
  const router = useRouter();
  const showToast = (message: string, type: 'success' | 'error') => {
    Toast.show({
      type: type,
      text1: message,
    });
  };

  // States
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(
    null,
  );
  const [selectedDocTypeId, setSelectedDocTypeId] = useState<string | null>(
    null,
  );
  const [projectSearchQuery, setProjectSearchQuery] = useState('');
  const [docTypeSearchQuery, setDocTypeSearchQuery] = useState('');
  const [hasChanges, setHasChanges] = useState(false);

  // Lấy thông tin người dùng đăng nhập
  const { data: userProfile, isLoading: isLoadingProfile } = useProfile();

  // Query params - lấy donViId từ profile người dùng
  const donViId = userProfile?.DonViID;

  // Fetch projects
  const {
    data: projectsData,
    isLoading: isLoadingProjects,
    error: projectsError,
  } = useDuAnQuery({
    donViId,
    enabled: !!donViId,
  });

  // Fetch document types
  const {
    data: docTypesData,
    isLoading: isLoadingDocTypes,
    error: docTypesError,
  } = useDocumentTypes({
    SearchText: docTypeSearchQuery,
    PageSize: 100,
  });

  // Get saved IDs from storage
  useEffect(() => {
    // Get values directly from the API config to ensure sync with actual values
    const currentProjectId = getCurrentProjectId();
    const currentDocTypeId = getCurrentDocumentType();

    if (currentProjectId) {
      setSelectedProjectId(currentProjectId);
    }

    if (currentDocTypeId) {
      setSelectedDocTypeId(currentDocTypeId);
    }
  }, []);

  // Check for changes
  useEffect(() => {
    const currentProjectId = getCurrentProjectId();
    const currentDocTypeId = getCurrentDocumentType();

    const projectChanged = selectedProjectId !== currentProjectId;
    const docTypeChanged = selectedDocTypeId !== currentDocTypeId;

    setHasChanges(projectChanged || docTypeChanged);
  }, [selectedProjectId, selectedDocTypeId]);

  // Filter projects
  const filteredProjects = React.useMemo(() => {
    const projects = projectsData?.Items || [];
    if (!projectSearchQuery) return projects;

    return projects.filter(
      project =>
        project.TenDuAn?.toLowerCase().includes(
          projectSearchQuery.toLowerCase(),
        ) ||
        (project.MaDuAn &&
          project.MaDuAn.toLowerCase().includes(
            projectSearchQuery.toLowerCase(),
          )),
    );
  }, [projectsData, projectSearchQuery]);

  // Filter document types
  const filteredDocTypes = React.useMemo(() => {
    const docTypes = docTypesData?.Items || [];
    if (!docTypeSearchQuery) return docTypes;

    return docTypes.filter(
      docType =>
        docType.NghiepVu?.toLowerCase().includes(
          docTypeSearchQuery.toLowerCase(),
        ) ||
        (docType.NghiepVuID &&
          docType.NghiepVuID.toLowerCase().includes(
            docTypeSearchQuery.toLowerCase(),
          )),
    );
  }, [docTypesData, docTypeSearchQuery]);

  // Save changes
  const saveChanges = useCallback(() => {
    if (selectedProjectId) {
      // Update both storage and API config
      updateProjectId(selectedProjectId);
    }

    if (selectedDocTypeId) {
      // Update both storage and API config
      updateDocumentType(selectedDocTypeId);
    }

    setHasChanges(false);
    showToast('Đã lưu cài đặt', 'success');
  }, [selectedProjectId, selectedDocTypeId, showToast]);

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name={'arrow-back' as IconName} size={24} color='#333' />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Cài đặt dự án & văn bản</Text>
        <TouchableOpacity
          style={[styles.saveButton, !hasChanges && styles.saveButtonDisabled]}
          onPress={saveChanges}
          disabled={!hasChanges}
        >
          <Text
            style={[
              styles.saveButtonText,
              !hasChanges && styles.saveButtonTextDisabled,
            ]}
          >
            Lưu
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <AnimatedView
            style={styles.infoNote}
            duration={600}
            delay={300}
            slideValue={20}
          >
            <Ionicons name='information-circle' size={22} color='#4285F4' />
            <Text style={styles.infoNoteText}>
              Chọn dự án và loại tài liệu mặc định để sử dụng khi quét và xử lý
              tài liệu.
            </Text>
          </AnimatedView>

          {/* Projects section */}
          <AnimatedView
            style={styles.section}
            duration={600}
            delay={400}
            slideValue={20}
          >
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionHeaderTitle}>
                <Ionicons
                  name={'business' as IconName}
                  size={18}
                  color='#4285F4'
                />{' '}
                Dự án
              </Text>
              <Text style={styles.sectionHeaderCount}>
                {filteredProjects.length} dự án
              </Text>
            </View>

            <View style={styles.searchContainer}>
              <Ionicons name={'search' as IconName} size={18} color='#999' />
              <TextInput
                style={styles.searchInput}
                placeholder='Tìm kiếm dự án...'
                value={projectSearchQuery}
                onChangeText={setProjectSearchQuery}
              />
              {projectSearchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setProjectSearchQuery('')}>
                  <Ionicons
                    name={'close-circle' as IconName}
                    size={18}
                    color='#999'
                  />
                </TouchableOpacity>
              )}
            </View>

            {isLoadingProjects ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size='small' color='#4285F4' />
                <Text style={styles.loadingText}>
                  Đang tải danh sách dự án...
                </Text>
              </View>
            ) : projectsError ? (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>
                  Không thể tải danh sách dự án
                </Text>
              </View>
            ) : filteredProjects.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>Không tìm thấy dự án nào</Text>
              </View>
            ) : (
              <View style={styles.listContainer}>
                {filteredProjects.map((project, index) => (
                  <TouchableOpacity
                    key={project.DuAnId}
                    style={[
                      styles.itemCard,
                      selectedProjectId === project.DuAnId &&
                        styles.selectedItemCard,
                    ]}
                    onPress={() => setSelectedProjectId(project.DuAnId)}
                  >
                    <View style={styles.itemContent}>
                      <Text style={styles.itemTitle} numberOfLines={2}>
                        {project.TenDuAn}
                      </Text>
                      {project.MaDuAn && (
                        <Text style={styles.itemCode}>
                          Mã: {project.MaDuAn}
                        </Text>
                      )}
                    </View>
                    {selectedProjectId === project.DuAnId && (
                      <View style={styles.checkIconContainer}>
                        <Ionicons
                          name='checkmark-circle'
                          size={22}
                          color='#4285F4'
                        />
                      </View>
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </AnimatedView>

          {/* Document Types section */}
          <AnimatedView
            style={styles.section}
            duration={600}
            delay={500}
            slideValue={20}
          >
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionHeaderTitle}>
                <Ionicons
                  name={'document-text' as IconName}
                  size={18}
                  color='#34A853'
                />{' '}
                loại tài liệu
              </Text>
              <Text style={styles.sectionHeaderCount}>
                {filteredDocTypes.length} loại tài liệu
              </Text>
            </View>

            <View style={styles.searchContainer}>
              <Ionicons name={'search' as IconName} size={18} color='#999' />
              <TextInput
                style={styles.searchInput}
                placeholder='Tìm kiếm loại tài liệu...'
                value={docTypeSearchQuery}
                onChangeText={setDocTypeSearchQuery}
              />
              {docTypeSearchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setDocTypeSearchQuery('')}>
                  <Ionicons
                    name={'close-circle' as IconName}
                    size={18}
                    color='#999'
                  />
                </TouchableOpacity>
              )}
            </View>

            {isLoadingDocTypes ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size='small' color='#34A853' />
                <Text style={styles.loadingText}>
                  Đang tải danh sách loại tài liệu...
                </Text>
              </View>
            ) : docTypesError ? (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>
                  Không thể tải danh sách loại tài liệu
                </Text>
              </View>
            ) : filteredDocTypes.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>
                  Không tìm thấy loại tài liệu nào
                </Text>
              </View>
            ) : (
              <View style={styles.listContainer}>
                {filteredDocTypes.map((docType, index) => (
                  <TouchableOpacity
                    key={docType.NghiepVuID}
                    style={[
                      styles.itemCard,
                      selectedDocTypeId === docType.NghiepVuID &&
                        styles.selectedItemCard,
                      { borderLeftColor: '#34A853' },
                    ]}
                    onPress={() => setSelectedDocTypeId(docType.NghiepVuID)}
                  >
                    <View style={styles.itemContent}>
                      <Text style={styles.itemTitle} numberOfLines={2}>
                        {docType.NghiepVu}
                      </Text>
                      {docType.NghiepVuID && (
                        <Text style={styles.itemCode}>
                          Mã: {docType.NghiepVuID}
                        </Text>
                      )}
                    </View>
                    {selectedDocTypeId === docType.NghiepVuID && (
                      <View style={styles.checkIconContainer}>
                        <Ionicons
                          name='checkmark-circle'
                          size={22}
                          color='#34A853'
                        />
                      </View>
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </AnimatedView>

          {/* Save button at bottom */}
          {hasChanges && (
            <TouchableOpacity
              style={styles.saveAllButton}
              onPress={saveChanges}
            >
              <Ionicons
                name={'save' as IconName}
                size={20}
                color='#fff'
                style={styles.saveAllButtonIcon}
              />
              <Text style={styles.saveAllButtonText}>Lưu thay đổi</Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  backButton: {
    padding: 4,
  },
  saveButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: '#8E44AD',
    borderRadius: 16,
  },
  saveButtonDisabled: {
    backgroundColor: '#f0f0f0',
  },
  saveButtonText: {
    color: '#fff',
    fontWeight: '500',
    fontSize: 14,
  },
  saveButtonTextDisabled: {
    color: '#999',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  infoNote: {
    flexDirection: 'row',
    backgroundColor: '#e8f1ff',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
  },
  infoNoteText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 10,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    overflow: 'hidden',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sectionHeaderTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  sectionHeaderCount: {
    fontSize: 12,
    color: '#666',
    backgroundColor: '#f5f5f5',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    margin: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eee',
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#333',
    height: 36,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 8,
    color: '#666',
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
  },
  errorText: {
    color: '#f44336',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    color: '#666',
  },
  listContainer: {
    padding: 12,
  },
  itemCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 10,
    borderLeftWidth: 5,
    borderLeftColor: '#4285F4',
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  selectedItemCard: {
    backgroundColor: '#f3f8ff',
    borderLeftWidth: 5,
  },
  itemContent: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  itemCode: {
    fontSize: 12,
    color: '#666',
  },
  checkIconContainer: {
    justifyContent: 'center',
    paddingLeft: 8,
  },
  saveAllButton: {
    backgroundColor: '#8E44AD',
    borderRadius: 10,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 16,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  saveAllButtonIcon: {
    marginRight: 8,
  },
  saveAllButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ProjectDocumentSettingsScreen;
