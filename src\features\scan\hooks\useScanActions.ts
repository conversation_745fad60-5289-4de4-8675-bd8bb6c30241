import { useState, useCallback } from 'react';
import * as MediaLibrary from 'expo-media-library';
import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';
import Share from 'react-native-share';
import DocumentScanner, {
  ScanDocumentResponse,
} from 'react-native-document-scanner-plugin';
import { useScan } from '@features/scan/context/ScanContext';
import { StorageManager } from '@features/scan/services/memoryManager';
import ScanService from '@features/scan/services/scanService';
import Toast from 'react-native-toast-message';

export const useScanActions = () => {
  const {
    currentSession,
    addScanSession,
    updateCurrentSessionImages,
    removeImagesFromCurrentSession,
  } = useScan();
  const [selected, setSelected] = useState<Set<number>>(new Set());
  const [isProcessing, setIsProcessing] = useState(false);
  const showToast = (message: string, type: 'success' | 'error') => {
    Toast.show({
      type: type,
      text1: message,
    });
  };
  const storageManager = StorageManager.getInstance();
  const scanService = ScanService.getInstance();

  /**
   * Xử lý chọn ảnh từ thư viện
   */
  const pickImagesFromLibrary = useCallback(async () => {
    try {
      setIsProcessing(true);

      // Xin quyền truy cập vào thư viện ảnh
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        showToast('Bạn cần cấp quyền truy cập thư viện ảnh.', 'error');
        setIsProcessing(false);
        return;
      }

      // Mở thư viện ảnh để chọn
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: true,
        quality: 0.8,
        aspect: [4, 3],
        selectionLimit: 10,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        showToast(`Đang xử lý ${result.assets.length} ảnh...`, 'success');

        // Lấy URI của các ảnh đã chọn
        const imageUris = result.assets.map(asset => asset.uri);

        // Xử lý ảnh (tương tự như khi quét)
        const processedImages = await scanService.processImages(
          imageUris,
          true, // Bật nén
        );

        if (processedImages.length > 0) {
          if (!currentSession) {
            // Tạo phiên mới nếu chưa có
            addScanSession(processedImages);
            showToast(
              `Đã thêm ${processedImages.length} ảnh vào phiên mới.`,
              'success',
            );
          } else {
            // Thêm vào phiên hiện tại
            updateCurrentSessionImages(processedImages);
            showToast(
              `Đã thêm ${processedImages.length} ảnh vào phiên hiện tại.`,
              'success',
            );
          }
        } else {
          showToast('Không thể xử lý ảnh đã chọn.', 'error');
        }
      }
    } catch (error) {
      console.error('Lỗi khi chọn ảnh từ thư viện:', error);
      showToast('Không thể tải ảnh từ thư viện.', 'error');
    } finally {
      setIsProcessing(false);
    }
  }, [
    currentSession,
    addScanSession,
    updateCurrentSessionImages,
    scanService,
    showToast,
  ]);

  /**
   * Bắt đầu quét tài liệu mới với cải tiến xử lý
   */
  const startScan = useCallback(async () => {
    try {
      setIsProcessing(true);

      // Kiểm tra bộ nhớ trước khi bắt đầu quét
      const memoryStatus = await storageManager.ensureStorageAvailable();
      if (!memoryStatus) {
        showToast(
          'Bộ nhớ thiết bị không đủ. Hãy dọn dẹp bộ nhớ và thử lại.',
          'error',
        );
        setIsProcessing(false);
        return;
      }

      // Bắt đầu quét với DocumentScanner
      const result: ScanDocumentResponse | undefined =
        await DocumentScanner.scanDocument({
          maxNumDocuments: 5, // Giới hạn số lượng trang quét
          // @ts-ignore
          quality: 'high', // Chất lượng quét cao
          letUserAdjustCrop: true, // Cho phép người dùng chỉnh sửa vùng cắt
        });

      if (
        result?.status === 'success' &&
        Array.isArray(result.scannedImages) &&
        result.scannedImages.length > 0
      ) {
        // Xử lý song song với scanService đã cải tiến
        const processingStart = Date.now();

        // Tạo progress notification
        showToast(
          `Đang xử lý ${result.scannedImages.length} ảnh...`,
          'success',
        );

        // Xử lý ảnh song song với nén hiệu quả
        const processedImages = await scanService.processImages(
          result.scannedImages,
          true, // Bật nén
        );

        console.log(
          `Xử lý ảnh hoàn tất trong ${Date.now() - processingStart}ms`,
        );
        console.log('Số lượng ảnh sau khi xử lý:', processedImages.length);

        if (processedImages.length > 0) {
          if (!currentSession) {
            console.log(
              'Tạo phiên quét mới với',
              processedImages.length,
              'ảnh',
            );
            addScanSession(processedImages);
          } else {
            console.log(
              'Cập nhật phiên hiện tại với',
              processedImages.length,
              'ảnh mới',
            );
            updateCurrentSessionImages(processedImages);
          }

          showToast(
            `Đã quét và tối ưu ${processedImages.length} ảnh.`,
            'success',
          );
        } else {
          showToast('Không thể xử lý ảnh đã quét.', 'error');
        }
      } else {
        // Người dùng có thể đã hủy hoặc xảy ra lỗi
        console.log('Scan result:', result);
      }
    } catch (error) {
      console.error('Lỗi quét ảnh:', error);
      showToast('Không thể quét tài liệu.', 'error');
    } finally {
      setIsProcessing(false);
    }
  }, [
    currentSession,
    addScanSession,
    scanService,
    showToast,
    storageManager,
    updateCurrentSessionImages,
  ]);

  /**
   * Chia sẻ nhiều ảnh
   */
  const handleShareMultiple = useCallback(
    async (selectedUris: string[]) => {
      if (!selectedUris.length) {
        return showToast('Bạn chưa chọn ảnh nào để chia sẻ.', 'error');
      }

      try {
        setIsProcessing(true);

        // Cập nhật thời gian truy cập
        await Promise.all(
          selectedUris.map(uri => storageManager.updateLastAccessed(uri)),
        );

        // Share files
        await Share.open({
          urls: selectedUris,
          type: 'image/jpeg',
          failOnCancel: false,
        });

        setIsProcessing(false);
      } catch (error) {
        console.error('Error sharing:', error);
        setIsProcessing(false);
        showToast('Không thể chia sẻ ảnh.', 'error');
      }
    },
    [showToast, storageManager],
  );

  /**
   * Lưu ảnh vào thư viện với cải tiến
   */
  const handleSave = useCallback(
    async (selectedUris: string[]) => {
      if (!selectedUris.length) {
        return showToast('Bạn chưa chọn ảnh nào để lưu.', 'error');
      }

      try {
        setIsProcessing(true);

        // Yêu cầu quyền truy cập thư viện
        const { status } = await MediaLibrary.requestPermissionsAsync();
        if (status !== 'granted') {
          setIsProcessing(false);
          return showToast('Bạn cần cấp quyền lưu ảnh.', 'error');
        }

        // Cập nhật thời gian truy cập
        await Promise.all(
          selectedUris.map(uri => storageManager.updateLastAccessed(uri)),
        );

        // Lưu ảnh vào thư viện theo batch để tối ưu hiệu suất
        const batchSize = 3;
        let savedCount = 0;

        for (let i = 0; i < selectedUris.length; i += batchSize) {
          const batch = selectedUris.slice(i, i + batchSize);

          const assets = await Promise.all(
            batch.map(uri => MediaLibrary.createAssetAsync(uri)),
          );

          savedCount += assets.length;

          // Hiển thị progress
          if (i + batchSize < selectedUris.length) {
            showToast(
              `Đã lưu ${savedCount}/${selectedUris.length} ảnh...`,
              'success',
            );
          }

          // Tạo album nếu chưa tồn tại
          const album = await MediaLibrary.getAlbumAsync('Scanned Documents');
          if (album === null && assets.length > 0) {
            await MediaLibrary.createAlbumAsync(
              'Scanned Documents',
              assets[0],
              false,
            );
          } else if (album && assets.length > 0) {
            await MediaLibrary.addAssetsToAlbumAsync(assets, album, false);
          }
        }

        showToast(`Đã lưu ${selectedUris.length} ảnh vào thư viện.`, 'success');
        setIsProcessing(false);
      } catch (error) {
        console.error('Error saving to library:', error);
        setIsProcessing(false);
        showToast('Không thể lưu ảnh vào thư viện.', 'error');
      }
    },
    [showToast, storageManager],
  );

  /**
   * Xóa ảnh đã chọn khỏi phiên hiện tại
   */
  const handleDelete = useCallback(
    async (indexes: number[]) => {
      if (!currentSession) return;
      try {
        // Gọi hàm xóa ảnh từ context
        removeImagesFromCurrentSession(indexes);
        return true;
      } catch (error) {
        console.error('Error deleting images:', error);
        throw error;
      }
    },
    [currentSession, removeImagesFromCurrentSession],
  );

  const toggleSelect = useCallback((index: number) => {
    setSelected(prev => {
      const newSet = new Set(prev);
      newSet.has(index) ? newSet.delete(index) : newSet.add(index);
      return newSet;
    });
  }, []);

  return {
    selected,
    setSelected,
    isProcessing,
    startScan,
    handleShareMultiple,
    handleSave,
    handleDelete,
    toggleSelect,
    pickImagesFromLibrary,
  };
};
