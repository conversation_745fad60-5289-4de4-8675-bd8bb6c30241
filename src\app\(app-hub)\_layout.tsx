import { Session } from '@features/auth/context/ctx';
import { Redirect, Stack } from 'expo-router';
import { useSession } from '@features/auth/context/ctx';
import { Text } from 'react-native-ui-lib';
import { Platform } from 'react-native';

export default function AppHubLayout() {
  const { isLoading, session }: Session = useSession();

  // Kiểm tra xác thực, chỉ cho phép người dùng đã đăng nhập truy cập
  if (isLoading) {
    return <Text>Loading...</Text>; 
  }

  if (!session) {
    return <Redirect href='/(auth)/sign-in' />;
  }

  return (
    <Stack
      screenOptions={{
        headerShown: false,
        animation: Platform.OS === 'ios' ? 'default' : 'fade',
        animationDuration: 200,
        contentStyle: { backgroundColor: '#111' },
        gestureEnabled: true,
        gestureDirection: 'horizontal',
        fullScreenGestureEnabled: true,
      }}
    >
      <Stack.Screen
        name='(tabs)'
        options={{
          gestureEnabled: false,
        }}
      />
      <Stack.Screen
        name='app-details/[id]/index'
        options={{
          headerTitle: 'Chi tiết ứng dụng',
          headerShown: false,
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name='settings/index'
        options={{
          headerTitle: 'Cài đặt App Hub',
          headerShown: true,
          animation: 'slide_from_right',
        }}
      />
    </Stack>
  );
} 